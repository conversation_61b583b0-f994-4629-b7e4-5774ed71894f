import{_ as z,c as l,d as g,t as _,q as N,r as h,o as G,m as R,s as r,x as H,e as n,g as L,n as C,f as q,v as W,i as M,F as V,p as K,y as X}from"./index-a2cd3c28.js";const J={name:"CryptoIcon",props:{symbol:{type:String,required:!0}},data(){return{useFallbackIcon:!1}},watch:{symbol(){this.useFallbackIcon=!1}},computed:{cleanSymbol(){return this.symbol.replace("USDT","").toLowerCase()},iconUrl(){if(this.useFallbackIcon)return null;const c=this.getCustomIconUrl(this.cleanSymbol);return c||(this.useFallbackIcon=!0,null)}},methods:{handleImageError(){this.useFallbackIcon=!0},getSymbolFirstChar(){return this.cleanSymbol.charAt(0).toUpperCase()},getCustomIconUrl(c){return{btc:"https://assets.coingecko.com/coins/images/1/large/bitcoin.png",eth:"https://assets.coingecko.com/coins/images/279/large/ethereum.png",usdt:"https://assets.coingecko.com/coins/images/325/large/Tether.png",bnb:"https://assets.coingecko.com/coins/images/825/large/bnb-icon2_2x.png",sol:"https://assets.coingecko.com/coins/images/4128/large/solana.png",xrp:"https://assets.coingecko.com/coins/images/44/large/xrp-symbol-white-128.png",ada:"https://assets.coingecko.com/coins/images/975/large/cardano.png",doge:"https://assets.coingecko.com/coins/images/5/large/dogecoin.png",moodeng:"https://assets.coingecko.com/coins/images/33311/large/moodeng.jpg",wld:"https://assets.coingecko.com/coins/images/31069/large/worldcoin.jpeg",pi:"https://assets.coingecko.com/coins/images/28362/large/pi_network.png",trump:"https://assets.coingecko.com/coins/images/31252/large/trump.jpg",pepe:"https://assets.coingecko.com/coins/images/29850/large/pepe-token.jpeg",sui:"https://assets.coingecko.com/coins/images/26375/large/sui_asset.jpeg",shib:"https://assets.coingecko.com/coins/images/11939/large/shiba.png",ltc:"https://assets.coingecko.com/coins/images/2/large/litecoin.png",dot:"https://assets.coingecko.com/coins/images/12171/large/polkadot.png",avax:"https://assets.coingecko.com/coins/images/12559/large/Avalanche_Circle_RedWhite_Trans.png",matic:"https://assets.coingecko.com/coins/images/4713/large/matic-token-icon.png",link:"https://assets.coingecko.com/coins/images/877/large/chainlink-new-logo.png",atom:"https://assets.coingecko.com/coins/images/1481/large/cosmos_hub.png",uni:"https://assets.coingecko.com/coins/images/12504/large/uni.jpg",etc:"https://assets.coingecko.com/coins/images/453/large/ethereum-classic-logo.png",fil:"https://assets.coingecko.com/coins/images/12817/large/filecoin.png",trx:"https://assets.coingecko.com/coins/images/1094/large/tron-logo.png",near:"https://assets.coingecko.com/coins/images/10365/large/near.jpg",vet:"https://assets.coingecko.com/coins/images/1167/large/VET_Token_Icon.png",algo:"https://assets.coingecko.com/coins/images/4380/large/download.png",icp:"https://assets.coingecko.com/coins/images/14495/large/Internet_Computer_logo.png",xmr:"https://assets.coingecko.com/coins/images/69/large/monero_logo.png",xlm:"https://assets.coingecko.com/coins/images/100/large/Stellar_symbol_black_RGB.png",hbar:"https://assets.coingecko.com/coins/images/3688/large/hbar.png",egld:"https://assets.coingecko.com/coins/images/12335/large/egld-token-logo.png",axs:"https://assets.coingecko.com/coins/images/13029/large/axie_infinity_logo.png",sand:"https://assets.coingecko.com/coins/images/12129/large/sandbox_logo.jpg",mana:"https://assets.coingecko.com/coins/images/878/large/decentraland-mana.png",gala:"https://assets.coingecko.com/coins/images/12493/large/GALA-COINGECKO.png",ftm:"https://assets.coingecko.com/coins/images/4001/large/Fantom_round.png",neo:"https://assets.coingecko.com/coins/images/480/large/NEO_512_512.png",bch:"https://assets.coingecko.com/coins/images/780/large/bitcoin-cash-circle.png",xtz:"https://assets.coingecko.com/coins/images/976/large/Tezos-logo.png",aave:"https://assets.coingecko.com/coins/images/12645/large/AAVE.png",mkr:"https://assets.coingecko.com/coins/images/1364/large/Mark_Maker.png",rune:"https://assets.coingecko.com/coins/images/9668/large/THORChain_Icon_Rounded.png",comp:"https://assets.coingecko.com/coins/images/10775/large/COMP.png",waves:"https://assets.coingecko.com/coins/images/425/large/waves.png",dash:"https://assets.coingecko.com/coins/images/19/large/dash-logo.png",zec:"https://assets.coingecko.com/coins/images/486/large/circle-zcash-color.png",eos:"https://assets.coingecko.com/coins/images/738/large/eos-eos-logo.png",snx:"https://assets.coingecko.com/coins/images/3406/large/SNX.png",crv:"https://assets.coingecko.com/coins/images/12124/large/Curve.png"}[c]||null},getIconBackground(){if(!this.useFallbackIcon)return"transparent";const c=["#F7931A","#627EEA","#2775CA","#8DC351","#345D9D","#E9C01E","#26A17B","#FF9900","#2A5ADA","#E6007A"],o={btc:"#F7931A",eth:"#627EEA",usdt:"#26A17B",bnb:"#F3BA2F",sol:"#14F195",xrp:"#23292F",ada:"#0033AD",doge:"#C2A633",dot:"#E6007A",moodeng:"#FF6B00",wld:"#3D96FF",pi:"#6C19FF",trump:"#E50000",pepe:"#00A300",sui:"#4DA1FF",trx:"#FF0013",ltc:"#345D9D",link:"#2A5ADA",uni:"#FF007A",shib:"#FFA409",avax:"#E84142",matic:"#8247E5",atom:"#2E3148",etc:"#328332",fil:"#0090FF",near:"#000000",vet:"#15BDFF",algo:"#000000",icp:"#3B00B9",xmr:"#FF6600",xlm:"#000000",hbar:"#222222",egld:"#1D45DD",axs:"#0055D5",sand:"#00ADEF",mana:"#FF2D55",gala:"#00D1FF",ftm:"#1969FF",neo:"#00E599",bch:"#8DC351",xtz:"#A6E000",aave:"#B6509E",mkr:"#1AAB9B",rune:"#3FBADF",comp:"#00D395",waves:"#0155FF",dash:"#008CE7",zec:"#ECB244",eos:"#000000",snx:"#00D1FF",crv:"#FF1E00"};if(o[this.cleanSymbol])return o[this.cleanSymbol];const f=this.cleanSymbol.split("").reduce((s,p)=>s+p.charCodeAt(0),0);return c[f%c.length]}}},Q=["src","alt"],Y={key:1,class:"crypto-fallback"};function Z(c,o,f,s,p,m){return l(),g("div",{class:"crypto-icon",style:N({backgroundColor:m.getIconBackground()})},[p.useFallbackIcon?(l(),g("span",Y,_(m.getSymbolFirstChar()),1)):(l(),g("img",{key:0,src:m.iconUrl,alt:f.symbol,class:"crypto-img",onError:o[0]||(o[0]=(...k)=>m.handleImageError&&m.handleImageError(...k)),ref:"iconImage"},null,40,Q))],4)}const $=z(J,[["render",Z],["__scopeId","data-v-0d80d195"]]);const ee={name:"MarketView",components:{CryptoIcon:$},setup(){const c=h([]),o=h([]),f=h(""),s=h(!0),p=h(null),m=h(!1),k=h(null),a=h({}),b=h({}),x=t=>t.replace("USDT",""),D=t=>{const e=parseFloat(t);return e>=1e4?e.toLocaleString("en-US",{minimumFractionDigits:1,maximumFractionDigits:1}):e>=100?e.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}):e>=1?e.toFixed(2):e>=.1||e>0?e.toFixed(4):"0.0000"},S=t=>{const e=parseFloat(t)*7.2;return e>=1e4?e.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}):e>=100?e.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}):(e>=10||e>=1,e.toFixed(2))},w=t=>{const e=parseFloat(t),i=e>=0?"+":"";return Math.abs(e)>=1e3?`${i}${Math.round(e)}%`:Math.abs(e)>=100?`${i}${e.toFixed(0)}%`:`${i}${e.toFixed(2)}%`},U=t=>parseFloat(t)>=0?"price-up":"price-down",A=(t,e)=>{if(!t||t==="0"){const d={BTC:"10x",ETH:"5x",MOODENG:"3x",SOL:"1x",WLD:"1x",PI:"1x",DOGE:"1x",TRUMP:"1x",PEPE:"1x",SUI:"1x"},u=e.replace("USDT","");if(d[u])return d[u];const v=["1x","3x","5x","10x"],F=u.split("").reduce((P,I)=>P+I.charCodeAt(0),0);return v[F%v.length]}const i=parseFloat(t);return i>=1e9?"10x":i>=5e8?"5x":i>=1e8?"3x":"1x"},E=t=>{const e={};t.forEach(i=>{var I,B;const d=i.symbol,u=parseFloat(i.lastPrice),v=parseFloat(i.priceChangePercent),F=(I=b.value[d])==null?void 0:I.price,P=(B=b.value[d])==null?void 0:B.change;F!==void 0&&u!==F&&(e[d]={updated:!0,priceDirection:u>F?"up":"down",direction:v>P?"up":"down"}),b.value[d]={price:u,change:v}}),a.value=e,setTimeout(()=>{a.value={}},2e3)},y=()=>{if(!f.value){o.value=c.value;return}o.value=c.value.filter(t=>t.symbol&&typeof t.symbol=="string"&&x(t.symbol).toLowerCase().includes(f.value.toLowerCase()))},T=async()=>{try{s.value=!0;const t=r.getPrices();if(t&&t.length>0)console.log("使用缓存的价格数据:",t.length),c.value=t,y(),s.value=!1,p.value=r.getLastUpdated();else{console.log("缓存为空，等待价格缓存服务获取数据...");const e=i=>{console.log("收到价格缓存更新:",i.length),c.value=i,y(),s.value=!1,r.removeUpdateCallback(e)};r.addUpdateCallback(e),setTimeout(()=>{s.value&&(console.log("等待超时，使用模拟数据"),s.value=!1,c.value.length===0&&(c.value=r.getPrices(),y()),r.removeUpdateCallback(e))},1e4)}}catch(t){console.error("加载市场数据时出错:",t),s.value=!1,c.value.length===0&&(c.value=r.getPrices(),y())}},O=t=>{if(!t)return"";const i=new Date-t;if(i<6e4)return"刚刚";if(i<36e5)return`${Math.floor(i/6e4)}分钟前`;const d=t.getHours().toString().padStart(2,"0"),u=t.getMinutes().toString().padStart(2,"0"),v=t.getSeconds().toString().padStart(2,"0");return`${d}:${u}:${v}`},j=()=>{const t=e=>{console.log("收到价格缓存更新:",e.length),E(e),m.value=!0,setTimeout(()=>{c.value=e,y(),p.value=r.getLastUpdated(),m.value=!1},500)};k.value=t,r.addUpdateCallback(t),p.value=r.getLastUpdated()};return G(()=>{T(),j()}),R(()=>{console.log("组件卸载，清理资源"),k.value&&(r.removeUpdateCallback(k.value),k.value=null)}),{cryptos:c,filteredCryptos:o,searchTerm:f,loading:s,lastUpdated:p,isUpdating:m,priceChanges:a,formatSymbol:x,formatPrice:D,formatCnyPrice:S,formatPriceChange:w,formatLastUpdated:O,getPriceChangeClass:U,getVolumeMultiplier:A,filterCryptos:y,retryConnection:()=>{console.log("重试连接"),s.value=!0,c.value=[],o.value=[],r.fetchPrices(),T()}}}},se={class:"market-view"},oe={class:"market-header"},te={class:"update-info"},ne={key:0,class:"last-updated"},ae={class:"search-container"},ce={class:"crypto-list"},ie={key:0,class:"loading-spinner"},re={key:1,class:"no-results"},le={key:2,class:"no-results"},ge={class:"crypto-left"},me={class:"crypto-info"},pe={class:"crypto-name"},de={class:"crypto-change"};function ue(c,o,f,s,p,m){const k=H("crypto-icon");return l(),g("div",se,[n("div",oe,[o[3]||(o[3]=n("div",null,"市场行情",-1)),n("div",te,[s.lastUpdated?(l(),g("div",ne," 最后更新: "+_(s.formatLastUpdated(s.lastUpdated)),1)):L("",!0),n("div",{class:C(["update-indicator",{updating:s.isUpdating}])},[n("i",{class:C(["fas fa-sync-alt",{spinning:s.isUpdating}])},null,2),n("span",null,_(s.isUpdating?"更新中...":"实时更新"),1)],2)])]),n("div",ae,[o[4]||(o[4]=n("i",{class:"fas fa-search search-icon"},null,-1)),q(n("input",{type:"text",class:"search-input",placeholder:"搜索币种...","onUpdate:modelValue":o[0]||(o[0]=a=>s.searchTerm=a),onInput:o[1]||(o[1]=(...a)=>s.filterCryptos&&s.filterCryptos(...a))},null,544),[[W,s.searchTerm]])]),n("div",ce,[s.loading?(l(),g("div",ie,o[5]||(o[5]=[n("div",{class:"spinner"},null,-1),n("div",{style:{"margin-left":"10px"}},"正在获取最新行情...",-1)]))):s.filteredCryptos.length===0&&s.searchTerm?(l(),g("div",re," 没有找到匹配的币种 ")):s.filteredCryptos.length===0?(l(),g("div",le,[o[7]||(o[7]=n("div",{class:"spinner"},null,-1)),o[8]||(o[8]=n("div",{style:{"margin-top":"15px"}},"无法连接到服务器，正在使用模拟数据...",-1)),o[9]||(o[9]=n("div",{style:{"margin-top":"10px","font-size":"13px",opacity:"0.7"}}," 如果问题持续，请检查网络连接或联系管理员 ",-1)),n("button",{class:"retry-button",onClick:o[2]||(o[2]=(...a)=>s.retryConnection&&s.retryConnection(...a))},o[6]||(o[6]=[n("i",{class:"fas fa-sync-alt"},null,-1),M(" 重试连接 ")]))])):(l(),g(V,{key:3},[o[11]||(o[11]=n("div",{class:"table-header"},[n("div",{class:"header-coin"},"币种"),n("div",{class:"header-price"},"价格"),n("div",{class:"header-change"},"24h涨幅")],-1)),(l(!0),g(V,null,K(s.filteredCryptos,a=>{var b,x,D,S,w,U,A,E;return l(),g("div",{class:C(["crypto-item",{"price-updated":(b=s.priceChanges[a.symbol])==null?void 0:b.updated,"price-up-flash":((x=s.priceChanges[a.symbol])==null?void 0:x.direction)==="up","price-down-flash":((D=s.priceChanges[a.symbol])==null?void 0:D.direction)==="down"}]),key:a.symbol},[n("div",ge,[X(k,{symbol:a.symbol},null,8,["symbol"]),n("div",me,[n("div",pe,_(s.formatSymbol(a.symbol)),1),o[10]||(o[10]=n("div",{class:"crypto-pair"},"/USDT",-1))])]),n("div",{class:C(["crypto-price",{"price-flash-up":((S=s.priceChanges[a.symbol])==null?void 0:S.priceDirection)==="up","price-flash-down":((w=s.priceChanges[a.symbol])==null?void 0:w.priceDirection)==="down"}])},[M(_(s.formatPrice(a.lastPrice))+" ",1),(U=s.priceChanges[a.symbol])!=null&&U.priceDirection?(l(),g("i",{key:0,class:C(["price-arrow",((A=s.priceChanges[a.symbol])==null?void 0:A.priceDirection)==="up"?"fas fa-arrow-up":"fas fa-arrow-down",((E=s.priceChanges[a.symbol])==null?void 0:E.priceDirection)==="up"?"arrow-up":"arrow-down"])},null,2)):L("",!0)],2),n("div",de,[n("div",{class:C(["price-change",s.getPriceChangeClass(a.priceChangePercent)])},_(s.formatPriceChange(a.priceChangePercent)),3)])],2)}),128))],64))])])}const ke=z(ee,[["render",ue],["__scopeId","data-v-45cf8c03"]]);export{ke as default};
