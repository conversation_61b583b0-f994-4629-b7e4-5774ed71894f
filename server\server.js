// 加载环境变量
require('dotenv').config();

// 强制使用MongoDB存储策略数据
process.env.USE_MONGODB = 'true';

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const axios = require('axios');
const jwt = require('jsonwebtoken');
const Binance = require('node-binance-api');
const mongoose = require('mongoose');
const Strategy = require('./models/Strategy');
const okxService = require('./services/okxService');
const binanceService = require('./services/binanceService');
const pythonTradeService = require('./services/pythonTradeService');
const binancePythonTradeService = require('./services/binancePythonTradeService');
const strategyMonitorService = require('./services/strategyMonitorService');
const binanceStrategyMonitorService = require('./services/binanceStrategyMonitorService');
const backtestService = require('./services/backtestService');
const { router: authRouter, authenticateToken } = require('./routes/auth');
const { router: inviteRouter } = require('./routes/invite');
const { router: earningsRouter } = require('./routes/earnings');
const rankingRouter = require('./routes/ranking');
const membershipRouter = require('./routes/membership');
const userRouter = require('./routes/user');

// 币安API密钥存储路径
const fs = require('fs');
const path = require('path');
const DATA_DIR = path.join(__dirname, 'data');
const BINANCE_API_KEYS_FILE = path.join(DATA_DIR, 'binance_api_keys.json');

// 确保数据目录存在
if (!fs.existsSync(DATA_DIR)) {
  fs.mkdirSync(DATA_DIR, { recursive: true });
}

// 如果币安API密钥文件不存在，创建一个空的JSON文件
if (!fs.existsSync(BINANCE_API_KEYS_FILE)) {
  fs.writeFileSync(BINANCE_API_KEYS_FILE, JSON.stringify({}), 'utf8');
}

/**
 * 保存币安用户API密钥
 * @param {string} userId 用户ID
 * @param {Object} keys API密钥对象
 */
function saveBinanceApiKeys(userId, keys) {
  try {
    // 读取现有的API密钥
    let apiKeys = {};
    if (fs.existsSync(BINANCE_API_KEYS_FILE)) {
      const fileContent = fs.readFileSync(BINANCE_API_KEYS_FILE, 'utf8');
      if (fileContent.trim()) {
        apiKeys = JSON.parse(fileContent);
      }
    }

    // 更新API密钥，标注为币安
    apiKeys[userId] = {
      ...keys,
      exchange: 'binance',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // 写入文件
    fs.writeFileSync(BINANCE_API_KEYS_FILE, JSON.stringify(apiKeys, null, 2), 'utf8');
    console.log(`币安API密钥已保存到文件: ${BINANCE_API_KEYS_FILE}`);
  } catch (error) {
    console.error('保存币安API密钥到文件失败:', error);
  }
}

/**
 * 获取币安用户API密钥
 * @param {string} userId 用户ID
 * @returns {Object|null} API密钥对象
 */
function getBinanceApiKeys(userId) {
  try {
    if (fs.existsSync(BINANCE_API_KEYS_FILE)) {
      const fileContent = fs.readFileSync(BINANCE_API_KEYS_FILE, 'utf8');
      if (fileContent.trim()) {
        const apiKeys = JSON.parse(fileContent);
        const keys = apiKeys[userId];

        // 确保是币安的API密钥
        if (keys && keys.exchange === 'binance') {
          return keys;
        }
      }
    }
  } catch (error) {
    console.error('从文件读取币安API密钥失败:', error);
  }

  return null;
}

// 初始化Express应用
const app = express();
const server = http.createServer(app);

// 配置CORS - 支持更多来源包括公网访问
app.use(cors({
  origin: function (origin, callback) {
    // 允许的来源列表
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:5173',
      'http://localhost:5174',
      'http://localhost:8080',
      'http://localhost:3008',
      'http://localhost:3009',
      'http://127.0.0.1:5174',
      'http://127.0.0.1:5173'
    ];

    // 允许任何包含5173端口的来源（用于公网访问）
    if (!origin || allowedOrigins.includes(origin) || origin.includes(':5173')) {
      callback(null, true);
    } else {
      callback(null, true); // 暂时允许所有来源，生产环境需要更严格的控制
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  credentials: true
}));
app.use(express.json());

// 设置静态文件目录
app.use(express.static(path.join(__dirname, 'public')));

// 添加测试页面路由
app.get('/test-okx', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'test-okx.html'));
});

// 添加Python帮助页面路由
app.get('/python-help', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'python-help.html'));
});

// 初始化Socket.io
const io = socketIo(server, {
  cors: {
    origin: function (origin, callback) {
      // 允许的来源列表
      const allowedOrigins = [
        'http://localhost:5173',
        'http://localhost:8080',
        'http://localhost:3008',
        'http://127.0.0.1:5173'
      ];

      // 允许任何包含5173端口的来源（用于公网访问）
      if (!origin || allowedOrigins.includes(origin) || origin.includes(':5173')) {
        callback(null, true);
      } else {
        callback(null, true); // 暂时允许所有来源
      }
    },
    methods: ['GET', 'POST'],
    credentials: true
  }
});

// 初始化Binance API (仅保留兼容性，价格数据已改用Gate.io API)
const binance = new Binance();

// 导入Gate.io API服务
const gateApi = require('./services/gate-api');

// 认证路由
app.use('/api/auth', authRouter);

// 邀请路由（需要认证）
app.use('/api/invite', authenticateToken, inviteRouter);

// 收益路由（需要认证）
app.use('/api/earnings', authenticateToken, earningsRouter);

// 排行榜路由
app.use('/api/ranking', rankingRouter);

// 会员路由
app.use('/api/membership', membershipRouter);

// 用户管理路由
app.use('/api/user', userRouter);

// 策略权限路由
const strategyPermissionRouter = require('./routes/strategyPermission');
app.use('/api/strategy-permission', strategyPermissionRouter);

// K线数据路由
const klineDataRouter = require('./routes/klineData');
app.use('/api/kline-data', klineDataRouter);

// 公告路由
const announcementRouter = require('./routes/announcement');
app.use('/api/announcements', announcementRouter);

// 导入网络诊断工具
const { performHealthCheck, globalDiagnostics } = require('./utils/networkDiagnostics');
const { testDNSResolution, getDNSInfo, clearDNSCache, diagnoseDNSIssues, forceInitializeDNS } = require('./config/dnsConfig');

// 导入网络修复配置
const { fixNetworkConnections } = require('./config/networkFix');

// 路由
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', message: 'Server is running' });
});

// 网络健康检查端点
app.get('/api/network-health', async (req, res) => {
  try {
    console.log('执行网络健康检查...');
    const healthStatus = await performHealthCheck();
    res.json(healthStatus);
  } catch (error) {
    console.error('网络健康检查失败:', error);
    res.status(500).json({
      overall: 'error',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 网络诊断统计端点
app.get('/api/network-stats', (req, res) => {
  try {
    const stats = globalDiagnostics.getStats();
    const report = globalDiagnostics.generateDiagnosticReport();
    res.json({
      stats,
      report,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('获取网络统计失败:', error);
    res.status(500).json({ error: error.message });
  }
});

// DNS诊断端点
app.get('/api/dns-diagnosis', async (req, res) => {
  try {
    console.log('执行DNS诊断...');
    const diagnosis = await diagnoseDNSIssues();
    res.json(diagnosis);
  } catch (error) {
    console.error('DNS诊断失败:', error);
    res.status(500).json({
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// DNS测试端点
app.get('/api/dns-test', async (req, res) => {
  try {
    console.log('执行DNS解析测试...');
    const results = await testDNSResolution();
    res.json({
      success: true,
      results,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('DNS测试失败:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// DNS信息端点
app.get('/api/dns-info', (req, res) => {
  try {
    const info = getDNSInfo();
    res.json({
      success: true,
      info,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('获取DNS信息失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 清除DNS缓存端点
app.post('/api/dns-clear-cache', (req, res) => {
  try {
    clearDNSCache();
    res.json({
      success: true,
      message: 'DNS缓存已清除',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('清除DNS缓存失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取加密货币价格 (使用CoinGecko API按市值排序)
app.get('/api/prices', async (req, res) => {
  try {
    console.log('获取CoinGecko市值数据...');

    // 使用CoinGecko API获取按市值排序的加密货币数据
    const prices = await gateApi.getCoinGeckoMarketData(20);

    // 打印前5个币种的信息用于调试
    prices.slice(0, 5).forEach(item => {
      console.log(`币种: ${item.symbol}, 价格: $${item.lastPrice}, 市值: $${item.marketCap.toLocaleString()}, 涨跌幅: ${item.priceChangePercent}%`);
    });

    console.log(`返回 ${prices.length} 个按市值排序的币种数据`);
    res.json(prices);
  } catch (error) {
    console.error('Error fetching market cap data from CoinGecko:', error);

    // 如果CoinGecko API失败，回退到Gate.io API
    try {
      console.log('CoinGecko API失败，回退到Gate.io API...');
      const tickers = await gateApi.getTickers();
      const fallbackPrices = gateApi.formatTickerData(tickers, 20);
      console.log(`回退: 返回 ${fallbackPrices.length} 个Gate.io交易对数据`);
      res.json(fallbackPrices);
    } catch (fallbackError) {
      console.error('Gate.io API也失败:', fallbackError);
      res.status(500).json({ error: 'Failed to fetch cryptocurrency data from both CoinGecko and Gate.io' });
    }
  }
});

// OKX API验证 (需要认证)
app.post('/api/okx/validate', authenticateToken, async (req, res) => {
  try {
    console.log('收到OKX API验证请求:', {
      headers: req.headers,
      body: {
        ...req.body,
        apiKey: req.body.apiKey ? '***' : undefined,
        secretKey: req.body.secretKey ? '***' : undefined,
        passphrase: req.body.passphrase ? '***' : undefined
      }
    });
    const { apiKey, secretKey, passphrase } = req.body;
    const userId = req.user.uid; // 使用认证用户的UID

    if (!apiKey || !secretKey || !passphrase) {
      console.log('验证失败: 缺少必要参数');
      return res.status(400).json({ success: false, error: '缺少必要的API参数' });
    }

    console.log('开始验证OKX API密钥...');

    try {
      const isValid = await okxService.validateApiKeys(apiKey, secretKey, passphrase);
      console.log('验证结果:', isValid);

      if (isValid) {
        // 保存API密钥
        okxService.saveUserApiKeys(userId, { apiKey, secretKey, passphrase });
        console.log('API密钥验证成功并保存，用户UID:', userId);
        return res.json({ success: true, message: 'API密钥验证成功' });
      } else {
        console.log('API密钥验证失败');
        return res.status(401).json({
          success: false,
          error: 'API密钥验证失败，请检查API密钥、Secret Key和Passphrase是否正确，并确保API密钥具有交易权限'
        });
      }
    } catch (validationError) {
      console.error('API验证过程中出错:', validationError);

      // 检查是否是授权错误
      if (validationError.message && validationError.message.includes('401')) {
        return res.status(401).json({
          success: false,
          error: 'API密钥验证失败: 授权错误，请检查API密钥是否有效且具有足够的权限'
        });
      }

      // 检查是否是网络错误
      if (validationError.message && validationError.message.includes('ECONNREFUSED')) {
        return res.status(503).json({
          success: false,
          error: '无法连接到OKX服务器，请检查网络连接'
        });
      }

      // 其他错误
      return res.status(500).json({
        success: false,
        error: `验证过程中发生错误: ${validationError.message}`
      });
    }
  } catch (error) {
    console.error('OKX API验证错误:', error);
    return res.status(500).json({
      success: false,
      error: '验证过程中发生服务器错误，请稍后重试'
    });
  }
});

// 直接保存OKX API密钥（不进行验证，需要认证）
app.post('/api/okx/save-keys', authenticateToken, async (req, res) => {
  try {
    console.log('收到保存OKX API密钥请求:', {
      headers: req.headers,
      body: {
        ...req.body,
        apiKey: req.body.apiKey ? '***' : undefined,
        secretKey: req.body.secretKey ? '***' : undefined,
        passphrase: req.body.passphrase ? '***' : undefined
      }
    });

    const { apiKey, secretKey, passphrase } = req.body;
    const userId = req.user.uid; // 使用认证用户的UID

    if (!apiKey || !secretKey || !passphrase) {
      console.log('保存失败: 缺少必要参数');
      return res.status(400).json({ success: false, error: '缺少必要的API参数' });
    }

    // 直接保存API密钥，不进行验证
    okxService.saveUserApiKeys(userId, { apiKey, secretKey, passphrase });
    console.log('API密钥保存成功，用户UID:', userId);

    return res.json({
      success: true,
      message: 'API密钥已保存，将用于Python SDK交易'
    });
  } catch (error) {
    console.error('保存OKX API密钥错误:', error);
    return res.status(500).json({
      success: false,
      error: '保存过程中发生服务器错误，请稍后重试'
    });
  }
});

// 获取用户的OKX API密钥状态（需要认证）
app.get('/api/okx/api-keys', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.uid; // 使用认证用户的UID
    console.log('获取API密钥状态请求，用户UID:', userId);

    // 检查用户是否已配置API密钥
    const apiKeys = okxService.getUserApiKeys(userId);

    if (apiKeys && apiKeys.apiKey && apiKeys.secretKey && apiKeys.passphrase) {
      // 返回API密钥已配置的状态，但不返回实际的密钥内容
      return res.json({
        success: true,
        hasApiKeys: true,
        apiKeyPreview: apiKeys.apiKey ? `${apiKeys.apiKey.substring(0, 4)}...${apiKeys.apiKey.substring(apiKeys.apiKey.length - 4)}` : '',
        message: 'API密钥已配置'
      });
    } else {
      return res.json({
        success: true,
        hasApiKeys: false,
        message: '尚未配置API密钥'
      });
    }
  } catch (error) {
    console.error('获取API密钥状态错误:', error);
    return res.status(500).json({
      success: false,
      error: '获取API密钥状态失败'
    });
  }
});

// 删除用户的OKX API密钥（需要认证）
app.delete('/api/okx/api-keys', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.uid; // 使用认证用户的UID
    console.log('删除OKX API密钥请求，用户UID:', userId);

    // 调用服务删除API密钥
    const result = await okxService.deleteUserApiKeys(userId);

    if (result.success) {
      console.log('OKX API密钥删除成功，用户UID:', userId);
      return res.json({
        success: true,
        message: result.message
      });
    } else {
      console.error('删除OKX API密钥失败:', result.error);
      return res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('删除OKX API密钥错误:', error);
    return res.status(500).json({
      success: false,
      error: '删除过程中发生服务器错误，请稍后重试'
    });
  }
});

// ==================== 币安 API 路由 ====================

// 保存币安API密钥（需要认证）
app.post('/api/binance/save-keys', authenticateToken, async (req, res) => {
  try {
    console.log('收到保存币安API密钥请求:', {
      headers: req.headers,
      body: {
        ...req.body,
        apiKey: req.body.apiKey ? '***' : undefined,
        secretKey: req.body.secretKey ? '***' : undefined
      }
    });

    const { apiKey, secretKey } = req.body;
    const userId = req.user.uid; // 使用认证用户的UID

    if (!apiKey || !secretKey) {
      console.log('保存失败: 缺少必要参数');
      return res.status(400).json({ success: false, error: '缺少必要的API参数' });
    }

    // 保存币安API密钥到文件，标注为币安
    saveBinanceApiKeys(userId, { apiKey, secretKey });
    console.log('币安API密钥保存成功，用户UID:', userId);

    return res.json({
      success: true,
      message: '币安API密钥已保存'
    });
  } catch (error) {
    console.error('保存币安API密钥错误:', error);
    return res.status(500).json({
      success: false,
      error: '保存过程中发生服务器错误，请稍后重试'
    });
  }
});

// 获取用户的币安API密钥状态（需要认证）
app.get('/api/binance/api-keys', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.uid; // 使用认证用户的UID
    console.log('获取币安API密钥状态请求，用户UID:', userId);

    // 检查用户是否已配置币安API密钥
    const apiKeys = getBinanceApiKeys(userId);

    if (apiKeys && apiKeys.apiKey && apiKeys.secretKey) {
      // 返回API密钥已配置的状态，但不返回实际的密钥内容
      return res.json({
        success: true,
        hasApiKeys: true,
        apiKeyPreview: apiKeys.apiKey ? `${apiKeys.apiKey.substring(0, 4)}...${apiKeys.apiKey.substring(apiKeys.apiKey.length - 4)}` : '',
        message: '币安API密钥已配置'
      });
    } else {
      return res.json({
        success: true,
        hasApiKeys: false,
        message: '尚未配置币安API密钥'
      });
    }
  } catch (error) {
    console.error('获取币安API密钥状态错误:', error);
    return res.status(500).json({
      success: false,
      error: '获取币安API密钥状态失败'
    });
  }
});

// 删除用户的币安API密钥（需要认证）
app.delete('/api/binance/api-keys', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.uid; // 使用认证用户的UID
    console.log('删除币安API密钥请求，用户UID:', userId);

    // 调用服务删除API密钥
    const result = await binanceService.deleteUserApiKeys(userId);

    if (result.success) {
      console.log('币安API密钥删除成功，用户UID:', userId);
      return res.json({
        success: true,
        message: result.message
      });
    } else {
      console.error('删除币安API密钥失败:', result.error);
      return res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('删除币安API密钥错误:', error);
    return res.status(500).json({
      success: false,
      error: '删除过程中发生服务器错误，请稍后重试'
    });
  }
});

// 导入策略权限验证中间件
const { verifyStrategyPermission, verifyBatchStrategyPermission } = require('./middleware/strategyPermissionMiddleware');

// 创建币安交易策略
app.post('/api/binance/strategy', authenticateToken, verifyStrategyPermission, async (req, res) => {
  try {
    const userId = req.user.uid; // 使用认证用户的UID
    const strategyParams = req.body;
    console.log('创建币安策略请求:', req.body);

    // 验证请求参数
    if (!strategyParams.type || !strategyParams.direction || !strategyParams.amount || !strategyParams.symbol || !strategyParams.strategyTemplate) {
      console.error('请求参数不完整:', strategyParams);
      return res.status(400).json({ error: '请求参数不完整' });
    }

    // 创建策略
    console.log('调用binanceService.createStrategy...');
    const result = await binanceService.createStrategy(userId, strategyParams);
    console.log('binanceService.createStrategy返回结果:', result);

    if (result.error) {
      console.log('创建币安策略失败:', result.error);
      return res.status(400).json({ error: result.error });
    }

    console.log('创建币安策略成功');
    res.json(result);
  } catch (error) {
    console.error('创建币安策略错误:', error);
    res.status(500).json({ error: '创建币安策略失败: ' + error.message });
  }
});

// 批量创建币安交易策略
app.post('/api/binance/batch-strategies', authenticateToken, verifyBatchStrategyPermission, async (req, res) => {
  try {
    const userId = req.user.uid; // 使用认证用户的UID
    const { strategies } = req.body;
    console.log('批量创建币安策略请求:', { userId, strategiesCount: strategies?.length });

    // 验证请求参数
    if (!strategies || !Array.isArray(strategies) || strategies.length === 0) {
      console.error('请求参数不完整: 缺少策略数组或策略数组为空');
      return res.status(400).json({ error: '请求参数不完整: 缺少策略数组或策略数组为空' });
    }

    // 验证每个策略的参数
    for (const [index, strategy] of strategies.entries()) {
      if (!strategy.type || !strategy.direction || !strategy.amount || !strategy.symbol || !strategy.strategyTemplate) {
        console.error(`第 ${index + 1} 个币安策略参数不完整:`, strategy);
        return res.status(400).json({ error: `第 ${index + 1} 个币安策略参数不完整` });
      }
    }

    // 批量创建策略
    console.log('调用binanceService.createBatchStrategies...');
    const result = await binanceService.createBatchStrategies(userId, strategies);
    console.log('binanceService.createBatchStrategies返回结果:', result);

    if (result.error) {
      console.log('批量创建币安策略失败:', result.error);
      return res.status(400).json({ error: result.error });
    }

    console.log('批量创建币安策略成功');
    res.json(result);
  } catch (error) {
    console.error('批量创建币安策略错误:', error);
    res.status(500).json({ error: '批量创建币安策略失败: ' + error.message });
  }
});

// 获取币安策略列表
app.get('/api/binance/strategies', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.uid;
    console.log('获取币安策略列表请求，用户UID:', userId);

    const result = await binanceService.getStrategies(userId);

    if (result.success) {
      console.log(`返回 ${result.strategies.length} 个币安策略`);
      res.json(result);
    } else {
      console.log('获取币安策略列表失败:', result.error);
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('获取币安策略列表错误:', error);
    res.status(500).json({ error: '获取币安策略列表失败: ' + error.message });
  }
});

// 停止币安策略
app.post('/api/binance/strategies/:id/stop', authenticateToken, async (req, res) => {
  try {
    const strategyId = req.params.id;
    console.log('停止币安策略请求:', strategyId);

    const result = await binanceService.stopStrategy(strategyId);

    if (result.success) {
      console.log('停止币安策略成功');
      res.json(result);
    } else {
      console.log('停止币安策略失败:', result.error);
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('停止币安策略错误:', error);
    res.status(500).json({ error: '停止币安策略失败: ' + error.message });
  }
});

// 删除币安策略
app.delete('/api/binance/strategies/:id', authenticateToken, async (req, res) => {
  try {
    const strategyId = req.params.id;
    console.log('删除币安策略请求:', strategyId);

    const result = await binanceService.deleteStrategy(strategyId);

    if (result.success) {
      console.log('删除币安策略成功');
      res.json(result);
    } else {
      console.log('删除币安策略失败:', result.error);
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('删除币安策略错误:', error);
    res.status(500).json({ error: '删除币安策略失败: ' + error.message });
  }
});

// 批量停止币安策略
app.post('/api/binance/strategies/batch-stop', authenticateToken, async (req, res) => {
  try {
    const { strategyIds } = req.body;
    console.log('批量停止币安策略请求:', strategyIds);

    if (!strategyIds || !Array.isArray(strategyIds) || strategyIds.length === 0) {
      return res.status(400).json({ error: '策略ID列表不能为空' });
    }

    const result = await binanceService.batchStopStrategies(strategyIds);

    if (result.success) {
      console.log('批量停止币安策略成功');
      res.json(result);
    } else {
      console.log('批量停止币安策略失败:', result.error);
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('批量停止币安策略错误:', error);
    res.status(500).json({ error: '批量停止策略失败: ' + error.message });
  }
});

// 批量删除币安策略
app.post('/api/binance/strategies/batch-delete', authenticateToken, async (req, res) => {
  try {
    const { strategyIds } = req.body;
    console.log('批量删除币安策略请求:', strategyIds);

    if (!strategyIds || !Array.isArray(strategyIds) || strategyIds.length === 0) {
      return res.status(400).json({ error: '策略ID列表不能为空' });
    }

    const result = await binanceService.batchDeleteStrategies(strategyIds);

    if (result.success) {
      console.log('批量删除币安策略成功');
      res.json(result);
    } else {
      console.log('批量删除币安策略失败:', result.error);
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('批量删除币安策略错误:', error);
    res.status(500).json({ error: '批量删除策略失败: ' + error.message });
  }
});

// 币安策略一键平仓
app.post('/api/binance/strategies/:id/close-position', authenticateToken, async (req, res) => {
  try {
    const strategyId = req.params.id;
    const userId = req.user.uid;
    console.log('币安策略一键平仓请求:', { strategyId, userId });

    const result = await binanceService.closePosition(strategyId, userId);

    if (result.success) {
      console.log('币安策略平仓成功');
      res.json(result);
    } else {
      console.log('币安策略平仓失败:', result.error);
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('币安策略平仓错误:', error);
    res.status(500).json({ error: '币安策略平仓失败: ' + error.message });
  }
});

// 币安策略恢复
app.post('/api/binance/strategies/:id/recover', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.uid;
    console.log('币安策略恢复请求:', { strategyId: id, userId });

    // 检查是否使用MongoDB
    const useMongoDb = process.env.USE_MONGODB === 'true';
    let strategy = null;

    if (useMongoDb) {
      try {
        // 确保Strategy模型已注册
        let Strategy;
        try {
          Strategy = mongoose.model('Strategy');
        } catch (error) {
          console.log('Strategy模型未注册，正在加载...');
          Strategy = require('./models/Strategy');
        }

        // 从数据库获取策略
        strategy = await Strategy.findById(id);

        if (!strategy) {
          return res.status(404).json({ error: '策略不存在' });
        }

        // 验证策略所有者
        if (strategy.userId !== userId) {
          return res.status(403).json({ error: '无权限操作此策略' });
        }

        // 检查策略状态
        if (strategy.status !== 'error') {
          return res.status(400).json({ error: '只能恢复错误状态的策略' });
        }

      } catch (error) {
        console.error('MongoDB查询失败:', error);
        return res.status(500).json({ error: '数据库查询失败' });
      }
    } else {
      // 从内存中获取策略
      const strategyMonitorService = require('./services/strategyMonitorService');
      strategy = strategyMonitorService.getInMemoryStrategies().get(id);

      if (!strategy) {
        return res.status(404).json({ error: '策略不存在' });
      }

      if (strategy.userId !== userId) {
        return res.status(403).json({ error: '无权限操作此策略' });
      }

      if (strategy.status !== 'error') {
        return res.status(400).json({ error: '只能恢复错误状态的策略' });
      }
    }

    // 导入策略监控服务
    const strategyMonitorService = require('./services/strategyMonitorService');

    // 恢复策略状态
    console.log(`恢复策略 ${id}，当前状态: ${strategy.status}`);

    if (useMongoDb) {
      // 更新数据库中的策略状态
      try {
        await strategy.updateStatus('waiting');
        await strategy.addLog('用户手动恢复策略，重新启动监控');
        console.log(`策略 ${id} 状态已更新为waiting`);
      } catch (updateError) {
        console.error(`更新策略状态失败: ${updateError.message}`);
        return res.status(500).json({ error: '恢复策略失败: ' + updateError.message });
      }
    } else {
      // 更新内存中的策略状态
      strategy.status = 'waiting';
      if (!strategy.logs) strategy.logs = [];
      strategy.logs.push({
        message: '用户手动恢复策略，重新启动监控',
        timestamp: new Date()
      });
      strategyMonitorService.getInMemoryStrategies().set(id, strategy);
      console.log(`策略 ${id} 状态已更新为waiting`);
    }

    // 重新启动策略监控
    try {
      await strategyMonitorService.startStrategyMonitor(strategy);
      console.log(`策略 ${id} 监控已重新启动`);
    } catch (monitorError) {
      console.error(`重新启动策略监控失败: ${monitorError.message}`);
      // 如果启动监控失败，将状态改回error
      if (useMongoDb) {
        await strategy.updateStatus('error');
        await strategy.addLog(`恢复失败: ${monitorError.message}`);
      } else {
        strategy.status = 'error';
        strategy.logs.push({
          message: `恢复失败: ${monitorError.message}`,
          timestamp: new Date()
        });
        strategyMonitorService.getInMemoryStrategies().set(id, strategy);
      }
      return res.status(500).json({ error: '重新启动监控失败: ' + monitorError.message });
    }

    console.log('恢复策略监控成功');
    res.json({
      success: true,
      message: '策略已恢复，监控重新启动',
      strategy: {
        id: strategy._id || strategy.id,
        status: strategy.status,
        strategyName: strategy.strategyName
      }
    });
  } catch (error) {
    console.error('恢复策略监控错误:', error);
    res.status(500).json({ error: '恢复策略监控失败: ' + error.message });
  }
});

// 获取币安历史订单
app.get('/api/binance/orders', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.uid;
    const { symbol, limit = 20, page = 1 } = req.query;
    console.log('获取币安历史订单请求:', { userId, symbol, limit, page });

    const result = await binanceService.getOrderHistory(userId, { symbol, limit: parseInt(limit), page: parseInt(page) });

    if (result.success) {
      console.log(`返回 ${result.orders.length} 个币安历史订单`);
      res.json(result);
    } else {
      console.log('获取币安历史订单失败:', result.error);
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('获取币安历史订单错误:', error);
    res.status(500).json({ error: '获取币安历史订单失败: ' + error.message });
  }
});

// 获取币安账户信息（需要认证）
app.get('/api/binance/account', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.uid; // 使用认证用户的UID
    console.log('获取币安账户信息请求，用户UID:', userId);

    // 检查用户是否已配置币安API密钥
    const apiKeys = getBinanceApiKeys(userId);
    if (!apiKeys || !apiKeys.apiKey || !apiKeys.secretKey) {
      console.log('币安API密钥未配置');
      return res.status(400).json({
        success: false,
        error: '请先配置币安API密钥'
      });
    }

    // 调用Python脚本获取币安账户信息
    console.log('调用Python脚本获取币安账户余额，用户ID:', userId);
    const result = await binancePythonTradeService.getBinanceAccountBalance(userId);

    if (result.error) {
      console.error('获取币安账户信息失败:', result.error);
      return res.status(400).json({
        success: false,
        error: result.error
      });
    }

    if (result.success && result.balance) {
      // 格式化账户信息以匹配前端期望的格式
      const accountInfo = {
        balances: result.balance,
        canTrade: true,  // 假设有交易权限，实际应该从API获取
        canWithdraw: true, // 假设有提现权限，实际应该从API获取
        canDeposit: true   // 假设有充值权限，实际应该从API获取
      };

      console.log('币安账户信息获取成功');
      return res.json({
        success: true,
        account: accountInfo
      });
    } else {
      console.error('币安账户信息格式异常:', result);
      return res.status(400).json({
        success: false,
        error: '获取账户信息失败'
      });
    }
  } catch (error) {
    console.error('获取币安账户信息错误:', error);
    res.status(500).json({
      success: false,
      error: '获取账户信息失败: ' + (error.message || '未知错误')
    });
  }
});

// 获取OKX账户持仓信息
app.get('/api/okx/account', async (req, res) => {
  try {
    const userId = req.query.userId || 'default';
    console.log('🔄 获取真实OKX账户信息请求:', userId);

    // 检查API密钥是否已配置
    const userApiKeys = okxService.getUserApiKeys(userId);
    if (!userApiKeys) {
      console.error('❌ API密钥未配置');
      return res.status(400).json({
        error: '请先配置有效的OKX API密钥才能访问真实交易功能'
      });
    }

    const result = await okxService.getAccountPositions(userId);

    if (result.error) {
      console.error('❌ 获取账户信息失败:', result.error);
      return res.status(400).json({ error: result.error });
    }

    console.log('✅ 获取真实账户信息成功');
    res.json(result);
  } catch (error) {
    console.error('❌ 获取OKX账户信息错误:', error);
    res.status(500).json({ error: '获取账户信息失败: ' + error.message });
  }
});

// 创建OKX交易策略
app.post('/api/okx/strategy', authenticateToken, verifyStrategyPermission, async (req, res) => {
  try {
    const userId = req.user.uid; // 使用认证用户的UID
    const strategyParams = req.body;
    console.log('创建OKX策略请求:', req.body);

    // 验证请求参数
    if (!strategyParams.type || !strategyParams.direction || !strategyParams.amount || !strategyParams.symbol || !strategyParams.strategyTemplate) {
      console.error('请求参数不完整:', strategyParams);
      return res.status(400).json({ error: '请求参数不完整' });
    }

    // 创建策略
    console.log('调用okxService.createStrategy...');
    const result = await okxService.createStrategy(userId, strategyParams);
    console.log('okxService.createStrategy返回结果:', result);

    if (result.error) {
      console.log('创建策略失败:', result.error);
      return res.status(400).json({ error: result.error });
    }

    console.log('创建策略成功');
    res.json(result);
  } catch (error) {
    console.error('创建OKX策略错误:', error);
    res.status(500).json({ error: '创建策略失败: ' + error.message });
  }
});

// 批量创建OKX交易策略
app.post('/api/okx/batch-strategies', authenticateToken, verifyBatchStrategyPermission, async (req, res) => {
  try {
    const userId = req.user.uid; // 使用认证用户的UID
    const { strategies } = req.body;
    console.log('批量创建OKX策略请求:', { userId, strategiesCount: strategies?.length });

    // 验证请求参数
    if (!strategies || !Array.isArray(strategies) || strategies.length === 0) {
      console.error('请求参数不完整: 缺少策略数组或策略数组为空');
      return res.status(400).json({ error: '请求参数不完整: 缺少策略数组或策略数组为空' });
    }

    // 验证每个策略的参数
    for (const [index, strategy] of strategies.entries()) {
      if (!strategy.type || !strategy.direction || !strategy.amount || !strategy.symbol || !strategy.strategyTemplate) {
        console.error(`第 ${index + 1} 个策略参数不完整:`, strategy);
        return res.status(400).json({ error: `第 ${index + 1} 个策略参数不完整` });
      }
    }

    // 批量创建策略
    console.log('调用okxService.createBatchStrategies...');
    const result = await okxService.createBatchStrategies(userId, strategies);
    console.log('okxService.createBatchStrategies返回结果:', result);

    if (result.error) {
      console.log('批量创建策略失败:', result.error);
      return res.status(400).json({ error: result.error });
    }

    console.log('批量创建策略成功');
    res.json(result);
  } catch (error) {
    console.error('批量创建OKX策略错误:', error);
    res.status(500).json({ error: '批量创建策略失败: ' + error.message });
  }
});

// 批量停止OKX策略
app.post('/api/okx/strategies/batch-stop', authenticateToken, async (req, res) => {
  try {
    const { strategyIds } = req.body;
    console.log('批量停止OKX策略请求:', strategyIds);

    if (!strategyIds || !Array.isArray(strategyIds) || strategyIds.length === 0) {
      return res.status(400).json({ error: '策略ID列表不能为空' });
    }

    const result = await okxService.batchStopStrategies(strategyIds);

    if (result.success) {
      console.log('批量停止OKX策略成功');
      res.json(result);
    } else {
      console.log('批量停止OKX策略失败:', result.error);
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('批量停止OKX策略错误:', error);
    res.status(500).json({ error: '批量停止策略失败: ' + error.message });
  }
});

// 批量删除OKX策略
app.post('/api/okx/strategies/batch-delete', authenticateToken, async (req, res) => {
  try {
    const { strategyIds } = req.body;
    console.log('批量删除OKX策略请求:', strategyIds);

    if (!strategyIds || !Array.isArray(strategyIds) || strategyIds.length === 0) {
      return res.status(400).json({ error: '策略ID列表不能为空' });
    }

    const result = await okxService.batchDeleteStrategies(strategyIds);

    if (result.success) {
      console.log('批量删除OKX策略成功');
      res.json(result);
    } else {
      console.log('批量删除OKX策略失败:', result.error);
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('批量删除OKX策略错误:', error);
    res.status(500).json({ error: '批量删除策略失败: ' + error.message });
  }
});

// 检查Python环境
app.get('/api/python/check-env', async (req, res) => {
  try {
    console.log('检查Python环境...');
    const result = await pythonTradeService.checkEnvironment();

    console.log('Python环境检查结果:', result);
    res.json(result);
  } catch (error) {
    console.error('检查Python环境错误:', error);
    res.status(500).json({
      success: false,
      error: '检查Python环境失败: ' + (error.message || error.error || '未知错误')
    });
  }
});

// OKX账户余额查询 - 使用JavaScript OKX服务
app.get('/api/python/okx/balance', async (req, res) => {
  try {
    console.log('获取OKX账户余额(JavaScript)...');

    // 检查API密钥是否已设置
    const userId = req.query.userId || 'default';
    const apiKeys = okxService.getUserApiKeys(userId);
    if (!apiKeys) {
      console.error('OKX API密钥未设置，无法获取账户余额');
      return res.status(400).json({
        error: 'API密钥未设置，请先配置API密钥',
        helpUrl: '/test-okx'
      });
    }

    console.log('✅ API密钥已配置，使用JavaScript OKX服务获取余额');

    // 使用JavaScript OKX服务获取账户信息
    const result = await okxService.getAccountPositions(userId);

    if (result.error) {
      console.log('获取账户余额失败:', result.error);
      return res.status(400).json({
        error: result.error
      });
    }

    // 格式化返回数据以匹配前端期望的格式
    console.log('🔍 原始余额数据:', JSON.stringify(result.balance, null, 2));

    // 转换余额数据格式 - 处理OKX API的嵌套结构
    let formattedBalance = {};
    if (Array.isArray(result.balance) && result.balance.length > 0) {
      // OKX API返回的是数组，第一个元素包含details数组
      const balanceData = result.balance[0];
      if (balanceData && balanceData.details && Array.isArray(balanceData.details)) {
        balanceData.details.forEach(item => {
          if (item.ccy && (parseFloat(item.eq || 0) > 0 || parseFloat(item.availEq || 0) > 0)) {
            const totalBalance = parseFloat(item.eq || 0);
            const availableBalance = parseFloat(item.availEq || 0);
            const usedBalance = totalBalance - availableBalance;

            formattedBalance[item.ccy] = {
              free: availableBalance.toString(),
              used: usedBalance.toString(),
              total: totalBalance.toString()
            };
          }
        });
      }
    }

    const formattedResult = {
      success: true,
      balance: formattedBalance,
      positions: result.positions || [],
      message: '获取账户余额成功'
    };

    console.log('✅ 获取账户余额成功，格式化后的余额:', JSON.stringify(formattedBalance, null, 2));
    res.json(formattedResult);
  } catch (error) {
    console.error('获取OKX账户余额错误:', error);
    res.status(500).json({
      error: '获取账户余额失败: ' + (error.message || '未知错误')
    });
  }
});

// Python交易API - 获取市场价格
app.get('/api/python/okx/price', async (req, res) => {
  try {
    const symbol = req.query.symbol;
    if (!symbol) {
      return res.status(400).json({ error: '缺少交易对参数' });
    }

    console.log(`获取OKX市场价格(Python): ${symbol}`);
    const result = await pythonTradeService.getMarketPrice(symbol);

    if (result.error) {
      console.log('获取市场价格失败:', result.error);
      return res.status(400).json({ error: result.error });
    }

    console.log('获取市场价格成功');
    res.json(result);
  } catch (error) {
    console.error('获取OKX市场价格错误:', error);
    res.status(500).json({ error: '获取市场价格失败' });
  }
});

// Python交易API - 执行交易
app.post('/api/python/okx/trade', async (req, res) => {
  try {
    const { symbol, side, amount, price } = req.body;

    if (!symbol || !side || !amount) {
      return res.status(400).json({ error: '缺少必要的交易参数' });
    }

    if (side !== 'buy' && side !== 'sell') {
      return res.status(400).json({ error: '交易方向无效，必须是 buy 或 sell' });
    }

    console.log(`执行OKX交易(Python): ${side} ${amount} ${symbol} @ ${price || 'market'}`);
    const result = await pythonTradeService.executeTrade(symbol, side, parseFloat(amount), price ? parseFloat(price) : undefined);

    if (result.error) {
      console.log('执行交易失败:', result.error);
      return res.status(400).json({ error: result.error });
    }

    console.log('执行交易成功');
    res.json(result);
  } catch (error) {
    console.error('执行OKX交易错误:', error);
    res.status(500).json({ error: '执行交易失败' });
  }
});

// 获取OKX历史订单
app.get('/api/okx/orders', async (req, res) => {
  try {
    const userId = req.query.userId || 'default';
    const params = req.query;
    console.log('获取历史订单请求:', params);

    const result = await okxService.getHistoryOrders(userId, params);

    if (result.error) {
      console.log('获取历史订单失败:', result.error);
      return res.status(400).json({ error: result.error });
    }

    console.log('获取历史订单成功');
    res.json(result);
  } catch (error) {
    console.error('获取OKX历史订单错误:', error);
    res.status(500).json({ error: '获取历史订单失败' });
  }
});


// 测试API端点 - 返回当前用户的OKX策略
app.get('/api/test-strategies', async (req, res) => {
  try {
    console.log('测试策略API请求');

    // 获取用户ID，优先从查询参数获取，然后从请求头获取
    let userId = req.query.userId;

    // 如果没有提供userId，尝试从请求头获取
    if (!userId && req.headers.authorization) {
      try {
        const token = req.headers.authorization.split(' ')[1];
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
        userId = decoded.uid;
        console.log('从JWT token获取用户ID:', userId);
      } catch (error) {
        console.log('JWT解析失败，使用默认用户ID');
      }
    }

    // 如果仍然没有userId，使用默认值
    if (!userId) {
      userId = 'default';
      console.log('使用默认用户ID:', userId);
    }

    console.log('查询用户策略，用户ID:', userId);

    // 检查是否使用MongoDB
    const useMongoDb = process.env.USE_MONGODB === 'true';
    let strategies = [];

    if (useMongoDb) {
      try {
        // 确保Strategy模型已注册
        let Strategy;
        try {
          Strategy = mongoose.model('Strategy');
        } catch (error) {
          // 如果模型未注册，则加载模型
          console.log('Strategy模型未注册，正在加载...');
          Strategy = require('./models/Strategy');
        }

        // 从数据库获取用户的OKX策略
        strategies = await Strategy.find({
          userId: userId,
          $or: [
            { exchange: 'okx' },
            { exchange: { $exists: false } } // 兼容旧数据，没有exchange字段的默认为OKX
          ]
        }).sort({ createdAt: -1 });

        console.log(`MongoDB: 找到 ${strategies.length} 个OKX策略，用户ID: ${userId}`);

        // 打印每个策略的基本信息
        strategies.forEach((strategy, index) => {
          console.log(`策略 ${index + 1}:`, {
            id: strategy._id.toString(),
            name: strategy.strategyName,
            status: strategy.status,
            type: strategy.type,
            exchange: strategy.exchange || 'okx',
            createdAt: strategy.createdAt
          });
        });
      } catch (error) {
        console.error('MongoDB查询失败:', error);
        // 如果数据库查询失败，返回空数组而不是错误
        strategies = [];
      }
    }

    // 直接返回策略列表
    res.json({ success: true, strategies: strategies, userId: userId });
  } catch (error) {
    console.error('获取策略列表错误:', error);
    res.status(500).json({ success: false, error: '获取策略列表失败: ' + error.message });
  }
});

// 获取策略列表
app.get('/api/strategies', async (req, res) => {
  try {
    const userId = req.query.userId || 'default';
    console.log('获取策略列表请求:', userId);

    // 检查是否使用MongoDB
    const useMongoDb = process.env.USE_MONGODB === 'true';
    let strategies = [];

    if (useMongoDb) {
      try {
        // 确保Strategy模型已注册
        let Strategy;
        try {
          Strategy = mongoose.model('Strategy');
        } catch (error) {
          // 如果模型未注册，则加载模型
          console.log('Strategy模型未注册，正在加载...');
          Strategy = require('./models/Strategy');
        }

        // 从数据库获取OKX策略列表
        strategies = await Strategy.find({
          userId,
          $or: [
            { exchange: 'okx' },
            { exchange: { $exists: false } } // 兼容旧数据
          ]
        }).sort({ createdAt: -1 });
        console.log(`MongoDB: 找到 ${strategies.length} 个OKX策略，用户ID: ${userId}`);

        // 打印每个策略的基本信息
        strategies.forEach((strategy, index) => {
          console.log(`策略 ${index + 1}:`, {
            id: strategy._id.toString(),
            name: strategy.strategyName,
            status: strategy.status,
            type: strategy.type,
            createdAt: strategy.createdAt
          });
        });
      } catch (error) {
        console.error('MongoDB查询失败:', error);
      }
    }

    // 直接返回策略列表，不进行额外处理
    res.json({ success: true, strategies: strategies });
  } catch (error) {
    console.error('获取策略列表错误:', error);
    res.status(500).json({ error: '获取策略列表失败' });
  }
});

// 获取单个策略详情
app.get('/api/strategies/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log('获取策略详情请求:', id);

    // 检查是否使用MongoDB
    const useMongoDb = process.env.USE_MONGODB === 'true';
    let strategy = null;

    if (useMongoDb) {
      try {
        // 确保Strategy模型已注册
        let Strategy;
        try {
          Strategy = mongoose.model('Strategy');
        } catch (error) {
          console.log('Strategy模型未注册，正在加载...');
          Strategy = require('./models/Strategy');
        }

        // 从数据库获取策略
        strategy = await Strategy.findById(id);
      } catch (error) {
        console.error('MongoDB查询失败:', error);
      }
    } else {
      // 从内存中获取策略
      const strategyMonitorService = require('./services/strategyMonitorService');
      strategy = strategyMonitorService.getInMemoryStrategies().get(id);
    }

    if (!strategy) {
      return res.status(404).json({ error: '策略不存在' });
    }

    console.log('获取策略详情成功');
    res.json({ success: true, strategy });
  } catch (error) {
    console.error('获取策略详情错误:', error);
    res.status(500).json({ error: '获取策略详情失败' });
  }
});

// 停止策略监控
app.post('/api/strategies/:id/stop', async (req, res) => {
  try {
    const { id } = req.params;
    console.log('停止策略监控请求:', id);

    // 检查是否使用MongoDB
    const useMongoDb = process.env.USE_MONGODB === 'true';
    let strategy = null;

    if (useMongoDb) {
      try {
        // 确保Strategy模型已注册
        let Strategy;
        try {
          Strategy = mongoose.model('Strategy');
        } catch (error) {
          console.log('Strategy模型未注册，正在加载...');
          Strategy = require('./models/Strategy');
        }

        // 从数据库获取策略
        strategy = await Strategy.findById(id);

        if (!strategy) {
          return res.status(404).json({ error: '策略不存在' });
        }
      } catch (error) {
        console.error('MongoDB查询失败:', error);
      }
    } else {
      // 从内存中获取策略
      const strategyMonitorService = require('./services/strategyMonitorService');
      strategy = strategyMonitorService.getInMemoryStrategies().get(id);

      if (!strategy) {
        return res.status(404).json({ error: '策略不存在' });
      }
    }

    // 导入策略监控服务
    const strategyMonitorService = require('./services/strategyMonitorService');

    // 检查监控任务状态
    const monitorJobs = strategyMonitorService.getMonitorJobs();
    const hasActiveJob = monitorJobs.has(id);
    console.log(`策略 ${id} 是否有活跃的监控任务: ${hasActiveJob}`);

    // 尝试停止策略监控
    const stopResult = await strategyMonitorService.stopStrategyMonitor(id);
    console.log(`停止策略监控结果: ${stopResult}`);

    // 确保策略状态正确更新
    if (strategy && (strategy.status === 'waiting' || strategy.status === 'active')) {
      console.log(`策略 ${id} 当前状态: ${strategy.status}，更新为completed`);

      if (useMongoDb) {
        // 更新数据库中的策略状态
        try {
          await strategy.updateStatus('completed');
          await strategy.addLog('用户手动停止策略');
          console.log(`策略 ${id} 状态已更新为completed`);
        } catch (updateError) {
          console.error(`更新策略状态失败: ${updateError.message}`);
        }
      } else {
        // 更新内存中的策略状态
        strategy.status = 'completed';
        if (!strategy.logs) strategy.logs = [];
        strategy.logs.push({
          message: '用户手动停止策略',
          timestamp: new Date()
        });
        strategyMonitorService.getInMemoryStrategies().set(id, strategy);
        console.log(`策略 ${id} 状态已更新为completed`);
      }
    } else {
      console.log(`策略 ${id} 当前状态: ${strategy ? strategy.status : '未知'}，无需更新`);
    }

    console.log('停止策略监控成功');
    res.json({ success: true, message: '策略监控已停止' });
  } catch (error) {
    console.error('停止策略监控错误:', error);
    res.status(500).json({ error: '停止策略监控失败' });
  }
});

// 恢复策略监控
app.post('/api/strategies/:id/recover', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.uid;
    console.log('恢复策略监控请求:', { strategyId: id, userId });

    // 检查是否使用MongoDB
    const useMongoDb = process.env.USE_MONGODB === 'true';
    let strategy = null;

    if (useMongoDb) {
      try {
        // 确保Strategy模型已注册
        let Strategy;
        try {
          Strategy = mongoose.model('Strategy');
        } catch (error) {
          console.log('Strategy模型未注册，正在加载...');
          Strategy = require('./models/Strategy');
        }

        // 从数据库获取策略
        strategy = await Strategy.findById(id);

        if (!strategy) {
          return res.status(404).json({ error: '策略不存在' });
        }

        // 验证策略所有者
        if (strategy.userId !== userId) {
          return res.status(403).json({ error: '无权限操作此策略' });
        }

        // 检查策略状态
        if (strategy.status !== 'error') {
          return res.status(400).json({ error: '只能恢复错误状态的策略' });
        }

      } catch (error) {
        console.error('MongoDB查询失败:', error);
        return res.status(500).json({ error: '数据库查询失败' });
      }
    } else {
      // 从内存中获取策略
      const strategyMonitorService = require('./services/strategyMonitorService');
      strategy = strategyMonitorService.getInMemoryStrategies().get(id);

      if (!strategy) {
        return res.status(404).json({ error: '策略不存在' });
      }

      if (strategy.userId !== userId) {
        return res.status(403).json({ error: '无权限操作此策略' });
      }

      if (strategy.status !== 'error') {
        return res.status(400).json({ error: '只能恢复错误状态的策略' });
      }
    }

    // 导入策略监控服务
    const strategyMonitorService = require('./services/strategyMonitorService');

    // 恢复策略状态
    console.log(`恢复策略 ${id}，当前状态: ${strategy.status}`);

    if (useMongoDb) {
      // 更新数据库中的策略状态
      try {
        await strategy.updateStatus('waiting');
        await strategy.addLog('用户手动恢复策略，重新启动监控');
        console.log(`策略 ${id} 状态已更新为waiting`);
      } catch (updateError) {
        console.error(`更新策略状态失败: ${updateError.message}`);
        return res.status(500).json({ error: '恢复策略失败: ' + updateError.message });
      }
    } else {
      // 更新内存中的策略状态
      strategy.status = 'waiting';
      if (!strategy.logs) strategy.logs = [];
      strategy.logs.push({
        message: '用户手动恢复策略，重新启动监控',
        timestamp: new Date()
      });
      strategyMonitorService.getInMemoryStrategies().set(id, strategy);
      console.log(`策略 ${id} 状态已更新为waiting`);
    }

    // 重新启动策略监控
    try {
      await strategyMonitorService.startStrategyMonitor(strategy);
      console.log(`策略 ${id} 监控已重新启动`);
    } catch (monitorError) {
      console.error(`重新启动策略监控失败: ${monitorError.message}`);
      // 如果启动监控失败，将状态改回error
      if (useMongoDb) {
        await strategy.updateStatus('error');
        await strategy.addLog(`恢复失败: ${monitorError.message}`);
      } else {
        strategy.status = 'error';
        strategy.logs.push({
          message: `恢复失败: ${monitorError.message}`,
          timestamp: new Date()
        });
        strategyMonitorService.getInMemoryStrategies().set(id, strategy);
      }
      return res.status(500).json({ error: '重新启动监控失败: ' + monitorError.message });
    }

    console.log('恢复策略监控成功');
    res.json({
      success: true,
      message: '策略已恢复，监控重新启动',
      strategy: {
        id: strategy._id || strategy.id,
        status: strategy.status,
        strategyName: strategy.strategyName
      }
    });
  } catch (error) {
    console.error('恢复策略监控错误:', error);
    res.status(500).json({ error: '恢复策略监控失败: ' + error.message });
  }
});

// 一键平仓
app.post('/api/strategies/:id/close-position', async (req, res) => {
  try {
    const { id } = req.params;
    console.log('一键平仓请求:', id);

    // 检查是否使用MongoDB
    const useMongoDb = process.env.USE_MONGODB === 'true';
    let strategy = null;

    if (useMongoDb) {
      try {
        // 确保Strategy模型已注册
        let Strategy;
        try {
          Strategy = mongoose.model('Strategy');
        } catch (error) {
          console.log('Strategy模型未注册，正在加载...');
          Strategy = require('./models/Strategy');
        }

        // 从数据库获取策略
        strategy = await Strategy.findById(id);

        if (!strategy) {
          return res.status(404).json({ error: '策略不存在' });
        }
      } catch (error) {
        console.error('MongoDB查询失败:', error);
        return res.status(500).json({ error: '数据库查询失败' });
      }
    } else {
      // 从内存中获取策略
      const strategyMonitorService = require('./services/strategyMonitorService');
      strategy = strategyMonitorService.getInMemoryStrategies().get(id);

      if (!strategy) {
        return res.status(404).json({ error: '策略不存在' });
      }
    }

    // 检查策略状态
    if (strategy.status !== 'active') {
      return res.status(400).json({ error: '只有运行中的策略才能执行一键平仓' });
    }

    // 检查是否有持仓
    const hasPosition = strategy.orders && strategy.orders.length > 0 && 
                       strategy.orders.some(order => order.side === 'buy');

    if (!hasPosition) {
      return res.status(400).json({ error: '策略没有持仓，无需平仓' });
    }

    console.log(`策略 ${strategy.strategyName} 开始执行一键平仓`);

    // 获取用户API凭证
    const okxService = require('./services/okxService');
    const userApiKeys = okxService.getUserApiKeys(strategy.userId);

    let credentials;
    if (userApiKeys) {
      credentials = {
        apiKey: userApiKeys.apiKey,
        secret: userApiKeys.secretKey,
        password: userApiKeys.passphrase
      };
    } else {
      credentials = {
        apiKey: process.env.OKX_API_KEY,
        secret: process.env.OKX_SECRET_KEY,
        password: process.env.OKX_PASSPHRASE
      };
    }

    // 执行平仓
    const exitMonitorService = require('./services/exitMonitorService');
    await exitMonitorService.executeExit(strategy, credentials);

    // 停止策略监控
    const strategyMonitorService = require('./services/strategyMonitorService');
    await strategyMonitorService.stopStrategyMonitor(id);

    // 记录日志
    if (useMongoDb && strategy.addLog) {
      await strategy.addLog('用户执行一键平仓');
    } else if (!useMongoDb) {
      if (!strategy.logs) strategy.logs = [];
      strategy.logs.push({
        message: '用户执行一键平仓',
        timestamp: new Date()
      });
      strategyMonitorService.getInMemoryStrategies().set(id, strategy);
    }

    console.log('一键平仓执行成功');
    res.json({ success: true, message: '一键平仓执行成功，所有持仓已卖出' });
  } catch (error) {
    console.error('一键平仓错误:', error);

    // 改进错误处理，确保错误信息正确传递给前端
    let errorMessage = error.message || '未知错误';

    // 如果是我们自定义的详细错误，直接使用
    if (error.originalError) {
      errorMessage = error.message;
    }

    // 检查是否是余额不足错误，提供更友好的提示
    if (errorMessage.includes('51008') || errorMessage.includes('insufficient')) {
      errorMessage += '\n\n💡 建议检查:\n1. 账户中是否有对应币种的余额\n2. 策略是否已经有实际持仓\n3. 使用"查看持仓"功能确认实际余额';
    }

    res.status(500).json({
      success: false,
      error: errorMessage
    });
  }
});

// OKX账户余额查询
app.get('/api/okx/balance/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    console.log(`查询OKX账户余额: ${userId}`);

    const okxService = require('./services/okxService');
    const client = okxService.getOkxClient(userId);

    if (!client) {
      return res.status(400).json({
        success: false,
        error: 'API密钥未设置，请先配置OKX API密钥'
      });
    }

    // 获取账户余额
    const balanceResponse = await client.getBalance();
    console.log('账户余额响应:', balanceResponse);

    let balances = [];
    if (balanceResponse && balanceResponse.data && Array.isArray(balanceResponse.data)) {
      for (const balanceData of balanceResponse.data) {
        if (balanceData.details && Array.isArray(balanceData.details)) {
          balances = balanceData.details.filter(detail =>
            parseFloat(detail.availBal) > 0 || parseFloat(detail.bal) > 0
          );
        }
      }
    }

    res.json({
      success: true,
      balances: balances,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('查询账户余额失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '查询账户余额失败'
    });
  }
});

// 删除策略
app.delete('/api/strategies/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log('删除策略请求:', id);

    // 检查是否使用MongoDB
    const useMongoDb = process.env.USE_MONGODB === 'true';

    // 首先尝试停止策略监控（如果有）
    try {
      await strategyMonitorService.stopStrategyMonitor(id);
    } catch (error) {
      console.log(`停止策略监控失败，但将继续删除: ${error.message}`);
    }

    if (useMongoDb) {
      try {
        // 确保Strategy模型已注册
        let Strategy;
        try {
          Strategy = mongoose.model('Strategy');
        } catch (error) {
          console.log('Strategy模型未注册，正在加载...');
          Strategy = require('./models/Strategy');
        }

        // 从数据库删除策略
        const result = await Strategy.findByIdAndDelete(id);

        if (!result) {
          console.log(`策略 ${id} 不存在或已被删除`);
        } else {
          console.log(`策略 ${id} 已从数据库中删除`);
        }
      } catch (error) {
        console.error('MongoDB删除失败:', error);
        return res.status(500).json({ error: '删除策略失败: ' + error.message });
      }
    } else {
      // 从内存中删除策略
      const strategies = strategyMonitorService.getInMemoryStrategies();
      if (strategies.has(id)) {
        strategies.delete(id);
        console.log(`策略 ${id} 已从内存中删除`);
      } else {
        console.log(`策略 ${id} 不存在或已被删除`);
      }
    }

    console.log('删除策略成功');
    res.json({ success: true, message: '策略已删除' });
  } catch (error) {
    console.error('删除策略错误:', error);
    res.status(500).json({ error: '删除策略失败' });
  }
});

// 测试OKX API连接
app.get('/api/okx/test-connection', async (req, res) => {
  try {
    const userId = req.query.userId || 'default';
    console.log('测试OKX API连接:', userId);

    const client = okxService.getOkxClient(userId);
    if (!client) {
      return res.status(400).json({
        success: false,
        error: 'API密钥未设置，请先配置API密钥'
      });
    }

    // 尝试获取系统时间，这是一个简单的公共API调用
    const systemTime = await client.getSystemTime();
    console.log('系统时间:', systemTime);

    // 尝试获取交易对信息，这是一个公共API调用
    const instruments = await client.getInstruments({ instType: 'SPOT' });
    console.log('获取交易对成功，数量:', instruments.length);

    // 尝试获取账户余额，这需要API密钥验证
    try {
      const balance = await client.getBalance();
      console.log('获取账户余额成功');

      // 尝试获取持仓信息
      try {
        const positionsResult = await client.getPositions({ instType: 'ANY' });
        let positions = [];

        // 检查返回结果的格式
        if (positionsResult && Array.isArray(positionsResult)) {
          positions = positionsResult;
        } else if (positionsResult && positionsResult.data && Array.isArray(positionsResult.data)) {
          positions = positionsResult.data;
        } else {
          console.log('获取持仓信息成功，但返回格式异常:', typeof positionsResult);
          positions = [];
        }

        console.log('获取持仓信息成功，持仓数量:', positions.length);

        return res.json({
          success: true,
          message: 'OKX API连接成功',
          systemTime,
          instruments: instruments.slice(0, 3), // 只返回前3个交易对
          balance,
          positions,
          apiVersion: require('okx-api/package.json').version
        });
      } catch (positionsError) {
        console.warn('获取持仓信息失败，可能没有持仓:', positionsError.message);
        return res.json({
          success: true,
          message: 'OKX API连接成功，但获取持仓信息失败',
          systemTime,
          instruments: instruments.slice(0, 3),
          balance,
          positions: [],
          positionsError: positionsError.message,
          apiVersion: require('okx-api/package.json').version
        });
      }
    } catch (balanceError) {
      console.error('获取余额失败:', balanceError);
      return res.json({
        success: true,
        message: 'OKX API公共接口连接成功，但获取账户数据失败',
        systemTime,
        instruments: instruments.slice(0, 3),
        error: balanceError.message,
        apiVersion: require('okx-api/package.json').version
      });
    }
  } catch (error) {
    console.error('测试OKX API连接失败:', error);
    return res.status(500).json({
      success: false,
      error: '测试OKX API连接失败: ' + error.message,
      apiVersion: '2.0.5'
    });
  }
});

// WebSocket连接
io.on('connection', (socket) => {
  console.log('Client connected');

  // 价格更新相关变量
  let priceUpdateFailCount = 0;
  let priceUpdateInterval = 3000; // 初始3秒更新一次

  // 定期发送价格更新 (使用Gate.io API)
  const priceUpdateFunction = async () => {;
    try {
      // 使用Gate.io API服务获取价格信息
      const tickers = await gateApi.getTickers();

      // 使用API服务格式化数据
      const prices = gateApi.formatTickerData(tickers, 20);

      // 重置失败计数和更新间隔
      if (priceUpdateFailCount > 0) {
        console.log('Gate.io价格更新恢复正常');
        priceUpdateFailCount = 0;
        priceUpdateInterval = 3000;
      }

      socket.emit('priceUpdate', prices);
    } catch (error) {
      priceUpdateFailCount++;

      // 根据失败次数调整更新频率，避免频繁请求失败的API
      if (priceUpdateFailCount >= 5) {
        if (priceUpdateInterval < 30000) { // 最长延长到30秒
          clearInterval(priceInterval);
          priceUpdateInterval = Math.min(priceUpdateInterval * 2, 30000);
          console.log(`Gate.io价格更新失败${priceUpdateFailCount}次，调整更新间隔为${priceUpdateInterval}ms`);

          // 清除旧的定时器并使用新的间隔重新设置
          clearInterval(priceInterval);
          socket.priceInterval = setInterval(priceUpdateFunction, priceUpdateInterval);
        }
      }

      console.error(`Gate.io价格更新失败(${priceUpdateFailCount}): ${error.message}`);

      // 发送错误通知给客户端
      if (priceUpdateFailCount % 5 === 0) { // 每5次失败发送一次通知
        socket.emit('priceUpdateError', {
          error: 'Gate.io价格数据暂时不可用，请稍后再试',
          retryIn: Math.floor(priceUpdateInterval / 1000)
        });
      }
    }
  };

  socket.priceInterval = setInterval(priceUpdateFunction, priceUpdateInterval);

  // OKX WebSocket连接
  socket.on('subscribeOkx', async (data) => {
    const { userId = 'default' } = data;
    console.log('订阅OKX账户更新:', userId);

    // 检查API密钥是否已设置
    const apiKeys = okxService.getUserApiKeys(userId);
    if (!apiKeys) {
      console.error('OKX API密钥未设置，无法订阅账户更新');
      socket.emit('okxError', { error: 'API密钥未设置，请先配置API密钥' });
      return;
    }

    // 订阅OKX账户和持仓更新
    const cleanup = okxService.subscribeAccountUpdates(userId, (updateData) => {
      socket.emit('okxUpdate', updateData);
    });

    // 保存清理函数，在主断开连接处理程序中使用
    socket.okxCleanup = cleanup;

    // 发送初始账户数据
    try {
      console.log('获取OKX初始账户数据...');
      const accountData = await okxService.getAccountPositions(userId);

      if (accountData.error) {
        console.error('获取OKX账户数据失败:', accountData.error);
        socket.emit('okxError', { error: accountData.error });
      } else {
        console.log('成功获取OKX账户数据');
        socket.emit('okxAccountInit', accountData);
      }
    } catch (error) {
      console.error('获取OKX初始账户数据错误:', error);
      socket.emit('okxError', {
        error: '获取账户数据失败',
        details: error.message || '未知错误'
      });
    }
  });

  // 币安WebSocket连接
  socket.on('subscribeBinance', async (data) => {
    const { userId = 'default' } = data;
    console.log('订阅币安账户更新:', userId);

    // 检查API密钥是否已设置
    const apiKeys = getBinanceApiKeys(userId);
    if (!apiKeys || !apiKeys.apiKey || !apiKeys.secretKey) {
      console.error('币安API密钥未设置，无法订阅账户更新');
      socket.emit('binanceError', { error: 'API密钥未设置，请先配置API密钥' });
      return;
    }

    // 发送初始账户数据
    try {
      console.log('获取币安初始账户数据...');
      const accountData = await binancePythonTradeService.getBinanceAccountBalance(userId);

      if (accountData.error) {
        console.error('获取币安账户数据失败:', accountData.error);
        socket.emit('binanceError', { error: accountData.error });
      } else {
        console.log('成功获取币安账户数据');
        socket.emit('binanceAccountInit', accountData);
      }
    } catch (error) {
      console.error('获取币安初始账户数据错误:', error);
      socket.emit('binanceError', {
        error: '获取账户数据失败',
        details: error.message || '未知错误'
      });
    }
  });

  // 断开连接时清除定时器和WebSocket订阅
  socket.on('disconnect', () => {
    console.log('Client disconnected');

    // 清除价格更新定时器
    if (socket.priceInterval) {
      clearInterval(socket.priceInterval);
    }

    // 清理OKX WebSocket订阅
    try {
      if (socket.okxCleanup && typeof socket.okxCleanup === 'function') {
        console.log('清理OKX WebSocket订阅');
        socket.okxCleanup();
      }
    } catch (error) {
      console.error('清理OKX WebSocket订阅失败:', error);
    }
  });
});

// 连接MongoDB数据库
const connectDB = async () => {;
  try {
    console.log('正在连接MongoDB数据库...');

    // 使用本地身份验证连接MongoDB
    // 注意：这里使用您系统的用户名和密码
    // 如果您的MongoDB没有设置身份验证，可以使用简单的连接字符串
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/crypto-trading-platform';
    console.log('MongoDB URI:', mongoURI);

    // 定义Mongoose连接选项
    const mongooseOptions = {
      serverSelectionTimeoutMS: 30000,  // 增加超时时间到30秒
      socketTimeoutMS: 60000,           // Socket超时60秒
      connectTimeoutMS: 30000,          // 连接超时30秒
      maxPoolSize: 10,                  // 最大连接池大小
      minPoolSize: 2,                   // 最小连接池大小
      maxIdleTimeMS: 30000,             // 最大空闲时间
      heartbeatFrequencyMS: 10000       // 心跳频率
    };

    // 尝试连接
    await mongoose.connect(mongoURI, {
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      maxPoolSize: 10
    });

    console.log('MongoDB数据库连接成功');

    // 加载数据模型（不删除缓存，避免影响其他服务）
    require('./models/User');
    require('./models/Strategy');
    require('./models/KlineData');
    console.log('数据模型加载完成');

    // 设置数据库连接到各个服务
    const klineDataService = require('./services/klineDataService');
    klineDataService.setDB(mongoose.connection.db);
    console.log('K线数据服务数据库连接设置完成');

    // 测试数据库连接
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('数据库集合列表:', collections.map(c => c.name));

    // 设置环境变量
    process.env.USE_MONGODB = 'true';

    return true;
  } catch (error) {
    console.error('MongoDB数据库连接失败:', error);
    console.log('将使用内存存储策略');

    // 设置环境变量
    process.env.USE_MONGODB = 'false';

    return false;
  }
};

// 启动服务器
const PORT = process.env.PORT || 3009; // 使用3009端口
server.listen(PORT, async () => {
  console.log(`服务器运行在端口 ${PORT}`);

  // 应用网络连接修复
  console.log('\n=== 应用网络连接修复 ===');
  try {
    await fixNetworkConnections();
    console.log('✅ 网络连接修复完成');
  } catch (error) {
    console.error('❌ 网络连接修复失败:', error);
  }

  // 启动时执行网络和DNS检查
  console.log('\n=== 启动时网络和DNS检查 ===');

  // 强制初始化DNS配置 (VPN环境优化)
  console.log('🚀 强制初始化DNS配置 (VPN环境优化)...');
  try {
    const dnsInitSuccess = forceInitializeDNS();
    if (dnsInitSuccess) {
      console.log('✅ VPN环境DNS配置初始化成功');
    } else {
      console.warn('⚠️ VPN环境DNS配置初始化失败，将使用默认配置');
    }
  } catch (error) {
    console.error('❌ DNS强制初始化失败:', error.message);
  }

  // 等待DNS配置生效
  await new Promise(resolve => setTimeout(resolve, 2000));

  // DNS检查
  try {
    console.log('🔍 执行DNS检查...');
    const dnsResults = await testDNSResolution();
    console.log(`DNS解析测试: 成功${dnsResults.successful.length}个, 失败${dnsResults.failed.length}个`);

    if (dnsResults.failed.length > 0) {
      console.warn('⚠️  部分域名DNS解析失败:');
      dnsResults.failed.forEach(fail => {
        console.warn(`  ❌ ${fail.hostname}: ${fail.error}`);
      });
    }
  } catch (error) {
    console.error('❌ DNS检查失败:', error.message);
  }

  // 网络健康检查
  try {
    console.log('🔍 执行网络健康检查...');
    const healthStatus = await performHealthCheck();
    console.log('网络健康检查完成:', healthStatus.overall);

    if (healthStatus.overall === 'degraded') {
      console.warn('⚠️  检测到网络连接问题，某些API可能不稳定');
      healthStatus.results.forEach(result => {
        if (!result.success) {
          console.warn(`❌ ${result.url}: ${result.message}`);
        }
      });
      console.warn('💡 建议运行DNS诊断: node server/scripts/dns-fix.js');
    } else {
      console.log('✅ 所有网络连接正常');
    }
  } catch (error) {
    console.error('❌ 启动时网络检查失败:', error.message);
  }
  console.log('==============================\n');

  // 连接数据库
  const dbConnected = await connectDB();

  // 无论数据库是否连接成功，都初始化策略监控服务
  try {
    console.log('正在初始化OKX策略监控服务...');
    await strategyMonitorService.initStrategyMonitor();
    console.log('OKX策略监控服务初始化成功');
  } catch (error) {
    console.error('OKX策略监控服务初始化失败:', error);
  }

  // 初始化币安策略监控服务
  try {
    console.log('正在初始化币安策略监控服务...');
    await binanceStrategyMonitorService.initStrategyMonitor();
    console.log('币安策略监控服务初始化成功');
  } catch (error) {
    console.error('币安策略监控服务初始化失败:', error);
  }

  // 启动K线数据收集服务 - 已禁用，因为已有完整历史数据
  if (dbConnected) {
    console.log('⚠️  K线数据收集服务已禁用');
    console.log('原因: 已有完整的历史数据(2024/5/1-2025/5/1)，无需定时收集');
    console.log('如需启用，请调用 /api/kline-data/start-collection API');

    // 注释掉自动启动代码，避免与历史数据收集冲突
    // try {
    //   console.log('正在启动K线数据收集服务...');
    //   const dataCollectionService = require('./services/dataCollectionService');
    //   await dataCollectionService.start();
    //   console.log('K线数据收集服务启动成功');
    // } catch (error) {
    //   console.error('K线数据收集服务启动失败:', error);
    // }
  } else {
    console.log('数据库未连接，跳过K线数据收集服务启动');
  }
});

// ==================== 策略回测 API 路由 ====================

// 测试回测功能（不需要认证，仅用于调试）
app.post('/api/backtest', async (req, res) => {
  try {
    const userId = req.body.userId || 'default';
    const {
      exchange,
      strategyId,
      symbol,
      timeframe,
      startDate,
      endDate,
      initialCapital,
      feeRate,
      strategyParams
    } = req.body;

    console.log('收到回测请求:', {
      userId,
      exchange,
      strategyId,
      symbol,
      timeframe,
      startDate,
      endDate,
      initialCapital
    });

    // 验证请求参数
    if (!exchange || !strategyId || !symbol || !timeframe || !startDate || !endDate || !initialCapital) {
      return res.status(400).json({
        success: false,
        error: '缺少必要的回测参数'
      });
    }

    // 验证交易所
    if (!['binance', 'okx'].includes(exchange)) {
      return res.status(400).json({
        success: false,
        error: '不支持的交易所'
      });
    }

    // 验证日期
    const start = new Date(startDate);
    const end = new Date(endDate);
    if (start >= end) {
      return res.status(400).json({
        success: false,
        error: '开始日期必须早于结束日期'
      });
    }

    // 验证初始资金
    if (initialCapital <= 0) {
      return res.status(400).json({
        success: false,
        error: '初始资金必须大于0'
      });
    }

    // 运行回测
    const result = await backtestService.runBacktest({
      exchange,
      strategyId,
      symbol,
      timeframe,
      startDate,
      endDate,
      initialCapital,
      feeRate: feeRate || 0.1,
      strategyParams: strategyParams || {},
      userId
    });

    if (result.success) {
      console.log('回测完成:', {
        totalReturn: result.results.totalReturn,
        winRate: result.results.winRate,
        totalTrades: result.results.totalTrades
      });
      res.json(result);
    } else {
      console.log('回测失败:', result.error);
      res.status(400).json(result);
    }

  } catch (error) {
    console.error('回测失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 运行策略回测（需要认证）
app.post('/api/backtest/run', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.uid;
    const {
      exchange,
      strategyId,
      symbol,
      timeframe,
      startDate,
      endDate,
      initialCapital,
      feeRate,
      strategyParams
    } = req.body;

    console.log('收到回测请求:', {
      userId,
      exchange,
      strategyId,
      symbol,
      timeframe,
      startDate,
      endDate,
      initialCapital
    });

    // 验证请求参数
    if (!exchange || !strategyId || !symbol || !timeframe || !startDate || !endDate || !initialCapital) {
      return res.status(400).json({
        success: false,
        error: '缺少必要的回测参数'
      });
    }

    // 验证交易所
    if (!['binance', 'okx'].includes(exchange)) {
      return res.status(400).json({
        success: false,
        error: '不支持的交易所'
      });
    }

    // 验证日期
    const start = new Date(startDate);
    const end = new Date(endDate);
    if (start >= end) {
      return res.status(400).json({
        success: false,
        error: '开始日期必须早于结束日期'
      });
    }

    // 验证初始资金
    if (initialCapital <= 0) {
      return res.status(400).json({
        success: false,
        error: '初始资金必须大于0'
      });
    }

    // 运行回测
    const result = await backtestService.runBacktest({
      exchange,
      strategyId,
      symbol,
      timeframe,
      startDate,
      endDate,
      initialCapital,
      feeRate: feeRate || 0.1,
      strategyParams: strategyParams || {},
      userId
    });

    if (result.success) {
      console.log('回测完成:', {
        totalReturn: result.results.totalReturn,
        winRate: result.results.winRate,
        totalTrades: result.results.totalTrades
      });
      res.json(result);
    } else {
      console.log('回测失败:', result.error);
      res.status(400).json(result);
    }

  } catch (error) {
    console.error('回测API错误:', error);
    res.status(500).json({
      success: false,
      error: '回测过程中发生服务器错误: ' + error.message
    });
  }
});

// 获取所有策略定义
app.get('/api/backtest/strategies', async (req, res) => {
  try {
    const { exchange } = req.query;

    if (exchange === 'binance') {
      const { binanceFuturesStrategies, binanceSpotStrategies } = require('./services/binanceStrategyDefinitionService');

      // 转换币安策略为前端格式
      const strategies = [];

      // 添加币安合约策略
      Object.keys(binanceFuturesStrategies).forEach(key => {
        const strategy = binanceFuturesStrategies[key];
        strategies.push({
          id: key,
          name: strategy.name,
          icon: getStrategyIcon(key),
          description: strategy.description,
          type: 'futures',
          typeLabel: '合约',
          timeframe: strategy.timeframe,
          exchange: 'binance',
          parameters: getStrategyParameters(strategy)
        });
      });

      // 添加币安现货策略（过滤掉多多益善策略）
      Object.keys(binanceSpotStrategies).forEach(key => {
        // 隐藏币安多多益善策略
        if (key === 'binance_spot5') {
          return;
        }

        const strategy = binanceSpotStrategies[key];
        strategies.push({
          id: key,
          name: strategy.name,
          icon: getStrategyIcon(key),
          description: strategy.description,
          type: 'spot',
          typeLabel: '现货',
          timeframe: strategy.timeframe,
          exchange: 'binance',
          parameters: getStrategyParameters(strategy)
        });
      });

      res.json({ success: true, strategies });
    } else if (exchange === 'okx') {
      const { futuresStrategies, spotStrategies } = require('./services/strategyDefinitionService');

      // 转换OKX策略为前端格式
      const strategies = [];

      // 添加OKX合约策略
      Object.keys(futuresStrategies).forEach(key => {
        const strategy = futuresStrategies[key];
        strategies.push({
          id: key,
          name: `OKX ${strategy.name}`,
          icon: getStrategyIcon(key),
          description: strategy.description,
          type: 'futures',
          typeLabel: '合约',
          timeframe: strategy.timeframe,
          exchange: 'okx',
          parameters: getStrategyParameters(strategy)
        });
      });

      // 添加OKX现货策略（过滤掉多多益善策略）
      Object.keys(spotStrategies).forEach(key => {
        // 隐藏OKX多多益善策略
        if (key === 'spot5') {
          return;
        }

        const strategy = spotStrategies[key];
        strategies.push({
          id: key,
          name: `OKX ${strategy.name}`,
          icon: getStrategyIcon(key),
          description: strategy.description,
          type: 'spot',
          typeLabel: '现货',
          timeframe: strategy.timeframe,
          exchange: 'okx',
          parameters: getStrategyParameters(strategy)
        });
      });

      res.json({ success: true, strategies });
    } else {
      res.status(400).json({ success: false, error: '请指定交易所参数' });
    }
  } catch (error) {
    console.error('获取策略定义失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 辅助函数：获取策略图标
function getStrategyIcon(strategyId) {
  const iconMap = {
    // 合约策略图标
    'binance_futures1': '📈', 'futures1': '📈',
    'binance_futures2': '📊', 'futures2': '📊',
    'binance_futures3': '⚡', 'futures3': '⚡',
    'binance_futures4': '🎯', 'futures4': '🎯',
    'binance_futures5': '🔄', 'futures5': '🔄',
    'binance_futures6': '📉', 'futures6': '📉',
    'binance_futures7': '🌊', 'futures7': '🌊',
    'binance_futures8': '🎲', 'futures8': '🎲',
    'binance_futures9': '⭐', 'futures9': '⭐',
    'binance_futures10': '🏆', 'futures10': '🏆',
    // 现货策略图标
    'binance_spot1': '💰', 'spot1': '💰',
    'binance_spot2': '📈', 'spot2': '📈',
    'binance_spot3': '🔄', 'spot3': '🔄',
    'binance_spot4': '🎯', 'spot4': '🎯',
    'binance_spot5': '🚀', 'spot5': '🚀'
  };
  return iconMap[strategyId] || '📊';
}

// 辅助函数：获取策略参数
function getStrategyParameters(strategy) {
  const parameters = [];

  // 根据策略的入场和出场条件生成参数
  if (strategy.entryConditions) {
    if (strategy.entryConditions.maCross) {
      parameters.push(
        { key: 'fastMA', label: '快速均线', type: 'number', default: strategy.entryConditions.maCross.fastPeriod, min: 5, max: 50, description: '短期移动平均线周期' },
        { key: 'slowMA', label: '慢速均线', type: 'number', default: strategy.entryConditions.maCross.slowPeriod, min: 20, max: 100, description: '长期移动平均线周期' }
      );
    }
    if (strategy.entryConditions.volumeIncrease) {
      parameters.push(
        { key: 'volumeRatio', label: '成交量倍数', type: 'number', default: strategy.entryConditions.volumeIncrease.ratio, min: 1.0, max: 3.0, step: 0.1, description: '成交量放大倍数' }
      );
    }
    if (strategy.entryConditions.rsiOversoldOverbought) {
      parameters.push(
        { key: 'rsiPeriod', label: 'RSI周期', type: 'number', default: strategy.entryConditions.rsiOversoldOverbought.period, min: 10, max: 30, description: 'RSI指标计算周期' },
        { key: 'rsiOversold', label: 'RSI超卖', type: 'number', default: strategy.entryConditions.rsiOversoldOverbought.oversoldThreshold, min: 20, max: 40, description: 'RSI超卖阈值' },
        { key: 'rsiOverbought', label: 'RSI超买', type: 'number', default: strategy.entryConditions.rsiOversoldOverbought.overboughtThreshold, min: 60, max: 80, description: 'RSI超买阈值' }
      );
    }
    if (strategy.addPositionConditions && strategy.addPositionConditions.priceDropTrigger) {
      parameters.push(
        { key: 'addPositionPercent', label: '加仓跌幅(%)', type: 'number', default: strategy.addPositionConditions.priceDropTrigger.percentage, min: 0.5, max: 5, step: 0.1, description: '价格下跌多少百分比时加仓' }
      );
    }
    if (strategy.exitConditions && strategy.exitConditions.totalProfit) {
      parameters.push(
        { key: 'profitTarget', label: '止盈目标(%)', type: 'number', default: strategy.exitConditions.totalProfit.percentage, min: 1, max: 20, description: '整体盈利多少百分比时卖出' }
      );
    }
  }

  return parameters;
}
