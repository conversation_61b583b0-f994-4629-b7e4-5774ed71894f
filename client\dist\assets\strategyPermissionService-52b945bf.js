import{k as a}from"./index-a2cd3c28.js";class n{constructor(){this.userStrategies=null,this.lastFetchTime=0,this.cacheTimeout=5*60*1e3}async getUserStrategies(){const t=Date.now();if(this.userStrategies&&t-this.lastFetchTime<this.cacheTimeout)return console.log("🔄 使用缓存的用户策略权限 (缓存时间:",Math.floor((t-this.lastFetchTime)/1e3),"秒前):",this.userStrategies),this.userStrategies;try{console.log("🌐 正在从服务器获取用户策略权限...");const e=await a.get("/strategy-permission/user-strategies");if(console.log("📡 策略权限API响应状态:",e.status),console.log("📡 策略权限API响应数据:",e.data),e.data.success&&e.data.data&&Array.isArray(e.data.data))return this.userStrategies=e.data.data,this.lastFetchTime=t,console.log("✅ 成功获取用户策略权限 (",this.userStrategies.length,"个策略):",this.userStrategies),console.log("📋 策略ID列表:",this.userStrategies.map(s=>s.strategyId)),this.userStrategies;throw console.error("❌ API响应格式异常:",e.data),new Error(e.data.error||"获取用户策略权限失败")}catch(e){console.error("❌ 获取用户策略权限失败:",e),console.log("🔧 权限获取失败，返回完整默认策略权限以确保用户可以使用系统");const s=[{strategyId:"spot1"},{strategyId:"spot2"},{strategyId:"spot3"},{strategyId:"spot4"},{strategyId:"spot5"},{strategyId:"futures1"},{strategyId:"futures2"},{strategyId:"futures3"},{strategyId:"futures4"},{strategyId:"futures5"},{strategyId:"futures6"},{strategyId:"futures7"},{strategyId:"futures8"},{strategyId:"futures9"},{strategyId:"futures10"}];return console.log("🛡️ 使用默认策略权限 (",s.length,"个策略):",s.map(r=>r.strategyId)),this.userStrategies=s,this.lastFetchTime=t,s}}async hasPermission(t){console.log("🔍 检查策略权限:",t);const e=await this.getUserStrategies(),s=e.some(r=>r.strategyId===t);return console.log("🎯 权限检查结果:",{strategyId:t,hasPermission:s,userStrategyIds:e.map(r=>r.strategyId),totalUserStrategies:e.length}),s}async filterStrategies(t){const s=(await this.getUserStrategies()).map(r=>r.strategyId);return t.filter(r=>{const u=this.mapStrategyTemplateToId(r.value||r.id);return s.includes(u)})}mapStrategyTemplateToId(t){return{spot1:"spot1",spot2:"spot2",spot3:"spot3",spot4:"spot4",spot5:"spot5",binance_spot1:"spot1",binance_spot2:"spot2",binance_spot3:"spot3",binance_spot4:"spot4",binance_spot5:"spot5",futures1:"futures1",futures2:"futures2",futures3:"futures3",futures4:"futures4",futures5:"futures5",futures6:"futures6",futures7:"futures7",futures8:"futures8",futures9:"futures9",futures10:"futures10",binance_futures1:"futures1",binance_futures2:"futures2",binance_futures3:"futures3",binance_futures4:"futures4",binance_futures5:"futures5",binance_futures6:"futures6",binance_futures7:"futures7",binance_futures8:"futures8",binance_futures9:"futures9",binance_futures10:"futures10"}[t]||t}getPermissionErrorMessage(t){return`您当前的会员等级无权使用策略：${this.getStrategyName(t)}。请升级会员等级或联系管理员。`}getStrategyName(t){return{spot1:"现货策略1（顺势抄底）",spot2:"现货策略2（顺势涨利）",spot3:"现货策略3（低买高卖）",spot4:"现货策略4（大单追踪）",spot5:"现货策略5（多多益善）",binance_spot1:"币安现货策略1（顺势抄底）",binance_spot2:"币安现货策略2（顺势涨利）",binance_spot3:"币安现货策略3（低买高卖）",binance_spot4:"币安现货策略4（大单追踪）",binance_spot5:"币安现货策略5（多多益善）",futures1:"合约策略1（趋势动量追击）",futures2:"合约策略2（震荡区间套利）",futures3:"合约策略3（突破追击）",futures4:"合约策略4（高频震荡双向狙击）",futures5:"合约策略5（超级高胜率策略）",futures6:"合约策略6（MACD超神策略）",futures7:"合约策略7（RSI顺势策略）",futures8:"合约策略8（TD战法策略）",futures9:"合约策略9（顺风收益王者）",futures10:"合约策略10（超长线策略）",binance_futures1:"币安合约策略1（趋势动量追击）",binance_futures2:"币安合约策略2（震荡区间套利）",binance_futures3:"币安合约策略3（突破追击）",binance_futures4:"币安合约策略4（高频震荡双向狙击）",binance_futures5:"币安合约策略5（超级高胜率策略）",binance_futures6:"币安合约策略6（MACD超神策略）",binance_futures7:"币安合约策略7（RSI顺势策略）",binance_futures8:"币安合约策略8（TD战法策略）",binance_futures9:"币安合约策略9（顺风收益王者）",binance_futures10:"币安合约策略10（超长线策略）"}[t]||t}clearCache(){this.userStrategies=null,this.lastFetchTime=0}async validateStrategyPermission(t){const e=this.mapStrategyTemplateToId(t);if(!await this.hasPermission(e))throw new Error(this.getPermissionErrorMessage(t));return!0}async getUserMembershipInfo(){try{const t=await this.getUserStrategies(),e=t.length;let s="none",r="普通会员";return e>=15?(s="lifetime",r="永久会员"):e>=7?(s="yearly",r="年度会员"):e>=5?(s="quarterly",r="季度会员"):e>=3&&(s="monthly",r="月度会员"),{level:s,name:r,strategyCount:e,strategies:t}}catch(t){return console.error("获取用户会员信息失败:",t),{level:"none",name:"普通会员",strategyCount:0,strategies:[]}}}}const c=new n;export{c as s};
