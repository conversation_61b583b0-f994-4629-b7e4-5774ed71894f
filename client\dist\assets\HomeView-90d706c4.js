import{_ as E,u as H,l as C,r as y,o as B,m as q,c as l,d as r,e as s,i as u,t as i,n as c,g as m,F as U,p as S,q as p,k as x}from"./index-a2cd3c28.js";const J={name:"HomeView",setup(){const d=H(),t=C(()=>{const a=localStorage.getItem("user");return a?JSON.parse(a):null}),w=()=>{localStorage.removeItem("token"),localStorage.removeItem("user"),d.push({name:"login"})},n=y([{title:"注册交易所",iconClass:"bg-blue",fontAwesome:"fas fa-university",route:"exchange-register"},{title:"API介绍",iconClass:"bg-purple",icon:"⚙️",route:"api-intro"},{title:"我的收益",iconClass:"bg-green",icon:"💰",route:"my-earnings"},{title:"邀请好友",iconClass:"bg-orange",icon:"🎁",route:"invite-friends"},{title:"策略学堂",iconClass:"bg-yellow",icon:"📚",route:"strategy-intro"},{title:"新手指南",iconClass:"bg-brown",icon:"🧭",route:"beginner-guide"}]),v=y([]),f=y([]),h=async()=>{try{const a=await x.get("/announcements/active");a.data.success&&a.data.data&&(f.value=a.data.data,console.log("公告数据加载成功:",f.value))}catch(a){console.error("获取公告数据失败:",a)}},k=async()=>{try{const a=await x.get("/ranking/home");a.data.success&&a.data.data&&a.data.data.length>0?(v.value=a.data.data.map(o=>({name:o.name,initial:o.initial,value:o.dailyEarnings,total:o.totalEarnings,color:o.color})),console.log("排行榜数据加载成功:",v.value)):e()}catch(a){console.error("获取排行榜数据失败:",a),e()}},e=()=>{v.value=[{name:"MJ",initial:"M",value:7017.33,total:33956.29,color:"#ffd700"},{name:"小白兔财富自由",initial:"小",value:269.5,total:2474.27,color:"#c0c0c0"},{name:"流水已去",initial:"流",value:28.79,total:171.01,color:"#cd7f32"}],console.log("使用默认排行榜数据:",v.value)},b=C(()=>v.value.slice(0,3).map(a=>({...a,value:a.value.toFixed(2)+"U",total:a.total.toFixed(2)+" U"}))),R=()=>{v.value.forEach(a=>{const o=(Math.random()-.3)*150;a.value+=o,o>0&&(a.total+=o),a.value<0&&(a.value=Math.abs(a.value)*.1),a.total<0&&(a.total=Math.abs(a.total))})},g=y({totalUsers:1234,totalTrades:856,totalProfit:45678.9});let _=null;const M=a=>a>=1e3?a.toLocaleString():a.toString(),L=a=>a.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}),T=C(()=>({totalUsers:M(g.value.totalUsers),totalTrades:M(g.value.totalTrades),totalProfit:L(g.value.totalProfit)})),P=()=>{Math.random()<.3&&(g.value.totalUsers+=Math.floor(Math.random()*3)+1),Math.random()<.6&&(g.value.totalTrades+=Math.floor(Math.random()*6));const a=(Math.random()-.5)*200;g.value.totalProfit+=a,g.value.totalProfit<0&&(g.value.totalProfit=Math.abs(g.value.totalProfit)),R()},A=a=>{let o="";switch(a){case 0:o="crown-gold";break;case 1:o="crown-silver";break;case 2:o="crown-bronze";break;default:o="crown-hidden"}return console.log(`排名 ${a+1} 的皇冠样式类: ${o}`),o},I=a=>{switch(a){case 0:return"ranking-card-gold";case 1:return"ranking-card-silver";case 2:return"ranking-card-bronze";default:return""}},N=a=>{a==="exchange-register"?d.push({name:"exchange-register"}):a==="api-intro"?d.push({name:"api-intro"}):a==="strategy-intro"?d.push({name:"strategy-intro"}):a==="beginner-guide"?d.push({name:"beginner-guide"}):a==="invite-friends"?d.push({name:"invite-friends"}):a==="my-earnings"?d.push({name:"my-earnings"}):alert("该功能正在开发中...")},F=()=>{d.push({name:"ranking"})},V=a=>({none:"无会员",monthly:"月会员",quarterly:"季会员",yearly:"年会员",lifetime:"永久会员"})[a]||"未知",D=a=>`membership-${a}`,z=a=>({info:"fas fa-info-circle",warning:"fas fa-exclamation-triangle",success:"fas fa-check-circle",error:"fas fa-times-circle"})[a]||"fas fa-info-circle";return B(()=>{h(),e(),k(),_=setInterval(P,15e3)}),q(()=>{_&&(clearInterval(_),_=null)}),{currentUser:t,handleLogout:w,cards:n,announcements:f,displayRankings:b,displayStats:T,getCrownClass:A,getRankingCardClass:I,navigateTo:N,navigateToRanking:F,getAnnouncementIcon:z,getMembershipLevelName:V,getMembershipClass:D}}},j={class:"home-view"},G={class:"header"},O={class:"header-content"},K={class:"logo-container"},Q={class:"main-content"},W={key:0,class:"announcements-section"},X={class:"announcement-icon"},Y={class:"announcement-content"},Z={class:"announcement-title"},$={class:"announcement-text"},ss={key:0,class:"announcement-pin"},as={class:"card-grid"},ns=["onClick"],ts={class:"card-icon-wrapper"},es={key:1,class:"icon-emoji"},is={class:"card-title"},os={class:"stats-section"},ls={class:"stats-grid"},rs={class:"stat-item stat-users"},cs={class:"stat-content"},ds={class:"stat-value"},gs={class:"stat-item stat-trades"},vs={class:"stat-content"},ms={class:"stat-value"},us={class:"stat-item stat-profit"},fs={class:"stat-content"},hs={class:"stat-value"},ks={class:"ranking-section"},_s={class:"ranking-header"},ys={class:"ranking-podium"},bs={class:"ranking-card-header"},Cs={class:"ranking-name"},ps={class:"ranking-value"},ws={class:"ranking-total"},Rs={class:"ranking-card-header"},Ms={class:"ranking-name"},Us={class:"ranking-value"},Ss={class:"ranking-total"},xs={class:"ranking-card-header"},Ls={class:"ranking-name"},Ts={class:"ranking-value"},Ps={class:"ranking-total"};function As(d,t,w,n,v,f){var h,k;return l(),r("div",j,[s("header",G,[s("div",O,[s("div",K,[s("h1",null,[u(" Hi, "+i(((h=n.currentUser)==null?void 0:h.username)||"Guest")+" ",1),(k=n.currentUser)!=null&&k.membershipLevel&&n.currentUser.membershipLevel!=="none"?(l(),r("span",{key:0,class:c(["membership-badge",n.getMembershipClass(n.currentUser.membershipLevel)])},i(n.getMembershipLevelName(n.currentUser.membershipLevel)),3)):m("",!0)]),t[2]||(t[2]=s("p",null,"欢迎使用AACoin",-1))]),s("div",{class:"login-button",onClick:t[0]||(t[0]=(...e)=>n.handleLogout&&n.handleLogout(...e))},t[3]||(t[3]=[s("i",{class:"fas fa-sign-out-alt"},null,-1),u(" 退出登录 ")]))])]),s("main",Q,[n.announcements.length>0?(l(),r("div",W,[(l(!0),r(U,null,S(n.announcements,e=>(l(),r("div",{key:e._id,class:c(["announcement-item",`announcement-${e.type}`])},[s("div",X,[s("i",{class:c(n.getAnnouncementIcon(e.type))},null,2)]),s("div",Y,[s("div",Z,i(e.title),1),s("div",$,i(e.content),1)]),e.isPinned?(l(),r("div",ss,t[4]||(t[4]=[s("i",{class:"fas fa-thumbtack"},null,-1)]))):m("",!0)],2))),128))])):m("",!0),s("div",as,[(l(!0),r(U,null,S(n.cards,(e,b)=>(l(),r("div",{class:"card",key:b,onClick:R=>n.navigateTo(e.route)},[s("div",ts,[s("div",{class:c(["card-icon",e.iconClass])},[e.fontAwesome?(l(),r("i",{key:0,class:c([e.fontAwesome,"icon-fa"])},null,2)):(l(),r("span",es,i(e.icon),1))],2)]),s("div",is,i(e.title),1)],8,ns))),128))]),s("div",os,[t[11]||(t[11]=s("div",{class:"section-title"},[s("i",{class:"fas fa-chart-pie"}),u(" 今日概览 ")],-1)),s("div",ls,[s("div",rs,[t[6]||(t[6]=s("div",{class:"stat-icon"},[s("i",{class:"fas fa-users"})],-1)),s("div",cs,[s("div",ds,i(n.displayStats.totalUsers),1),t[5]||(t[5]=s("div",{class:"stat-label"},"活跃用户",-1))])]),s("div",gs,[t[8]||(t[8]=s("div",{class:"stat-icon"},[s("i",{class:"fas fa-chart-line"})],-1)),s("div",vs,[s("div",ms,i(n.displayStats.totalTrades),1),t[7]||(t[7]=s("div",{class:"stat-label"},"今日交易",-1))])]),s("div",us,[t[10]||(t[10]=s("div",{class:"stat-icon"},[s("i",{class:"fas fa-dollar-sign"})],-1)),s("div",fs,[s("div",hs,i(n.displayStats.totalProfit),1),t[9]||(t[9]=s("div",{class:"stat-label"},"总收益(U)",-1))])])])]),s("div",ks,[t[16]||(t[16]=s("div",{class:"section-title"},[s("i",{class:"fas fa-trophy"}),u(" 收益排行 ")],-1)),s("div",_s,[s("div",{class:"more-link",onClick:t[1]||(t[1]=(...e)=>n.navigateToRanking&&n.navigateToRanking(...e))},t[12]||(t[12]=[u("更多 "),s("i",{class:"fas fa-chevron-right"},null,-1)]))]),s("div",ys,[n.displayRankings[1]?(l(),r("div",{key:0,class:c(["ranking-card ranking-card-second",n.getRankingCardClass(1)])},[s("div",bs,[s("div",{class:c(["crown-icon",n.getCrownClass(1)])},t[13]||(t[13]=[s("i",{class:"fas fa-crown"},null,-1)]),2),s("div",{class:"ranking-icon",style:p({backgroundColor:n.displayRankings[1].color})},i(n.displayRankings[1].initial),5)]),s("div",Cs,i(n.displayRankings[1].name),1),s("div",ps,i(n.displayRankings[1].value),1),s("div",ws,"累计收益: "+i(n.displayRankings[1].total),1)],2)):m("",!0),n.displayRankings[0]?(l(),r("div",{key:1,class:c(["ranking-card ranking-card-first",n.getRankingCardClass(0)])},[s("div",Rs,[s("div",{class:c(["crown-icon",n.getCrownClass(0)])},t[14]||(t[14]=[s("i",{class:"fas fa-crown"},null,-1)]),2),s("div",{class:"ranking-icon",style:p({backgroundColor:n.displayRankings[0].color})},i(n.displayRankings[0].initial),5)]),s("div",Ms,i(n.displayRankings[0].name),1),s("div",Us,i(n.displayRankings[0].value),1),s("div",Ss,"累计收益: "+i(n.displayRankings[0].total),1)],2)):m("",!0),n.displayRankings[2]?(l(),r("div",{key:2,class:c(["ranking-card ranking-card-third",n.getRankingCardClass(2)])},[s("div",xs,[s("div",{class:c(["crown-icon",n.getCrownClass(2)])},t[15]||(t[15]=[s("i",{class:"fas fa-crown"},null,-1)]),2),s("div",{class:"ranking-icon",style:p({backgroundColor:n.displayRankings[2].color})},i(n.displayRankings[2].initial),5)]),s("div",Ls,i(n.displayRankings[2].name),1),s("div",Ts,i(n.displayRankings[2].value),1),s("div",Ps,"累计收益: "+i(n.displayRankings[2].total),1)],2)):m("",!0)])])])])}const Ns=E(J,[["render",As],["__scopeId","data-v-883be6b2"]]);export{Ns as default};
