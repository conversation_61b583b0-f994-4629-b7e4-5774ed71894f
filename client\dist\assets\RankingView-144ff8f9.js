import{_ as I,u as z,r as v,l as F,o as N,c as r,d as l,e as a,g as _,F as b,p as h,t as c,k as U,n as u,i as x,q as M}from"./index-a2cd3c28.js";const q={name:"RankingView",setup(){const m=z(),s=v(!1),g=v(""),n=v("daily"),f=v([]),y=v([{label:"日榜",value:"daily",icon:"fas fa-calendar-day"},{label:"月榜",value:"monthly",icon:"fas fa-calendar-alt"},{label:"总榜",value:"total",icon:"fas fa-trophy"}]),e=async(t="daily")=>{try{s.value=!0,g.value="";const d=await U.get(`/ranking?type=${t}`);if(d.data.success){const k=d.data.data.map(i=>({id:i._id,name:i.name,initial:i.initial,earnings:o(i,t),color:i.color,level:i.level||"LV.1"}));k.sort((i,T)=>T.earnings-i.earnings),f.value=k,console.log(`获取${t}排行榜数据成功:`,k)}else g.value="获取排行榜数据失败"}catch(d){console.error("获取排行榜数据失败:",d),g.value="获取排行榜数据失败",f.value=[]}finally{s.value=!1}},o=(t,d)=>{switch(d){case"monthly":return t.monthlyEarnings||0;case"total":return t.totalEarnings||0;default:return t.dailyEarnings||0}},w=()=>{m.go(-1)},C=F(()=>f.value||[]),D=async t=>{n.value=t,await e(t)},R=t=>{switch(t){case 0:return"crown-gold";case 1:return"crown-silver";case 2:return"crown-bronze";default:return"crown-hidden"}},E=t=>{switch(t){case 0:return"ranking-item-gold";case 1:return"ranking-item-silver";case 2:return"ranking-item-bronze";default:return""}},V=t=>({"earnings-positive":t>0,"earnings-negative":t<0,"earnings-neutral":t===0}),p=t=>{switch(t){case 0:return"fas fa-medal";case 1:return"fas fa-award";case 2:return"fas fa-star";default:return""}},B=t=>t===0?"0.00 USDT":`${t>0?"+":""}${Math.abs(t).toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})} USDT`,L=()=>{switch(n.value){case"daily":return"今日收益";case"monthly":return"本月收益";case"total":return"累计收益";default:return"收益"}},S=async()=>{await e(n.value)};return N(async()=>{await e("daily")}),{goBack:w,loading:s,error:g,activeTab:n,tabs:y,currentRankingData:C,getCrownClass:R,getRankingItemClass:E,getEarningsClass:V,getRewardIcon:p,formatEarnings:B,getEarningsLabel:L,refreshData:S,handleTabChange:D}}},j={class:"ranking-view"},A={class:"header"},G={class:"header-content"},H={class:"main-content"},J={class:"tab-section"},K={class:"tab-buttons"},O=["onClick"],P={class:"ranking-content"},Q={key:0,class:"loading-state"},W={key:1,class:"error-state"},X={key:2,class:"ranking-list"},Y={class:"rank-number"},Z={class:"rank-text"},$={class:"user-info"},aa={class:"user-details"},na={class:"user-name"},sa={class:"user-level"},ta={class:"earnings-info"},ea={class:"earnings-label"},ra={key:0,class:"reward-icon"},la={key:0,class:"empty-state"};function oa(m,s,g,n,f,y){return r(),l("div",j,[a("header",A,[a("div",G,[a("button",{class:"back-button",onClick:s[0]||(s[0]=(...e)=>n.goBack&&n.goBack(...e))},s[3]||(s[3]=[a("i",{class:"fas fa-arrow-left"},null,-1)])),s[5]||(s[5]=a("h1",null,"收益排行榜",-1)),n.loading?_("",!0):(r(),l("button",{key:0,onClick:s[1]||(s[1]=(...e)=>n.refreshData&&n.refreshData(...e)),class:"refresh-button",title:"刷新数据"},s[4]||(s[4]=[a("i",{class:"fas fa-sync-alt"},null,-1)])))])]),a("main",H,[a("div",J,[a("div",K,[(r(!0),l(b,null,h(n.tabs,e=>(r(),l("button",{key:e.value,onClick:o=>n.handleTabChange(e.value),class:u([{active:n.activeTab===e.value},"tab-button"])},[a("i",{class:u(e.icon)},null,2),x(" "+c(e.label),1)],10,O))),128))])]),a("div",P,[n.loading?(r(),l("div",Q,s[6]||(s[6]=[a("i",{class:"fas fa-spinner fa-spin"},null,-1),a("span",null,"加载中...",-1)]))):n.error?(r(),l("div",W,[s[7]||(s[7]=a("i",{class:"fas fa-exclamation-triangle"},null,-1)),a("span",null,c(n.error),1),a("button",{onClick:s[2]||(s[2]=(...e)=>n.refreshData&&n.refreshData(...e)),class:"retry-button"},"重试")])):(r(),l("div",X,[(r(!0),l(b,null,h(n.currentRankingData,(e,o)=>(r(),l("div",{key:e.id||o,class:u(["ranking-item",n.getRankingItemClass(o)])},[a("div",Y,[o<3?(r(),l("div",{key:0,class:u(["crown-icon",n.getCrownClass(o)])},s[8]||(s[8]=[a("i",{class:"fas fa-crown"},null,-1)]),2)):_("",!0),a("span",Z,c(o+1),1)]),a("div",$,[a("div",{class:"user-avatar",style:M({backgroundColor:e.color})},c(e.initial),5),a("div",aa,[a("div",na,c(e.name),1),a("div",sa,c(e.level||"LV.1"),1)])]),a("div",ta,[a("div",{class:u(["earnings-amount",n.getEarningsClass(e.earnings)])},c(n.formatEarnings(e.earnings)),3),a("div",ea,c(n.getEarningsLabel()),1)]),o<3?(r(),l("div",ra,[a("i",{class:u(n.getRewardIcon(o))},null,2)])):_("",!0)],2))),128)),n.currentRankingData.length===0?(r(),l("div",la,s[9]||(s[9]=[a("i",{class:"fas fa-trophy"},null,-1),a("p",null,"暂无排行数据",-1),a("small",null,"开始交易后将显示在排行榜中",-1)]))):_("",!0)]))])])])}const ca=I(q,[["render",oa],["__scopeId","data-v-bb0bc50c"]]);export{ca as default};
