import{_ as g,u,r as f,c,d as v,e as t,n as e,i as n,C as o,g as l}from"./index-a2cd3c28.js";const m={name:"ExchangeRegisterView",setup(){const i=u(),a=f("binance");return{activeTab:a,goBack:()=>{i.go(-1)},navigateToApiSettings:()=>{i.push({name:"user"})},navigateToStrategies:()=>{a.value==="binance"?i.push({name:"binance"}):i.push({name:"okx"})}}}},h={class:"exchange-register-view"},b={class:"header"},x={class:"header-content"},A={class:"main-content"},k={class:"exchange-tabs"},I={key:0,class:"tutorial-content"},P={key:1,class:"tutorial-content"},T={class:"next-steps"},q={class:"next-step-cards"};function B(i,a,r,d,p,K){return c(),v("div",h,[t("header",b,[t("div",x,[t("button",{class:"back-button",onClick:a[0]||(a[0]=(...s)=>d.goBack&&d.goBack(...s))},a[5]||(a[5]=[t("i",{class:"fas fa-arrow-left"},null,-1)])),a[6]||(a[6]=t("h1",null,"注册交易所",-1))])]),t("main",A,[a[14]||(a[14]=t("div",{class:"intro-section"},[t("div",{class:"intro-card"},[t("h2",null,"🎯 为什么需要注册交易所？"),t("p",null,"要使用AACoin的自动化交易策略，您需要先在支持的交易所注册账户并获取API密钥。我们支持以下两个主流交易所：")])],-1)),t("div",k,[t("button",{class:e(["tab-button",{active:d.activeTab==="binance"}]),onClick:a[1]||(a[1]=s=>d.activeTab="binance")},a[7]||(a[7]=[t("i",{class:"fas fa-coins"},null,-1),n(" 币安 (Binance) ")]),2),t("button",{class:e(["tab-button",{active:d.activeTab==="okx"}]),onClick:a[2]||(a[2]=s=>d.activeTab="okx")},a[8]||(a[8]=[t("i",{class:"fas fa-exchange-alt"},null,-1),n(" OKX ")]),2)]),d.activeTab==="binance"?(c(),v("div",I,a[9]||(a[9]=[o('<div class="exchange-info-card" data-v-3dac7770><div class="exchange-header" data-v-3dac7770><img src="https://bin.bnbstatic.com/static/images/common/favicon.ico" alt="Binance" class="exchange-logo" data-v-3dac7770><div data-v-3dac7770><h3 data-v-3dac7770>币安 (Binance)</h3><p data-v-3dac7770>全球最大的加密货币交易所</p></div></div><div class="features" data-v-3dac7770><div class="feature-item" data-v-3dac7770><i class="fas fa-shield-alt" data-v-3dac7770></i><span data-v-3dac7770>安全可靠</span></div><div class="feature-item" data-v-3dac7770><i class="fas fa-chart-line" data-v-3dac7770></i><span data-v-3dac7770>流动性高</span></div><div class="feature-item" data-v-3dac7770><i class="fas fa-coins" data-v-3dac7770></i><span data-v-3dac7770>币种丰富</span></div></div><div class="register-button-container" data-v-3dac7770><a href="https://www.binance.com/zh-CN/register" target="_blank" class="register-button" data-v-3dac7770><i class="fas fa-external-link-alt" data-v-3dac7770></i> 前往币安官网注册 </a></div></div><div class="tutorial-steps" data-v-3dac7770><h3 data-v-3dac7770>📋 币安注册步骤</h3><div class="step-list" data-v-3dac7770><div class="step-item" data-v-3dac7770><div class="step-number" data-v-3dac7770>1</div><div class="step-content" data-v-3dac7770><h4 data-v-3dac7770>访问币安官网</h4><p data-v-3dac7770>点击上方按钮访问币安官网，选择&quot;注册&quot;</p></div></div><div class="step-item" data-v-3dac7770><div class="step-number" data-v-3dac7770>2</div><div class="step-content" data-v-3dac7770><h4 data-v-3dac7770>填写注册信息</h4><p data-v-3dac7770>输入邮箱地址和安全密码，完成邮箱验证</p></div></div><div class="step-item" data-v-3dac7770><div class="step-number" data-v-3dac7770>3</div><div class="step-content" data-v-3dac7770><h4 data-v-3dac7770>身份认证</h4><p data-v-3dac7770>根据当地法规要求完成身份认证（KYC）</p></div></div><div class="step-item" data-v-3dac7770><div class="step-number" data-v-3dac7770>4</div><div class="step-content" data-v-3dac7770><h4 data-v-3dac7770>启用双重验证</h4><p data-v-3dac7770>建议启用Google Authenticator或短信验证</p></div></div></div></div><div class="api-guide" data-v-3dac7770><h3 data-v-3dac7770>🔑 API密钥获取指南</h3><div class="api-steps" data-v-3dac7770><div class="api-step" data-v-3dac7770><strong data-v-3dac7770>步骤1：</strong> 登录币安账户，进入&quot;API管理&quot;页面 </div><div class="api-step" data-v-3dac7770><strong data-v-3dac7770>步骤2：</strong> 点击&quot;创建API&quot;，输入API标签名称 </div><div class="api-step" data-v-3dac7770><strong data-v-3dac7770>步骤3：</strong> 启用&quot;现货和杠杆交易&quot;权限 </div><div class="api-step" data-v-3dac7770><strong data-v-3dac7770>步骤4：</strong> 复制API Key和Secret Key到AACoin平台 </div></div><div class="warning-box" data-v-3dac7770><i class="fas fa-exclamation-triangle" data-v-3dac7770></i><p data-v-3dac7770><strong data-v-3dac7770>安全提醒：</strong> 请妥善保管您的API密钥，不要泄露给他人。建议设置IP白名单限制访问。</p></div></div>',3)]))):l("",!0),d.activeTab==="okx"?(c(),v("div",P,a[10]||(a[10]=[o('<div class="exchange-info-card" data-v-3dac7770><div class="exchange-header" data-v-3dac7770><img src="https://static.okx.com/cdn/assets/imgs/MjAyMTA0/6B7B1F8FD9B77B6F3D3F8F8F8F8F8F8F.png" alt="OKX" class="exchange-logo" data-v-3dac7770><div data-v-3dac7770><h3 data-v-3dac7770>OKX</h3><p data-v-3dac7770>领先的数字资产交易平台</p></div></div><div class="features" data-v-3dac7770><div class="feature-item" data-v-3dac7770><i class="fas fa-rocket" data-v-3dac7770></i><span data-v-3dac7770>技术先进</span></div><div class="feature-item" data-v-3dac7770><i class="fas fa-globe" data-v-3dac7770></i><span data-v-3dac7770>全球服务</span></div><div class="feature-item" data-v-3dac7770><i class="fas fa-tools" data-v-3dac7770></i><span data-v-3dac7770>工具丰富</span></div></div><div class="register-button-container" data-v-3dac7770><a href="https://www.okx.com/join" target="_blank" class="register-button" data-v-3dac7770><i class="fas fa-external-link-alt" data-v-3dac7770></i> 前往OKX官网注册 </a></div></div><div class="tutorial-steps" data-v-3dac7770><h3 data-v-3dac7770>📋 OKX注册步骤</h3><div class="step-list" data-v-3dac7770><div class="step-item" data-v-3dac7770><div class="step-number" data-v-3dac7770>1</div><div class="step-content" data-v-3dac7770><h4 data-v-3dac7770>访问OKX官网</h4><p data-v-3dac7770>点击上方按钮访问OKX官网，选择&quot;注册&quot;</p></div></div><div class="step-item" data-v-3dac7770><div class="step-number" data-v-3dac7770>2</div><div class="step-content" data-v-3dac7770><h4 data-v-3dac7770>创建账户</h4><p data-v-3dac7770>使用手机号或邮箱注册，设置安全密码</p></div></div><div class="step-item" data-v-3dac7770><div class="step-number" data-v-3dac7770>3</div><div class="step-content" data-v-3dac7770><h4 data-v-3dac7770>完成验证</h4><p data-v-3dac7770>完成手机/邮箱验证和身份认证</p></div></div><div class="step-item" data-v-3dac7770><div class="step-number" data-v-3dac7770>4</div><div class="step-content" data-v-3dac7770><h4 data-v-3dac7770>安全设置</h4><p data-v-3dac7770>设置资金密码和双重验证保护账户安全</p></div></div></div></div><div class="api-guide" data-v-3dac7770><h3 data-v-3dac7770>🔑 API密钥获取指南</h3><div class="api-steps" data-v-3dac7770><div class="api-step" data-v-3dac7770><strong data-v-3dac7770>步骤1：</strong> 登录OKX账户，进入&quot;API&quot;设置页面 </div><div class="api-step" data-v-3dac7770><strong data-v-3dac7770>步骤2：</strong> 点击&quot;创建V5 API Key&quot; </div><div class="api-step" data-v-3dac7770><strong data-v-3dac7770>步骤3：</strong> 设置API权限（交易、读取） </div><div class="api-step" data-v-3dac7770><strong data-v-3dac7770>步骤4：</strong> 获取API Key、Secret Key和Passphrase </div></div><div class="warning-box" data-v-3dac7770><i class="fas fa-exclamation-triangle" data-v-3dac7770></i><p data-v-3dac7770><strong data-v-3dac7770>安全提醒：</strong> API密钥具有交易权限，请确保安全保存。建议绑定IP地址限制访问范围。</p></div></div>',3)]))):l("",!0),t("div",T,[a[13]||(a[13]=t("h3",null,"🚀 注册完成后的下一步",-1)),t("div",q,[t("div",{class:"next-step-card",onClick:a[3]||(a[3]=(...s)=>d.navigateToApiSettings&&d.navigateToApiSettings(...s))},a[11]||(a[11]=[t("i",{class:"fas fa-key"},null,-1),t("h4",null,"配置API密钥",-1),t("p",null,"在AACoin中添加您的交易所API密钥",-1)])),t("div",{class:"next-step-card",onClick:a[4]||(a[4]=(...s)=>d.navigateToStrategies&&d.navigateToStrategies(...s))},a[12]||(a[12]=[t("i",{class:"fas fa-chart-line"},null,-1),t("h4",null,"选择交易策略",-1),t("p",null,"浏览并启用适合您的自动化交易策略",-1)]))])])])])}const C=g(m,[["render",B],["__scopeId","data-v-3dac7770"]]);export{C as default};
