import{_ as C,u as k,r,c as l,d as n,e as a,w as D,f as u,h as f,n as c,i as m,t as h,g as y,k as x}from"./index-a2cd3c28.js";const V={name:"ChangePasswordView",setup(){const d=k(),s=r({currentPassword:"",newPassword:"",confirmPassword:""}),v=r(!1),e=r(!1),P=r(!1),t=r(!1),o=r(""),w=r(""),_=()=>{d.go(-1)},b=()=>(o.value="",s.value.currentPassword?s.value.newPassword?s.value.newPassword.length<6?(o.value="新密码长度至少6位",!1):s.value.confirmPassword?s.value.newPassword!==s.value.confirmPassword?(o.value="两次输入的新密码不一致",!1):s.value.currentPassword===s.value.newPassword?(o.value="新密码不能与当前密码相同",!1):!0:(o.value="请确认新密码",!1):(o.value="请输入新密码",!1):(o.value="请输入当前密码",!1));return{formData:s,showCurrentPassword:v,showNewPassword:e,showConfirmPassword:P,loading:t,errorMessage:o,successMessage:w,goBack:_,handleSubmit:async()=>{var p,g;if(b()){t.value=!0,o.value="",w.value="";try{(await x.put("/auth/change-password",{currentPassword:s.value.currentPassword,newPassword:s.value.newPassword})).data.success&&(w.value="密码修改成功！",s.value={currentPassword:"",newPassword:"",confirmPassword:""},setTimeout(()=>{d.go(-1)},2e3))}catch(i){console.error("修改密码失败:",i),(g=(p=i.response)==null?void 0:p.data)!=null&&g.error?o.value=i.response.data.error:o.value="修改密码失败，请重试"}finally{t.value=!1}}}}}},M={class:"change-password-view"},N={class:"header"},B={class:"content"},S={class:"form-container"},q={class:"form-group"},U={class:"input-wrapper"},T=["type"],z={class:"form-group"},E={class:"input-wrapper"},F=["type"],I={class:"form-group"},R={class:"input-wrapper"},j=["type"],A={key:0,class:"error-message"},G={key:1,class:"success-message"},H=["disabled"],J={key:0,class:"fas fa-spinner fa-spin"},K={key:1};function L(d,s,v,e,P,t){return l(),n("div",M,[a("div",N,[a("i",{class:"fas fa-arrow-left back-button",onClick:s[0]||(s[0]=(...o)=>e.goBack&&e.goBack(...o))}),s[8]||(s[8]=a("div",{class:"title"},"修改密码",-1)),s[9]||(s[9]=a("div",{class:"placeholder"},null,-1))]),a("div",B,[a("div",S,[a("form",{onSubmit:s[7]||(s[7]=D((...o)=>e.handleSubmit&&e.handleSubmit(...o),["prevent"]))},[a("div",q,[s[10]||(s[10]=a("label",{class:"form-label"},"当前密码",-1)),a("div",U,[u(a("input",{"onUpdate:modelValue":s[1]||(s[1]=o=>e.formData.currentPassword=o),type:e.showCurrentPassword?"text":"password",class:"form-input",placeholder:"请输入当前密码",required:""},null,8,T),[[f,e.formData.currentPassword]]),a("i",{class:c([e.showCurrentPassword?"fas fa-eye-slash":"fas fa-eye","password-toggle"]),onClick:s[2]||(s[2]=o=>e.showCurrentPassword=!e.showCurrentPassword)},null,2)])]),a("div",z,[s[11]||(s[11]=a("label",{class:"form-label"},"新密码",-1)),a("div",E,[u(a("input",{"onUpdate:modelValue":s[3]||(s[3]=o=>e.formData.newPassword=o),type:e.showNewPassword?"text":"password",class:"form-input",placeholder:"请输入新密码（至少6位）",required:""},null,8,F),[[f,e.formData.newPassword]]),a("i",{class:c([e.showNewPassword?"fas fa-eye-slash":"fas fa-eye","password-toggle"]),onClick:s[4]||(s[4]=o=>e.showNewPassword=!e.showNewPassword)},null,2)])]),a("div",I,[s[12]||(s[12]=a("label",{class:"form-label"},"确认新密码",-1)),a("div",R,[u(a("input",{"onUpdate:modelValue":s[5]||(s[5]=o=>e.formData.confirmPassword=o),type:e.showConfirmPassword?"text":"password",class:"form-input",placeholder:"请再次输入新密码",required:""},null,8,j),[[f,e.formData.confirmPassword]]),a("i",{class:c([e.showConfirmPassword?"fas fa-eye-slash":"fas fa-eye","password-toggle"]),onClick:s[6]||(s[6]=o=>e.showConfirmPassword=!e.showConfirmPassword)},null,2)])]),e.errorMessage?(l(),n("div",A,[s[13]||(s[13]=a("i",{class:"fas fa-exclamation-circle"},null,-1)),m(" "+h(e.errorMessage),1)])):y("",!0),e.successMessage?(l(),n("div",G,[s[14]||(s[14]=a("i",{class:"fas fa-check-circle"},null,-1)),m(" "+h(e.successMessage),1)])):y("",!0),a("button",{type:"submit",class:"submit-button",disabled:e.loading},[e.loading?(l(),n("i",J)):(l(),n("span",K,"确认修改"))],8,H)],32),s[15]||(s[15]=a("div",{class:"security-tips"},[a("div",{class:"tips-title"},[a("i",{class:"fas fa-shield-alt"}),m(" 密码安全提示 ")]),a("ul",{class:"tips-list"},[a("li",null,"密码长度至少6位字符"),a("li",null,"建议包含字母、数字和特殊字符"),a("li",null,"不要使用过于简单的密码"),a("li",null,"定期更换密码以保障账户安全")])],-1))])])])}const W=C(V,[["render",L],["__scopeId","data-v-ce206d1c"]]);export{W as default};
