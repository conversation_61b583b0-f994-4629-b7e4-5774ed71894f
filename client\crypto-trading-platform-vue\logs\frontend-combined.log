C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
C:\PROGRAM FILES\NODEJS\NPM.CMD:1
:: Created by npm, please don't edit manually.
^

SyntaxError: Unexpected token ':'
    at wrapSafe (node:internal/modules/cjs/loader:1662:18)
    at Module._compile (node:internal/modules/cjs/loader:1704:20)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\ProcessContainer.js:292:27
    at wrapper (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\once.js:12:16)
    at next (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\waterfall.js:96:20)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\node_modules\async\internal\onlyOnce.js:12:16
    at WriteStream.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pm2\lib\Utility.js:186:13)
