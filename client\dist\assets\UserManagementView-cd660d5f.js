import{_ as J,u as O,r as u,l as R,o as G,c as i,d as n,e as s,f as b,v as F,n as I,F as K,p as W,t as m,g as v,B as X,C as Y,j as V,i as g,w as h,H as U}from"./index-a2cd3c28.js";const Z={name:"UserManagementView",setup(){const _=O(),e=u([]),p=u(!1),l=u(""),c=u({page:1,limit:20,total:0,pages:0}),y=u(!1),o=u({}),d=u({membershipLevel:"none",duration:null,isActive:!0,isAdmin:!1}),M=u(!1),A=u(!1),k=u({}),C=u(!1),S=R(()=>{const t=localStorage.getItem("user"),a=t?JSON.parse(t):null;return(a==null?void 0:a._id)||(a==null?void 0:a.id)});let L=null;const B=()=>{clearTimeout(L),L=setTimeout(()=>{c.value.page=1,f()},500)},f=async()=>{var t;try{p.value=!0;const a=localStorage.getItem("token"),r={page:c.value.page,limit:c.value.limit};l.value.trim()&&(r.search=l.value.trim());const D=await U.get("/api/user/admin/users",{headers:{Authorization:`Bearer ${a}`},params:r});D.data.success&&(e.value=D.data.data.users,c.value=D.data.data.pagination)}catch(a){console.error("加载用户列表失败:",a),((t=a.response)==null?void 0:t.status)===403?(alert("需要管理员权限"),_.push({name:"user"})):alert("加载用户列表失败")}finally{p.value=!1}},E=t=>{c.value.page=t,f()},T=t=>{console.log("选择用户:",t)},N=t=>{o.value=t,d.value={membershipLevel:t.membershipLevel,duration:null,isActive:t.isActive,isAdmin:t.isAdmin},y.value=!0},w=()=>{y.value=!1,o.value={},d.value={membershipLevel:"none",duration:null,isActive:!0,isAdmin:!1}},z=async()=>{var t,a;try{M.value=!0;const r=localStorage.getItem("token");d.value.membershipLevel!==o.value.membershipLevel&&await U.put(`/api/user/admin/users/${o.value.id}/membership`,{membershipLevel:d.value.membershipLevel,duration:d.value.duration},{headers:{Authorization:`Bearer ${r}`}}),(d.value.isActive!==o.value.isActive||d.value.isAdmin!==o.value.isAdmin)&&await U.put(`/api/user/admin/users/${o.value.id}/status`,{isActive:d.value.isActive,isAdmin:d.value.isAdmin},{headers:{Authorization:`Bearer ${r}`}}),alert("用户信息更新成功"),w(),f()}catch(r){console.error("保存用户失败:",r),alert(((a=(t=r.response)==null?void 0:t.data)==null?void 0:a.error)||"保存用户失败")}finally{M.value=!1}},P=t=>({none:"无会员",monthly:"月会员",quarterly:"季会员",yearly:"年会员",lifetime:"永久会员"})[t]||"未知",Q=t=>`membership-${t}`,q=t=>{k.value=t,A.value=!0},x=()=>{A.value=!1,k.value={}},j=async()=>{var t,a;try{C.value=!0;const r=localStorage.getItem("token");await U.delete(`/api/user/admin/users/${k.value.id}`,{headers:{Authorization:`Bearer ${r}`}}),alert("用户删除成功"),x(),f()}catch(r){console.error("删除用户失败:",r),alert(((a=(t=r.response)==null?void 0:t.data)==null?void 0:a.error)||"删除用户失败")}finally{C.value=!1}},H=()=>{_.go(-1)};return G(()=>{f()}),{users:e,loading:p,searchQuery:l,pagination:c,showEditModal:y,selectedUser:o,editForm:d,saving:M,showDeleteModal:A,userToDelete:k,deleting:C,currentUserId:S,handleSearch:B,loadUsers:f,changePage:E,selectUser:T,editUser:N,closeEditModal:w,saveUser:z,confirmDeleteUser:q,closeDeleteModal:x,deleteUser:j,getMembershipLevelName:P,getMembershipClass:Q,goBack:H}}},$={class:"user-management-view"},ee={class:"header"},se={class:"header-content"},le={class:"main-content"},oe={class:"search-section"},te={class:"search-box"},ae=["disabled"],ie={class:"users-section"},ne={key:0,class:"loading-section"},de={key:1,class:"empty-section"},re={key:2,class:"users-list"},me=["onClick"],ue={class:"user-info"},ve={class:"user-name"},ce={class:"user-email"},fe={class:"user-uid"},be={class:"user-status"},ge={class:"user-badges"},pe={key:0,class:"badge admin-badge"},ye={key:1,class:"badge inactive-badge"},ke={key:2,class:"badge member-badge"},he={class:"user-actions"},Ue=["onClick"],_e=["onClick"],Me={key:3,class:"pagination"},Ae=["disabled"],Ce={class:"page-info"},De=["disabled"],Le={class:"modal-header"},we={class:"modal-body"},xe={class:"form-group"},Fe=["value"],Ie={class:"form-group"},Ve=["value"],Se={class:"form-group"},Be=["value"],Ee={class:"form-group"},Te={key:0,class:"form-group"},Ne={class:"form-group"},ze={class:"checkbox-label"},Pe={class:"form-group"},Qe={class:"checkbox-label"},qe=["disabled"],je={class:"modal-footer"},He=["disabled"],Je={key:0,class:"fas fa-spinner fa-spin"},Oe={class:"modal-header"},Re={class:"modal-body"},Ge={class:"user-info-summary"},Ke={class:"info-item"},We={class:"info-item"},Xe={class:"info-item"},Ye={class:"info-item"},Ze={class:"modal-footer"},$e=["disabled"],es={key:0,class:"fas fa-spinner fa-spin"};function ss(_,e,p,l,c,y){return i(),n("div",$,[s("header",ee,[s("div",se,[s("button",{class:"back-button",onClick:e[0]||(e[0]=(...o)=>l.goBack&&l.goBack(...o))},e[20]||(e[20]=[s("i",{class:"fas fa-arrow-left"},null,-1)])),e[21]||(e[21]=s("h1",null,"用户管理",-1))])]),s("main",le,[s("div",oe,[s("div",te,[e[22]||(e[22]=s("i",{class:"fas fa-search"},null,-1)),b(s("input",{type:"text","onUpdate:modelValue":e[1]||(e[1]=o=>l.searchQuery=o),placeholder:"搜索用户名、邮箱或UID...",onInput:e[2]||(e[2]=(...o)=>l.handleSearch&&l.handleSearch(...o))},null,544),[[F,l.searchQuery]])]),s("button",{class:"refresh-button",onClick:e[3]||(e[3]=(...o)=>l.loadUsers&&l.loadUsers(...o)),disabled:l.loading},[s("i",{class:I(["fas fa-sync-alt",{"fa-spin":l.loading}])},null,2)],8,ae)]),s("div",ie,[l.loading&&l.users.length===0?(i(),n("div",ne,e[23]||(e[23]=[s("i",{class:"fas fa-spinner fa-spin"},null,-1),s("p",null,"正在加载用户列表...",-1)]))):l.users.length===0?(i(),n("div",de,e[24]||(e[24]=[s("i",{class:"fas fa-users"},null,-1),s("p",null,"暂无用户数据",-1)]))):(i(),n("div",re,[(i(!0),n(K,null,W(l.users,o=>(i(),n("div",{class:"user-card",key:o.id,onClick:d=>l.selectUser(o)},[e[30]||(e[30]=s("div",{class:"user-avatar"},[s("i",{class:"fas fa-user"})],-1)),s("div",ue,[s("div",ve,m(o.username),1),s("div",ce,m(o.email),1),s("div",fe,"UID: "+m(o.uid),1)]),s("div",be,[s("div",{class:I(["membership-level",l.getMembershipClass(o.membershipLevel)])},m(l.getMembershipLevelName(o.membershipLevel)),3),s("div",ge,[o.isAdmin?(i(),n("span",pe,e[25]||(e[25]=[s("i",{class:"fas fa-crown"},null,-1),g(" 管理员 ")]))):v("",!0),o.isActive?v("",!0):(i(),n("span",ye,e[26]||(e[26]=[s("i",{class:"fas fa-ban"},null,-1),g(" 已禁用 ")]))),o.isValidMember?(i(),n("span",ke,e[27]||(e[27]=[s("i",{class:"fas fa-star"},null,-1),g(" 有效会员 ")]))):v("",!0)])]),s("div",he,[s("button",{class:"action-button edit-button",onClick:h(d=>l.editUser(o),["stop"]),title:"编辑用户"},e[28]||(e[28]=[s("i",{class:"fas fa-edit"},null,-1)]),8,Ue),!o.isAdmin&&o.id!==l.currentUserId?(i(),n("button",{key:0,class:"action-button delete-button",onClick:h(d=>l.confirmDeleteUser(o),["stop"]),title:"删除用户"},e[29]||(e[29]=[s("i",{class:"fas fa-trash"},null,-1)]),8,_e)):v("",!0)])],8,me))),128))])),l.pagination.pages>1?(i(),n("div",Me,[s("button",{class:"page-button",disabled:l.pagination.page<=1,onClick:e[4]||(e[4]=o=>l.changePage(l.pagination.page-1))},e[31]||(e[31]=[s("i",{class:"fas fa-chevron-left"},null,-1)]),8,Ae),s("span",Ce,m(l.pagination.page)+" / "+m(l.pagination.pages),1),s("button",{class:"page-button",disabled:l.pagination.page>=l.pagination.pages,onClick:e[5]||(e[5]=o=>l.changePage(l.pagination.page+1))},e[32]||(e[32]=[s("i",{class:"fas fa-chevron-right"},null,-1)]),8,De)])):v("",!0)])]),l.showEditModal?(i(),n("div",{key:0,class:"modal-overlay",onClick:e[14]||(e[14]=(...o)=>l.closeEditModal&&l.closeEditModal(...o))},[s("div",{class:"modal-content",onClick:e[13]||(e[13]=h(()=>{},["stop"]))},[s("div",Le,[e[34]||(e[34]=s("h3",null,"编辑用户",-1)),s("button",{class:"close-button",onClick:e[6]||(e[6]=(...o)=>l.closeEditModal&&l.closeEditModal(...o))},e[33]||(e[33]=[s("i",{class:"fas fa-times"},null,-1)]))]),s("div",we,[s("div",xe,[e[35]||(e[35]=s("label",null,"用户名",-1)),s("input",{type:"text",value:l.selectedUser.username,readonly:""},null,8,Fe)]),s("div",Ie,[e[36]||(e[36]=s("label",null,"邮箱",-1)),s("input",{type:"text",value:l.selectedUser.email,readonly:""},null,8,Ve)]),s("div",Se,[e[37]||(e[37]=s("label",null,"UID",-1)),s("input",{type:"text",value:l.selectedUser.uid,readonly:""},null,8,Be)]),s("div",Ee,[e[39]||(e[39]=s("label",null,"会员等级",-1)),b(s("select",{"onUpdate:modelValue":e[7]||(e[7]=o=>l.editForm.membershipLevel=o)},e[38]||(e[38]=[Y('<option value="none" data-v-f1ff6833>无会员</option><option value="monthly" data-v-f1ff6833>月会员</option><option value="quarterly" data-v-f1ff6833>季会员</option><option value="yearly" data-v-f1ff6833>年会员</option><option value="lifetime" data-v-f1ff6833>永久会员</option>',5)]),512),[[X,l.editForm.membershipLevel]])]),l.editForm.membershipLevel!=="none"&&l.editForm.membershipLevel!=="lifetime"?(i(),n("div",Te,[e[40]||(e[40]=s("label",null,"会员天数",-1)),b(s("input",{type:"number","onUpdate:modelValue":e[8]||(e[8]=o=>l.editForm.duration=o),placeholder:"自定义天数（可选）",min:"1"},null,512),[[F,l.editForm.duration]])])):v("",!0),s("div",Ne,[s("label",ze,[b(s("input",{type:"checkbox","onUpdate:modelValue":e[9]||(e[9]=o=>l.editForm.isActive=o)},null,512),[[V,l.editForm.isActive]]),e[41]||(e[41]=s("span",null,"账户激活",-1))])]),s("div",Pe,[s("label",Qe,[b(s("input",{type:"checkbox","onUpdate:modelValue":e[10]||(e[10]=o=>l.editForm.isAdmin=o),disabled:l.selectedUser.id===l.currentUserId},null,8,qe),[[V,l.editForm.isAdmin]]),e[42]||(e[42]=s("span",null,"管理员权限",-1))])])]),s("div",je,[s("button",{class:"cancel-button",onClick:e[11]||(e[11]=(...o)=>l.closeEditModal&&l.closeEditModal(...o))},"取消"),s("button",{class:"save-button",onClick:e[12]||(e[12]=(...o)=>l.saveUser&&l.saveUser(...o)),disabled:l.saving},[l.saving?(i(),n("i",Je)):v("",!0),g(" "+m(l.saving?"保存中...":"保存"),1)],8,He)])])])):v("",!0),l.showDeleteModal?(i(),n("div",{key:1,class:"modal-overlay",onClick:e[19]||(e[19]=(...o)=>l.closeDeleteModal&&l.closeDeleteModal(...o))},[s("div",{class:"modal-content delete-modal",onClick:e[18]||(e[18]=h(()=>{},["stop"]))},[s("div",Oe,[e[44]||(e[44]=s("h3",null,"确认删除用户",-1)),s("button",{class:"close-button",onClick:e[15]||(e[15]=(...o)=>l.closeDeleteModal&&l.closeDeleteModal(...o))},e[43]||(e[43]=[s("i",{class:"fas fa-times"},null,-1)]))]),s("div",Re,[e[49]||(e[49]=s("div",{class:"delete-warning"},[s("i",{class:"fas fa-exclamation-triangle"}),s("p",null,"您确定要删除以下用户吗？此操作不可撤销！")],-1)),s("div",Ge,[s("div",Ke,[e[45]||(e[45]=s("label",null,"用户名：",-1)),s("span",null,m(l.userToDelete.username),1)]),s("div",We,[e[46]||(e[46]=s("label",null,"邮箱：",-1)),s("span",null,m(l.userToDelete.email),1)]),s("div",Xe,[e[47]||(e[47]=s("label",null,"UID：",-1)),s("span",null,m(l.userToDelete.uid),1)]),s("div",Ye,[e[48]||(e[48]=s("label",null,"会员等级：",-1)),s("span",null,m(l.getMembershipLevelName(l.userToDelete.membershipLevel)),1)])])]),s("div",Ze,[s("button",{class:"cancel-button",onClick:e[16]||(e[16]=(...o)=>l.closeDeleteModal&&l.closeDeleteModal(...o))},"取消"),s("button",{class:"delete-confirm-button",onClick:e[17]||(e[17]=(...o)=>l.deleteUser&&l.deleteUser(...o)),disabled:l.deleting},[l.deleting?(i(),n("i",es)):v("",!0),g(" "+m(l.deleting?"删除中...":"确认删除"),1)],8,$e)])])])):v("",!0)])}const os=J(Z,[["render",ss],["__scopeId","data-v-f1ff6833"]]);export{os as default};
