<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>清除登录令牌</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            margin: 0;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            width: 90%;
            max-width: 500px;
            text-align: center;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .message {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            opacity: 0.9;
        }
        .info {
            margin-top: 20px;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AACoin 登录令牌修复工具</h1>
        <p>如果您遇到了"Token验证错误"或无法看到运行中的策略，可能是登录令牌过期导致的。</p>
        <p>点击下方按钮清除本地存储的令牌，然后重新登录系统。</p>
        
        <div id="message" class="message" style="display: none;"></div>
        
        <button id="clearButton" class="button">清除令牌并修复</button>
        <button id="loginButton" class="button" style="display: none;">前往登录页面</button>
        
        <div class="info">
            <p>系统时间问题可能导致令牌验证失败。如果问题持续存在，请联系管理员。</p>
        </div>
    </div>

    <script>
        document.getElementById('clearButton').addEventListener('click', function() {
            try {
                // 清除本地存储
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                
                // 显示成功消息
                const messageDiv = document.getElementById('message');
                messageDiv.className = 'message success';
                messageDiv.textContent = '令牌已成功清除！请重新登录系统。';
                messageDiv.style.display = 'block';
                
                // 显示登录按钮
                document.getElementById('loginButton').style.display = 'inline-block';
                this.disabled = true;
            } catch (error) {
                // 显示错误消息
                const messageDiv = document.getElementById('message');
                messageDiv.className = 'message error';
                messageDiv.textContent = '清除令牌时出错：' + error.message;
                messageDiv.style.display = 'block';
            }
        });
        
        document.getElementById('loginButton').addEventListener('click', function() {
            window.location.href = '/login';
        });
    </script>
</body>
</html> 