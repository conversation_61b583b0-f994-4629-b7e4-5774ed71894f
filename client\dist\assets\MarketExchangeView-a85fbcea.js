import{_ as B,r as v,o as F,x as S,c as a,d as r,e,i as _,t as l,g as c,y as b,F as T,p as D,k as f,n as C}from"./index-a2cd3c28.js";import{B as N,a as V}from"./BinanceStrategyForm-3bd746fe.js";import"./strategyPermissionService-52b945bf.js";const L={name:"MarketExchangeView",components:{BinanceApiModal:N,BinanceStrategyForm:V},setup(){const d=v(!1),s=v(""),y=v(!1),n=v(""),u=v([]),h=()=>{const o=localStorage.getItem("user");if(o)try{const i=JSON.parse(o);if(i&&i.uid)return n.value=i.uid,i.uid}catch(i){console.error("解析用户信息失败:",i)}return"default"},g=async()=>{try{console.log("检查币安API密钥状态...");const o=await f.get("/binance/api-keys");o.data.success&&o.data.hasApiKeys?(console.log("币安API密钥已配置:",o.data.apiKeyPreview),d.value=!0,s.value=o.data.apiKeyPreview,localStorage.setItem(`binanceApiConnected_${n.value}`,"true")):(console.log("币安API密钥未配置"),d.value=!1,localStorage.setItem(`binanceApiConnected_${n.value}`,"false"))}catch(o){console.error("检查币安API密钥状态失败:",o),localStorage.getItem(`binanceApiConnected_${n.value}`)==="true"?(console.log("从本地存储加载币安API连接状态: 已连接"),d.value=!0):(console.log("币安API未连接"),d.value=!1)}},m=o=>{console.log("币安API密钥保存成功:",o),d.value=!0,s.value=o.apiKey?`${o.apiKey.substring(0,4)}...${o.apiKey.substring(o.apiKey.length-4)}`:"",localStorage.setItem(`binanceApiConnected_${n.value}`,"true"),t()},t=async()=>{try{console.log("正在获取币安策略列表，用户ID:",n.value);const o=await f.get("/binance/strategies");console.log("币安API响应:",o.data),o.data&&o.data.strategies?(console.log(`获取到 ${o.data.strategies.length} 个币安策略`),u.value=o.data.strategies):console.error("获取币安策略列表失败: 返回数据格式不正确",o.data)}catch(o){console.error("获取币安策略列表失败:",o),o.response&&console.error("错误响应:",o.response.data)}},k=o=>{console.log("币安策略创建成功:",o),u.value.unshift({...o,createdAt:Date.now()})},p=async o=>{try{if(!o){console.error("策略ID不能为空");return}const i=await f.post(`/binance/strategies/${o}/stop`);if(i.data&&i.data.success){const w=u.value.findIndex(A=>A.id===o||A._id===o);w!==-1&&(u.value[w].status="completed"),await t()}}catch(i){console.error("停止币安策略失败:",i)}},x=async o=>{try{if(!o){console.error("策略ID不能为空");return}if(!confirm("确定要删除此币安策略吗？此操作不可撤销。"))return;console.log("删除币安策略:",o);const i=await f.delete(`/binance/strategies/${o}`);i.data&&i.data.success?(console.log("删除币安策略成功"),await t()):console.error("删除币安策略失败:",i.data.error)}catch(i){console.error("删除币安策略失败:",i),i.response&&console.error("错误响应:",i.response.data)}},P=o=>o?new Date(o).toLocaleString():"-",I=o=>{switch(o){case"waiting":return"等待中";case"active":return"运行中";case"completed":return"已完成";case"executing":return"执行中";case"error":return"错误";default:return o||"未知"}},M=o=>{switch(o){case"waiting":return"status-waiting";case"active":return"status-active";case"completed":return"status-completed";case"executing":return"status-executing";case"error":return"status-error";default:return""}},K=(o,i)=>{switch(o){case"long":return i==="spot"?"买入":"做多";case"short":return"做空";case"both":return"自动判断";default:return o||"未知"}};return F(()=>{h(),g(),d.value&&t()}),{apiConnected:d,apiKeyPreview:s,showApiModal:y,userId:n,strategies:u,handleApiSaved:m,fetchStrategies:t,onStrategyCreated:k,stopStrategy:p,deleteStrategy:x,formatDate:P,getStatusText:I,getStatusClass:M,getDirectionText:K}}},E={class:"binance-view"},U={class:"binance-header"},R={class:"connection-status"},z={key:0,class:"status-connected"},J={key:1,class:"status-disconnected"},O={class:"main-content"},j={class:"api-status-card"},q={class:"card-header"},G={class:"card-content"},H={key:0,class:"connected-info"},Q={key:0,class:"api-preview"},W={key:1,class:"disconnected-info"},X={key:0},Y={class:"active-strategies"},Z={key:0,class:"no-strategies"},$={key:1,class:"strategies-list"},ee={class:"strategy-header"},te={class:"strategy-type"},se={key:0,class:"network-error-warning"},oe={class:"strategy-details"},ne={key:0,class:"strategy-item strategy-name"},ie={class:"value"},ae={class:"strategy-item"},re={class:"value"},le={class:"strategy-item"},ce={class:"value"},de={class:"strategy-item"},ue={class:"value"},ve={key:1,class:"strategy-item"},_e={class:"value"},pe={class:"strategy-item"},fe={class:"value"},ge={key:2,class:"strategy-item"},me={class:"value"},ke={key:3,class:"strategy-item"},ye={class:"value risk-value"},he={key:4,class:"strategy-item"},we={class:"value profit-value"},Ae={key:5,class:"strategy-item"},Se={key:0},be={class:"strategy-actions"},Ce={class:"action-buttons"},xe=["onClick"],Pe={key:1,class:"strategy-status-info"},Ie=["onClick"],Me={class:"delete-action"},Ke=["onClick"];function Be(d,s,y,n,u,h){const g=S("BinanceStrategyForm"),m=S("BinanceApiModal");return a(),r("div",E,[e("div",U,[s[5]||(s[5]=e("h2",null,"币安交易",-1)),e("div",R,[n.apiConnected?(a(),r("span",z,s[3]||(s[3]=[e("i",{class:"fas fa-check-circle"},null,-1),_(" 已连接 ")]))):(a(),r("span",J,s[4]||(s[4]=[e("i",{class:"fas fa-times-circle"},null,-1),_(" 未连接 ")])))])]),e("div",O,[e("div",j,[e("div",q,[s[7]||(s[7]=e("h3",null,"API连接状态",-1)),e("button",{class:"config-button",onClick:s[0]||(s[0]=t=>n.showApiModal=!0)},[s[6]||(s[6]=e("i",{class:"fas fa-cog"},null,-1)),_(" "+l(n.apiConnected?"重新配置":"配置API"),1)])]),e("div",G,[n.apiConnected?(a(),r("div",H,[s[8]||(s[8]=e("div",{class:"api-info"},[e("i",{class:"fas fa-key"}),e("span",null,"API密钥已配置")],-1)),n.apiKeyPreview?(a(),r("div",Q,[e("small",null,l(n.apiKeyPreview),1)])):c("",!0)])):(a(),r("div",W,[s[9]||(s[9]=e("i",{class:"fas fa-exclamation-triangle"},null,-1)),s[10]||(s[10]=e("p",null,"请配置您的币安API密钥以开始使用交易功能",-1)),e("button",{class:"connect-button",onClick:s[1]||(s[1]=t=>n.showApiModal=!0)}," 连接币安API ")]))])]),n.apiConnected?(a(),r("div",X,[b(g,{apiConnected:n.apiConnected,userId:n.userId,onStrategyCreated:n.onStrategyCreated},null,8,["apiConnected","userId","onStrategyCreated"]),e("div",Y,[s[22]||(s[22]=e("div",{class:"section-title"},"运行中的币安策略",-1)),n.strategies.length===0?(a(),r("div",Z," 暂无运行中的币安策略 ")):(a(),r("div",$,[(a(!0),r(T,null,D(n.strategies,(t,k)=>(a(),r("div",{key:k,class:"strategy-card"},[e("div",ee,[e("div",te,l(t.type==="spot"?"币安现货策略":"币安合约策略"),1),e("div",{class:C(["strategy-status",n.getStatusClass(t.status)])},l(n.getStatusText(t.status)),3),t.networkStatus==="network_error"?(a(),r("div",se,s[11]||(s[11]=[e("i",{class:"fas fa-exclamation-triangle"},null,-1),_(" 网络连接失败 ")]))):c("",!0)]),e("div",oe,[t.strategyName?(a(),r("div",ne,[e("div",ie,l(t.strategyName),1)])):c("",!0),e("div",ae,[s[12]||(s[12]=e("div",{class:"label"},"交易对",-1)),e("div",re,l(t.symbol),1)]),e("div",le,[s[13]||(s[13]=e("div",{class:"label"},"方向",-1)),e("div",ce,l(n.getDirectionText(t.direction,t.type)),1)]),e("div",de,[s[14]||(s[14]=e("div",{class:"label"},"金额",-1)),e("div",ue,l(t.amount)+" USDT",1)]),t.type==="futures"?(a(),r("div",ve,[s[15]||(s[15]=e("div",{class:"label"},"杠杆",-1)),e("div",_e,l(t.leverage)+"x",1)])):c("",!0),e("div",pe,[s[16]||(s[16]=e("div",{class:"label"},"创建时间",-1)),e("div",fe,l(n.formatDate(t.createdAt)),1)]),t.entryPrice&&t.entryPrice>0?(a(),r("div",ge,[s[17]||(s[17]=e("div",{class:"label"},"入场价格",-1)),e("div",me,l(t.entryPrice.toFixed(2)),1)])):c("",!0),t.stopLoss&&t.stopLoss>0?(a(),r("div",ke,[s[18]||(s[18]=e("div",{class:"label"},"止损价格",-1)),e("div",ye,l(t.stopLoss.toFixed(2)),1)])):c("",!0),t.takeProfit&&t.takeProfit>0?(a(),r("div",he,[s[19]||(s[19]=e("div",{class:"label"},"止盈价格",-1)),e("div",we,l(t.takeProfit.toFixed(2)),1)])):c("",!0),t.profit!==void 0&&t.profit!==0?(a(),r("div",Ae,[s[20]||(s[20]=e("div",{class:"label"},"收益",-1)),e("div",{class:C(["value",{"profit-positive":t.profit>0,"profit-negative":t.profit<0}])},[_(l(t.profit.toFixed(2))+" USDT ",1),t.profitPercentage!==void 0?(a(),r("span",Se," ("+l(t.profitPercentage>0?"+":"")+l(t.profitPercentage.toFixed(2))+"%) ",1)):c("",!0)],2)])):c("",!0),e("div",be,[e("div",Ce,[t.status==="waiting"||t.status==="active"?(a(),r("button",{key:0,class:"stop-button",onClick:p=>n.stopStrategy(t.id||t._id)}," 停止策略 ",8,xe)):t.status!=="error"?(a(),r("div",Pe,l(n.getStatusText(t.status)),1)):c("",!0),t.status==="error"?(a(),r("button",{key:2,class:"delete-button",onClick:p=>n.deleteStrategy(t.id||t._id)}," 删除 ",8,Ie)):c("",!0)]),e("div",Me,[t.status!=="waiting"&&t.status!=="active"?(a(),r("button",{key:0,class:"delete-button-small",title:"删除策略",onClick:p=>n.deleteStrategy(t.id||t._id)},s[21]||(s[21]=[e("span",{class:"delete-icon"},"×",-1)]),8,Ke)):c("",!0)])])])]))),128))]))])])):c("",!0)]),b(m,{show:n.showApiModal,isReconfiguring:n.apiConnected,onClose:s[2]||(s[2]=t=>n.showApiModal=!1),onApiSaved:n.handleApiSaved},null,8,["show","isReconfiguring","onApiSaved"])])}const Ne=B(L,[["render",Be],["__scopeId","data-v-1920678a"]]);export{Ne as default};
