<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理测试 - Sakura FRP</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { background-color: #d4edda; border-left: 4px solid #28a745; }
        .error { background-color: #f8d7da; border-left: 4px solid #dc3545; }
        .info { background-color: #d1ecf1; border-left: 4px solid #17a2b8; }
        .warning { background-color: #fff3cd; border-left: 4px solid #ffc107; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; border: 1px solid #e9ecef; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 代理测试工具 - Sakura FRP</h1>
        
        <div class="card info">
            <h3>📍 当前环境信息</h3>
            <p><strong>访问域名:</strong> <span id="hostname"></span></p>
            <p><strong>访问协议:</strong> <span id="protocol"></span></p>
            <p><strong>完整URL:</strong> <span id="fullUrl"></span></p>
            <p><strong>访问类型:</strong> <span id="accessType"></span></p>
        </div>

        <div class="card">
            <h3>🧪 代理测试</h3>
            <div class="test-grid">
                <button onclick="testDirectProxy()">测试代理 /api/health</button>
                <button onclick="testLoginProxy()">测试登录代理</button>
                <button onclick="testWithFetch()">Fetch API测试</button>
                <button onclick="testWithXHR()">XMLHttpRequest测试</button>
                <button onclick="clearResults()">清除结果</button>
            </div>
        </div>

        <div class="card">
            <h3>📊 测试结果</h3>
            <div id="testResults"></div>
        </div>
    </div>

    <script>
        // 显示当前访问信息
        const hostname = window.location.hostname;
        const protocol = window.location.protocol;
        const fullUrl = window.location.href;
        
        document.getElementById('hostname').textContent = hostname;
        document.getElementById('protocol').textContent = protocol;
        document.getElementById('fullUrl').textContent = fullUrl;
        
        const isLocal = hostname === 'localhost' || hostname === '127.0.0.1';
        document.getElementById('accessType').textContent = isLocal ? '本地访问' : 'Sakura FRP公网访问';
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `card ${type}`;
            div.innerHTML = `<p>${message}</p>`;
            document.getElementById('testResults').appendChild(div);
            div.scrollIntoView({ behavior: 'smooth' });
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
        }

        async function testDirectProxy() {
            addResult('🔄 测试直接代理 /api/health...', 'info');
            
            try {
                const response = await fetch('/api/health', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    addResult(`✅ 代理成功！<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                } else {
                    addResult(`❌ 代理失败: ${response.status} ${response.statusText}<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'error');
                }
            } catch (error) {
                addResult(`❌ 代理错误: ${error.message}`, 'error');
                addResult(`💡 这表明Vite代理没有正常工作，请检查：<br>1. 后端服务是否运行在3009端口<br>2. Vite配置是否正确<br>3. 前端服务是否正常启动`, 'warning');
            }
        }

        async function testLoginProxy() {
            addResult('🔄 测试登录代理 /api/auth/login...', 'info');
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'test123'
                    })
                });
                
                const data = await response.json();
                
                if (response.status === 401 || response.status === 400) {
                    addResult(`✅ 登录代理正常！(${response.status} 预期错误)<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                } else if (response.ok) {
                    addResult(`✅ 登录代理成功！<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                } else {
                    addResult(`❌ 登录代理异常: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ 登录代理错误: ${error.message}`, 'error');
            }
        }

        async function testWithFetch() {
            addResult('🔄 使用Fetch API测试...', 'info');
            
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 10000);
                
                const response = await fetch('/api/health', {
                    method: 'GET',
                    signal: controller.signal,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                clearTimeout(timeoutId);
                const data = await response.json();
                
                addResult(`✅ Fetch API测试成功<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
            } catch (error) {
                if (error.name === 'AbortError') {
                    addResult(`❌ Fetch API超时`, 'error');
                } else {
                    addResult(`❌ Fetch API错误: ${error.message}`, 'error');
                }
            }
        }

        async function testWithXHR() {
            addResult('🔄 使用XMLHttpRequest测试...', 'info');
            
            return new Promise((resolve) => {
                const xhr = new XMLHttpRequest();
                xhr.timeout = 10000;
                
                xhr.onload = function() {
                    try {
                        const data = JSON.parse(xhr.responseText);
                        addResult(`✅ XMLHttpRequest测试成功<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                    } catch (e) {
                        addResult(`❌ XMLHttpRequest响应解析失败: ${e.message}`, 'error');
                    }
                    resolve();
                };
                
                xhr.onerror = function() {
                    addResult(`❌ XMLHttpRequest网络错误`, 'error');
                    resolve();
                };
                
                xhr.ontimeout = function() {
                    addResult(`❌ XMLHttpRequest超时`, 'error');
                    resolve();
                };
                
                xhr.open('GET', '/api/health');
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.send();
            });
        }

        // 页面加载时自动测试
        window.onload = function() {
            addResult(`🌐 当前访问模式: ${isLocal ? '本地访问' : 'Sakura FRP公网访问'}`, 'info');
            addResult(`🔗 将测试代理路径: /api/*`, 'info');
            
            // 自动测试代理
            setTimeout(() => {
                testDirectProxy();
            }, 1000);
        };
    </script>
</body>
</html>
