<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复验证 - Sakura FRP</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f0f8ff; }
        .container { max-width: 600px; margin: 0 auto; }
        .card { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .success { background: linear-gradient(135deg, #d4edda, #c3e6cb); border-left: 5px solid #28a745; }
        .error { background: linear-gradient(135deg, #f8d7da, #f5c6cb); border-left: 5px solid #dc3545; }
        .info { background: linear-gradient(135deg, #d1ecf1, #bee5eb); border-left: 5px solid #17a2b8; }
        .warning { background: linear-gradient(135deg, #fff3cd, #ffeaa7); border-left: 5px solid #ffc107; }
        button { padding: 12px 24px; margin: 8px; background: #007bff; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; }
        button:hover { background: #0056b3; transform: translateY(-1px); }
        pre { background: #f8f9fa; padding: 15px; border-radius: 6px; overflow-x: auto; border: 1px solid #e9ecef; font-size: 12px; }
        .status { display: inline-block; padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; margin: 4px; }
        .status.online { background: #28a745; color: white; }
        .status.offline { background: #dc3545; color: white; }
        .status.unknown { background: #6c757d; color: white; }
        h1 { color: #2c3e50; text-align: center; }
        h3 { color: #34495e; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 修复验证工具</h1>
        
        <div class="card info">
            <h3>📍 当前访问状态</h3>
            <p><strong>访问域名:</strong> <span id="hostname"></span></p>
            <p><strong>访问类型:</strong> <span id="accessType" class="status unknown"></span></p>
            <p><strong>完整URL:</strong> <span id="fullUrl" style="font-size: 12px; word-break: break-all;"></span></p>
        </div>

        <div class="card">
            <h3>🧪 快速验证</h3>
            <button onclick="verifyFix()">🔍 验证修复状态</button>
            <button onclick="testLogin()">🔐 测试登录接口</button>
            <button onclick="clearResults()">🧹 清除结果</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        // 显示当前访问信息
        const hostname = window.location.hostname;
        const fullUrl = window.location.href;
        const isLocal = hostname === 'localhost' || hostname === '127.0.0.1';
        
        document.getElementById('hostname').textContent = hostname;
        document.getElementById('fullUrl').textContent = fullUrl;
        
        const accessTypeEl = document.getElementById('accessType');
        if (isLocal) {
            accessTypeEl.textContent = '本地访问';
            accessTypeEl.className = 'status offline';
        } else {
            accessTypeEl.textContent = 'Sakura FRP公网访问';
            accessTypeEl.className = 'status online';
        }
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `card ${type}`;
            div.innerHTML = message;
            document.getElementById('results').appendChild(div);
            div.scrollIntoView({ behavior: 'smooth' });
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function verifyFix() {
            clearResults();
            addResult('<h3>🔍 开始验证修复状态...</h3>', 'info');
            
            // 检查访问模式
            if (isLocal) {
                addResult(`
                    <h3>🏠 本地访问模式</h3>
                    <p>✅ 当前通过localhost访问，应该直连后端</p>
                    <p>📍 预期API地址: http://localhost:3009/api</p>
                `, 'success');
            } else {
                addResult(`
                    <h3>🌐 公网访问模式</h3>
                    <p>✅ 当前通过Sakura FRP访问，应该使用相对路径</p>
                    <p>📍 预期API地址: /api (通过代理)</p>
                `, 'success');
            }
            
            // 测试API连接
            addResult('<h3>🔄 测试API连接...</h3>', 'info');
            
            try {
                const response = await fetch('/api/health', {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`
                        <h3>✅ API连接成功！</h3>
                        <p>🎉 修复生效，代理工作正常</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `, 'success');
                } else {
                    addResult(`
                        <h3>❌ API连接失败</h3>
                        <p>状态码: ${response.status}</p>
                        <p>状态信息: ${response.statusText}</p>
                    `, 'error');
                }
            } catch (error) {
                addResult(`
                    <h3>❌ API连接错误</h3>
                    <p>错误信息: ${error.message}</p>
                    <p>💡 这表明代理没有工作，请检查：</p>
                    <ul>
                        <li>后端服务是否运行 (pm2 status)</li>
                        <li>Vite代理配置是否正确</li>
                        <li>前端服务是否正常启动</li>
                    </ul>
                `, 'error');
            }
        }

        async function testLogin() {
            addResult('<h3>🔐 测试登录接口...</h3>', 'info');
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'testpassword'
                    })
                });
                
                const data = await response.json();
                
                if (response.status === 401 || response.status === 400) {
                    addResult(`
                        <h3>✅ 登录接口正常！</h3>
                        <p>🎉 接口可以正常访问（预期的认证失败）</p>
                        <p>状态码: ${response.status}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `, 'success');
                } else if (response.ok) {
                    addResult(`
                        <h3>✅ 登录接口响应正常</h3>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `, 'success');
                } else {
                    addResult(`
                        <h3>⚠️ 登录接口异常响应</h3>
                        <p>状态码: ${response.status}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `, 'warning');
                }
            } catch (error) {
                addResult(`
                    <h3>❌ 登录接口错误</h3>
                    <p>错误信息: ${error.message}</p>
                    <p>🚨 这表明登录功能无法正常工作</p>
                `, 'error');
            }
        }

        // 页面加载时自动验证
        window.onload = function() {
            setTimeout(() => {
                verifyFix();
            }, 1000);
        };
    </script>
</body>
</html>
