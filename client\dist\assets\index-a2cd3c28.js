(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const i of r)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&s(o)}).observe(document,{childList:!0,subtree:!0});function n(r){const i={};return r.integrity&&(i.integrity=r.integrity),r.referrerPolicy&&(i.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?i.credentials="include":r.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function s(r){if(r.ep)return;r.ep=!0;const i=n(r);fetch(r.href,i)}})();/**
* @vue/shared v3.5.14
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Fr(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const ee={},jt=[],Ve=()=>{},Hl=()=>!1,Rs=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Mr=e=>e.startsWith("onUpdate:"),he=Object.assign,qr=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},jl=Object.prototype.hasOwnProperty,Y=(e,t)=>jl.call(e,t),M=Array.isArray,Kt=e=>Bn(e)==="[object Map]",tn=e=>Bn(e)==="[object Set]",wi=e=>Bn(e)==="[object Date]",V=e=>typeof e=="function",le=e=>typeof e=="string",nt=e=>typeof e=="symbol",re=e=>e!==null&&typeof e=="object",Uo=e=>(re(e)||V(e))&&V(e.then)&&V(e.catch),Fo=Object.prototype.toString,Bn=e=>Fo.call(e),Kl=e=>Bn(e).slice(8,-1),Mo=e=>Bn(e)==="[object Object]",Vr=e=>le(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,_n=Fr(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Ss=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Wl=/-(\w)/g,Ue=Ss(e=>e.replace(Wl,(t,n)=>n?n.toUpperCase():"")),zl=/\B([A-Z])/g,Dt=Ss(e=>e.replace(zl,"-$1").toLowerCase()),Ts=Ss(e=>e.charAt(0).toUpperCase()+e.slice(1)),Js=Ss(e=>e?`on${Ts(e)}`:""),Et=(e,t)=>!Object.is(e,t),zn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},qo=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},ls=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Ei;const As=()=>Ei||(Ei=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function $r(e){if(M(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=le(s)?Yl(s):$r(s);if(r)for(const i in r)t[i]=r[i]}return t}else if(le(e)||re(e))return e}const Jl=/;(?![^(]*\))/g,Gl=/:([^]+)/,Xl=/\/\*[^]*?\*\//g;function Yl(e){const t={};return e.replace(Xl,"").split(Jl).forEach(n=>{if(n){const s=n.split(Gl);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function An(e){let t="";if(le(e))t=e;else if(M(e))for(let n=0;n<e.length;n++){const s=An(e[n]);s&&(t+=s+" ")}else if(re(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Ql="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Zl=Fr(Ql);function Vo(e){return!!e||e===""}function ea(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=Nt(e[s],t[s]);return n}function Nt(e,t){if(e===t)return!0;let n=wi(e),s=wi(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=nt(e),s=nt(t),n||s)return e===t;if(n=M(e),s=M(t),n||s)return n&&s?ea(e,t):!1;if(n=re(e),s=re(t),n||s){if(!n||!s)return!1;const r=Object.keys(e).length,i=Object.keys(t).length;if(r!==i)return!1;for(const o in e){const c=e.hasOwnProperty(o),l=t.hasOwnProperty(o);if(c&&!l||!c&&l||!Nt(e[o],t[o]))return!1}}return String(e)===String(t)}function Hr(e,t){return e.findIndex(n=>Nt(n,t))}const $o=e=>!!(e&&e.__v_isRef===!0),Ho=e=>le(e)?e:e==null?"":M(e)||re(e)&&(e.toString===Fo||!V(e.toString))?$o(e)?Ho(e.value):JSON.stringify(e,jo,2):String(e),jo=(e,t)=>$o(t)?jo(e,t.value):Kt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],i)=>(n[Gs(s,i)+" =>"]=r,n),{})}:tn(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Gs(n))}:nt(t)?Gs(t):re(t)&&!M(t)&&!Mo(t)?String(t):t,Gs=(e,t="")=>{var n;return nt(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.14
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Se;class Ko{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Se,!t&&Se&&(this.index=(Se.scopes||(Se.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Se;try{return Se=this,t()}finally{Se=n}}}on(){++this._on===1&&(this.prevScope=Se,Se=this)}off(){this._on>0&&--this._on===0&&(Se=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function ta(e){return new Ko(e)}function na(){return Se}let se;const Xs=new WeakSet;class Wo{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Se&&Se.active&&Se.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Xs.has(this)&&(Xs.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Jo(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,vi(this),Go(this);const t=se,n=$e;se=this,$e=!0;try{return this.fn()}finally{Xo(this),se=t,$e=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Wr(t);this.deps=this.depsTail=void 0,vi(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Xs.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){dr(this)&&this.run()}get dirty(){return dr(this)}}let zo=0,bn,wn;function Jo(e,t=!1){if(e.flags|=8,t){e.next=wn,wn=e;return}e.next=bn,bn=e}function jr(){zo++}function Kr(){if(--zo>0)return;if(wn){let t=wn;for(wn=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;bn;){let t=bn;for(bn=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function Go(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Xo(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),Wr(s),sa(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function dr(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Yo(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Yo(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===xn)||(e.globalVersion=xn,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!dr(e))))return;e.flags|=2;const t=e.dep,n=se,s=$e;se=e,$e=!0;try{Go(e);const r=e.fn(e._value);(t.version===0||Et(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{se=n,$e=s,Xo(e),e.flags&=-3}}function Wr(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let i=n.computed.deps;i;i=i.nextDep)Wr(i,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function sa(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let $e=!0;const Qo=[];function ht(){Qo.push($e),$e=!1}function dt(){const e=Qo.pop();$e=e===void 0?!0:e}function vi(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=se;se=void 0;try{t()}finally{se=n}}}let xn=0;class ra{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class zr{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!se||!$e||se===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==se)n=this.activeLink=new ra(se,this),se.deps?(n.prevDep=se.depsTail,se.depsTail.nextDep=n,se.depsTail=n):se.deps=se.depsTail=n,Zo(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=se.depsTail,n.nextDep=void 0,se.depsTail.nextDep=n,se.depsTail=n,se.deps===n&&(se.deps=s)}return n}trigger(t){this.version++,xn++,this.notify(t)}notify(t){jr();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Kr()}}}function Zo(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)Zo(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const pr=new WeakMap,kt=Symbol(""),mr=Symbol(""),On=Symbol("");function me(e,t,n){if($e&&se){let s=pr.get(e);s||pr.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new zr),r.map=s,r.key=n),r.track()}}function at(e,t,n,s,r,i){const o=pr.get(e);if(!o){xn++;return}const c=l=>{l&&l.trigger()};if(jr(),t==="clear")o.forEach(c);else{const l=M(e),u=l&&Vr(n);if(l&&n==="length"){const a=Number(s);o.forEach((f,p)=>{(p==="length"||p===On||!nt(p)&&p>=a)&&c(f)})}else switch((n!==void 0||o.has(void 0))&&c(o.get(n)),u&&c(o.get(On)),t){case"add":l?u&&c(o.get("length")):(c(o.get(kt)),Kt(e)&&c(o.get(mr)));break;case"delete":l||(c(o.get(kt)),Kt(e)&&c(o.get(mr)));break;case"set":Kt(e)&&c(o.get(kt));break}}Kr()}function Vt(e){const t=X(e);return t===e?t:(me(t,"iterate",On),Be(e)?t:t.map(de))}function xs(e){return me(e=X(e),"iterate",On),e}const ia={__proto__:null,[Symbol.iterator](){return Ys(this,Symbol.iterator,de)},concat(...e){return Vt(this).concat(...e.map(t=>M(t)?Vt(t):t))},entries(){return Ys(this,"entries",e=>(e[1]=de(e[1]),e))},every(e,t){return ot(this,"every",e,t,void 0,arguments)},filter(e,t){return ot(this,"filter",e,t,n=>n.map(de),arguments)},find(e,t){return ot(this,"find",e,t,de,arguments)},findIndex(e,t){return ot(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return ot(this,"findLast",e,t,de,arguments)},findLastIndex(e,t){return ot(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return ot(this,"forEach",e,t,void 0,arguments)},includes(...e){return Qs(this,"includes",e)},indexOf(...e){return Qs(this,"indexOf",e)},join(e){return Vt(this).join(e)},lastIndexOf(...e){return Qs(this,"lastIndexOf",e)},map(e,t){return ot(this,"map",e,t,void 0,arguments)},pop(){return ln(this,"pop")},push(...e){return ln(this,"push",e)},reduce(e,...t){return Ri(this,"reduce",e,t)},reduceRight(e,...t){return Ri(this,"reduceRight",e,t)},shift(){return ln(this,"shift")},some(e,t){return ot(this,"some",e,t,void 0,arguments)},splice(...e){return ln(this,"splice",e)},toReversed(){return Vt(this).toReversed()},toSorted(e){return Vt(this).toSorted(e)},toSpliced(...e){return Vt(this).toSpliced(...e)},unshift(...e){return ln(this,"unshift",e)},values(){return Ys(this,"values",de)}};function Ys(e,t,n){const s=xs(e),r=s[t]();return s!==e&&!Be(e)&&(r._next=r.next,r.next=()=>{const i=r._next();return i.value&&(i.value=n(i.value)),i}),r}const oa=Array.prototype;function ot(e,t,n,s,r,i){const o=xs(e),c=o!==e&&!Be(e),l=o[t];if(l!==oa[t]){const f=l.apply(e,i);return c?de(f):f}let u=n;o!==e&&(c?u=function(f,p){return n.call(this,de(f),p,e)}:n.length>2&&(u=function(f,p){return n.call(this,f,p,e)}));const a=l.call(o,u,s);return c&&r?r(a):a}function Ri(e,t,n,s){const r=xs(e);let i=n;return r!==e&&(Be(e)?n.length>3&&(i=function(o,c,l){return n.call(this,o,c,l,e)}):i=function(o,c,l){return n.call(this,o,de(c),l,e)}),r[t](i,...s)}function Qs(e,t,n){const s=X(e);me(s,"iterate",On);const r=s[t](...n);return(r===-1||r===!1)&&Xr(n[0])?(n[0]=X(n[0]),s[t](...n)):r}function ln(e,t,n=[]){ht(),jr();const s=X(e)[t].apply(e,n);return Kr(),dt(),s}const ca=Fr("__proto__,__v_isRef,__isVue"),ec=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(nt));function la(e){nt(e)||(e=String(e));const t=X(this);return me(t,"has",e),t.hasOwnProperty(e)}class tc{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,i=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return i;if(n==="__v_raw")return s===(r?i?_a:ic:i?rc:sc).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const o=M(t);if(!r){let l;if(o&&(l=ia[n]))return l;if(n==="hasOwnProperty")return la}const c=Reflect.get(t,n,_e(t)?t:s);return(nt(n)?ec.has(n):ca(n))||(r||me(t,"get",n),i)?c:_e(c)?o&&Vr(n)?c:c.value:re(c)?r?cc(c):Os(c):c}}class nc extends tc{constructor(t=!1){super(!1,t)}set(t,n,s,r){let i=t[n];if(!this._isShallow){const l=Rt(i);if(!Be(s)&&!Rt(s)&&(i=X(i),s=X(s)),!M(t)&&_e(i)&&!_e(s))return l?!1:(i.value=s,!0)}const o=M(t)&&Vr(n)?Number(n)<t.length:Y(t,n),c=Reflect.set(t,n,s,_e(t)?t:r);return t===X(r)&&(o?Et(s,i)&&at(t,"set",n,s):at(t,"add",n,s)),c}deleteProperty(t,n){const s=Y(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&at(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!nt(n)||!ec.has(n))&&me(t,"has",n),s}ownKeys(t){return me(t,"iterate",M(t)?"length":kt),Reflect.ownKeys(t)}}class aa extends tc{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const ua=new nc,fa=new aa,ha=new nc(!0);const gr=e=>e,qn=e=>Reflect.getPrototypeOf(e);function da(e,t,n){return function(...s){const r=this.__v_raw,i=X(r),o=Kt(i),c=e==="entries"||e===Symbol.iterator&&o,l=e==="keys"&&o,u=r[e](...s),a=n?gr:t?as:de;return!t&&me(i,"iterate",l?mr:kt),{next(){const{value:f,done:p}=u.next();return p?{value:f,done:p}:{value:c?[a(f[0]),a(f[1])]:a(f),done:p}},[Symbol.iterator](){return this}}}}function Vn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function pa(e,t){const n={get(r){const i=this.__v_raw,o=X(i),c=X(r);e||(Et(r,c)&&me(o,"get",r),me(o,"get",c));const{has:l}=qn(o),u=t?gr:e?as:de;if(l.call(o,r))return u(i.get(r));if(l.call(o,c))return u(i.get(c));i!==o&&i.get(r)},get size(){const r=this.__v_raw;return!e&&me(X(r),"iterate",kt),Reflect.get(r,"size",r)},has(r){const i=this.__v_raw,o=X(i),c=X(r);return e||(Et(r,c)&&me(o,"has",r),me(o,"has",c)),r===c?i.has(r):i.has(r)||i.has(c)},forEach(r,i){const o=this,c=o.__v_raw,l=X(c),u=t?gr:e?as:de;return!e&&me(l,"iterate",kt),c.forEach((a,f)=>r.call(i,u(a),u(f),o))}};return he(n,e?{add:Vn("add"),set:Vn("set"),delete:Vn("delete"),clear:Vn("clear")}:{add(r){!t&&!Be(r)&&!Rt(r)&&(r=X(r));const i=X(this);return qn(i).has.call(i,r)||(i.add(r),at(i,"add",r,r)),this},set(r,i){!t&&!Be(i)&&!Rt(i)&&(i=X(i));const o=X(this),{has:c,get:l}=qn(o);let u=c.call(o,r);u||(r=X(r),u=c.call(o,r));const a=l.call(o,r);return o.set(r,i),u?Et(i,a)&&at(o,"set",r,i):at(o,"add",r,i),this},delete(r){const i=X(this),{has:o,get:c}=qn(i);let l=o.call(i,r);l||(r=X(r),l=o.call(i,r)),c&&c.call(i,r);const u=i.delete(r);return l&&at(i,"delete",r,void 0),u},clear(){const r=X(this),i=r.size!==0,o=r.clear();return i&&at(r,"clear",void 0,void 0),o}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=da(r,e,t)}),n}function Jr(e,t){const n=pa(e,t);return(s,r,i)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(Y(n,r)&&r in s?n:s,r,i)}const ma={get:Jr(!1,!1)},ga={get:Jr(!1,!0)},ya={get:Jr(!0,!1)};const sc=new WeakMap,rc=new WeakMap,ic=new WeakMap,_a=new WeakMap;function ba(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function wa(e){return e.__v_skip||!Object.isExtensible(e)?0:ba(Kl(e))}function Os(e){return Rt(e)?e:Gr(e,!1,ua,ma,sc)}function oc(e){return Gr(e,!1,ha,ga,rc)}function cc(e){return Gr(e,!0,fa,ya,ic)}function Gr(e,t,n,s,r){if(!re(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=wa(e);if(i===0)return e;const o=r.get(e);if(o)return o;const c=new Proxy(e,i===2?s:n);return r.set(e,c),c}function Wt(e){return Rt(e)?Wt(e.__v_raw):!!(e&&e.__v_isReactive)}function Rt(e){return!!(e&&e.__v_isReadonly)}function Be(e){return!!(e&&e.__v_isShallow)}function Xr(e){return e?!!e.__v_raw:!1}function X(e){const t=e&&e.__v_raw;return t?X(t):e}function lc(e){return!Y(e,"__v_skip")&&Object.isExtensible(e)&&qo(e,"__v_skip",!0),e}const de=e=>re(e)?Os(e):e,as=e=>re(e)?cc(e):e;function _e(e){return e?e.__v_isRef===!0:!1}function ac(e){return uc(e,!1)}function Ea(e){return uc(e,!0)}function uc(e,t){return _e(e)?e:new va(e,t)}class va{constructor(t,n){this.dep=new zr,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:X(t),this._value=n?t:de(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||Be(t)||Rt(t);t=s?t:X(t),Et(t,n)&&(this._rawValue=t,this._value=s?t:de(t),this.dep.trigger())}}function zt(e){return _e(e)?e.value:e}const Ra={get:(e,t,n)=>t==="__v_raw"?e:zt(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return _e(r)&&!_e(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function fc(e){return Wt(e)?e:new Proxy(e,Ra)}class Sa{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new zr(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=xn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&se!==this)return Jo(this,!0),!0}get value(){const t=this.dep.track();return Yo(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Ta(e,t,n=!1){let s,r;return V(e)?s=e:(s=e.get,r=e.set),new Sa(s,r,n)}const $n={},us=new WeakMap;let Ct;function Aa(e,t=!1,n=Ct){if(n){let s=us.get(n);s||us.set(n,s=[]),s.push(e)}}function xa(e,t,n=ee){const{immediate:s,deep:r,once:i,scheduler:o,augmentJob:c,call:l}=n,u=I=>r?I:Be(I)||r===!1||r===0?ut(I,1):ut(I);let a,f,p,m,_=!1,E=!1;if(_e(e)?(f=()=>e.value,_=Be(e)):Wt(e)?(f=()=>u(e),_=!0):M(e)?(E=!0,_=e.some(I=>Wt(I)||Be(I)),f=()=>e.map(I=>{if(_e(I))return I.value;if(Wt(I))return u(I);if(V(I))return l?l(I,2):I()})):V(e)?t?f=l?()=>l(e,2):e:f=()=>{if(p){ht();try{p()}finally{dt()}}const I=Ct;Ct=a;try{return l?l(e,3,[m]):e(m)}finally{Ct=I}}:f=Ve,t&&r){const I=f,$=r===!0?1/0:r;f=()=>ut(I(),$)}const S=na(),P=()=>{a.stop(),S&&S.active&&qr(S.effects,a)};if(i&&t){const I=t;t=(...$)=>{I(...$),P()}}let O=E?new Array(e.length).fill($n):$n;const L=I=>{if(!(!(a.flags&1)||!a.dirty&&!I))if(t){const $=a.run();if(r||_||(E?$.some((te,W)=>Et(te,O[W])):Et($,O))){p&&p();const te=Ct;Ct=a;try{const W=[$,O===$n?void 0:E&&O[0]===$n?[]:O,m];l?l(t,3,W):t(...W),O=$}finally{Ct=te}}}else a.run()};return c&&c(L),a=new Wo(f),a.scheduler=o?()=>o(L,!1):L,m=I=>Aa(I,!1,a),p=a.onStop=()=>{const I=us.get(a);if(I){if(l)l(I,4);else for(const $ of I)$();us.delete(a)}},t?s?L(!0):O=a.run():o?o(L.bind(null,!0),!0):a.run(),P.pause=a.pause.bind(a),P.resume=a.resume.bind(a),P.stop=P,P}function ut(e,t=1/0,n){if(t<=0||!re(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,_e(e))ut(e.value,t,n);else if(M(e))for(let s=0;s<e.length;s++)ut(e[s],t,n);else if(tn(e)||Kt(e))e.forEach(s=>{ut(s,t,n)});else if(Mo(e)){for(const s in e)ut(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&ut(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.14
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Dn(e,t,n,s){try{return s?e(...s):e()}catch(r){Cs(r,t,n)}}function st(e,t,n,s){if(V(e)){const r=Dn(e,t,n,s);return r&&Uo(r)&&r.catch(i=>{Cs(i,t,n)}),r}if(M(e)){const r=[];for(let i=0;i<e.length;i++)r.push(st(e[i],t,n,s));return r}}function Cs(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:o}=t&&t.appContext.config||ee;if(t){let c=t.parent;const l=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${n}`;for(;c;){const a=c.ec;if(a){for(let f=0;f<a.length;f++)if(a[f](e,l,u)===!1)return}c=c.parent}if(i){ht(),Dn(i,null,10,[e,l,u]),dt();return}}Oa(e,n,r,s,o)}function Oa(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const Ee=[];let Ze=-1;const Jt=[];let _t=null,$t=0;const hc=Promise.resolve();let fs=null;function Yr(e){const t=fs||hc;return e?t.then(this?e.bind(this):e):t}function Ca(e){let t=Ze+1,n=Ee.length;for(;t<n;){const s=t+n>>>1,r=Ee[s],i=Cn(r);i<e||i===e&&r.flags&2?t=s+1:n=s}return t}function Qr(e){if(!(e.flags&1)){const t=Cn(e),n=Ee[Ee.length-1];!n||!(e.flags&2)&&t>=Cn(n)?Ee.push(e):Ee.splice(Ca(t),0,e),e.flags|=1,dc()}}function dc(){fs||(fs=hc.then(mc))}function Pa(e){M(e)?Jt.push(...e):_t&&e.id===-1?_t.splice($t+1,0,e):e.flags&1||(Jt.push(e),e.flags|=1),dc()}function Si(e,t,n=Ze+1){for(;n<Ee.length;n++){const s=Ee[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;Ee.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function pc(e){if(Jt.length){const t=[...new Set(Jt)].sort((n,s)=>Cn(n)-Cn(s));if(Jt.length=0,_t){_t.push(...t);return}for(_t=t,$t=0;$t<_t.length;$t++){const n=_t[$t];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}_t=null,$t=0}}const Cn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function mc(e){const t=Ve;try{for(Ze=0;Ze<Ee.length;Ze++){const n=Ee[Ze];n&&!(n.flags&8)&&(n.flags&4&&(n.flags&=-2),Dn(n,n.i,n.i?15:14),n.flags&4||(n.flags&=-2))}}finally{for(;Ze<Ee.length;Ze++){const n=Ee[Ze];n&&(n.flags&=-2)}Ze=-1,Ee.length=0,pc(),fs=null,(Ee.length||Jt.length)&&mc()}}let ke=null,gc=null;function hs(e){const t=ke;return ke=e,gc=e&&e.type.__scopeId||null,t}function ka(e,t=ke,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&Li(-1);const i=hs(t);let o;try{o=e(...r)}finally{hs(i),s._d&&Li(1)}return o};return s._n=!0,s._c=!0,s._d=!0,s}function fm(e,t){if(ke===null)return e;const n=Ls(ke),s=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[i,o,c,l=ee]=t[r];i&&(V(i)&&(i={mounted:i,updated:i}),i.deep&&ut(o),s.push({dir:i,instance:n,value:o,oldValue:void 0,arg:c,modifiers:l}))}return e}function xt(e,t,n,s){const r=e.dirs,i=t&&t.dirs;for(let o=0;o<r.length;o++){const c=r[o];i&&(c.oldValue=i[o].value);let l=c.dir[s];l&&(ht(),st(l,n,8,[e.el,c,e,t]),dt())}}const Na=Symbol("_vte"),La=e=>e.__isTeleport;function Zr(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Zr(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function yc(e,t){return V(e)?(()=>he({name:e.name},t,{setup:e}))():e}function _c(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function ds(e,t,n,s,r=!1){if(M(e)){e.forEach((_,E)=>ds(_,t&&(M(t)?t[E]:t),n,s,r));return}if(En(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&ds(e,t,n,s.component.subTree);return}const i=s.shapeFlag&4?Ls(s.component):s.el,o=r?null:i,{i:c,r:l}=e,u=t&&t.r,a=c.refs===ee?c.refs={}:c.refs,f=c.setupState,p=X(f),m=f===ee?()=>!1:_=>Y(p,_);if(u!=null&&u!==l&&(le(u)?(a[u]=null,m(u)&&(f[u]=null)):_e(u)&&(u.value=null)),V(l))Dn(l,c,12,[o,a]);else{const _=le(l),E=_e(l);if(_||E){const S=()=>{if(e.f){const P=_?m(l)?f[l]:a[l]:l.value;r?M(P)&&qr(P,i):M(P)?P.includes(i)||P.push(i):_?(a[l]=[i],m(l)&&(f[l]=a[l])):(l.value=[i],e.k&&(a[e.k]=l.value))}else _?(a[l]=o,m(l)&&(f[l]=o)):E&&(l.value=o,e.k&&(a[e.k]=o))};o?(S.id=-1,Ce(S,n)):S()}}}As().requestIdleCallback;As().cancelIdleCallback;const En=e=>!!e.type.__asyncLoader,bc=e=>e.type.__isKeepAlive;function Ia(e,t){wc(e,"a",t)}function Ba(e,t){wc(e,"da",t)}function wc(e,t,n=ge){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(Ps(t,s,n),n){let r=n.parent;for(;r&&r.parent;)bc(r.parent.vnode)&&Da(s,t,n,r),r=r.parent}}function Da(e,t,n,s){const r=Ps(t,e,s,!0);Ec(()=>{qr(s[t],r)},n)}function Ps(e,t,n=ge,s=!1){if(n){const r=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...o)=>{ht();const c=Un(n),l=st(t,n,e,o);return c(),dt(),l});return s?r.unshift(i):r.push(i),i}}const pt=e=>(t,n=ge)=>{(!kn||e==="sp")&&Ps(e,(...s)=>t(...s),n)},Ua=pt("bm"),Fa=pt("m"),Ma=pt("bu"),qa=pt("u"),Va=pt("bum"),Ec=pt("um"),$a=pt("sp"),Ha=pt("rtg"),ja=pt("rtc");function Ka(e,t=ge){Ps("ec",e,t)}const vc="components";function Wa(e,t){return Ja(vc,e,!0,t)||e}const za=Symbol.for("v-ndc");function Ja(e,t,n=!0,s=!1){const r=ke||ge;if(r){const i=r.type;if(e===vc){const c=Fu(i,!1);if(c&&(c===t||c===Ue(t)||c===Ts(Ue(t))))return i}const o=Ti(r[e]||i[e],t)||Ti(r.appContext[e],t);return!o&&s?i:o}}function Ti(e,t){return e&&(e[t]||e[Ue(t)]||e[Ts(Ue(t))])}function Ga(e,t,n,s){let r;const i=n&&n[s],o=M(e);if(o||le(e)){const c=o&&Wt(e);let l=!1,u=!1;c&&(l=!Be(e),u=Rt(e),e=xs(e)),r=new Array(e.length);for(let a=0,f=e.length;a<f;a++)r[a]=t(l?u?as(de(e[a])):de(e[a]):e[a],a,void 0,i&&i[a])}else if(typeof e=="number"){r=new Array(e);for(let c=0;c<e;c++)r[c]=t(c+1,c,void 0,i&&i[c])}else if(re(e))if(e[Symbol.iterator])r=Array.from(e,(c,l)=>t(c,l,void 0,i&&i[l]));else{const c=Object.keys(e);r=new Array(c.length);for(let l=0,u=c.length;l<u;l++){const a=c[l];r[l]=t(e[a],a,l,i&&i[l])}}else r=[];return n&&(n[s]=r),r}const yr=e=>e?Vc(e)?Ls(e):yr(e.parent):null,vn=he(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>yr(e.parent),$root:e=>yr(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>ei(e),$forceUpdate:e=>e.f||(e.f=()=>{Qr(e.update)}),$nextTick:e=>e.n||(e.n=Yr.bind(e.proxy)),$watch:e=>gu.bind(e)}),Zs=(e,t)=>e!==ee&&!e.__isScriptSetup&&Y(e,t),Xa={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:i,accessCache:o,type:c,appContext:l}=e;let u;if(t[0]!=="$"){const m=o[t];if(m!==void 0)switch(m){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return i[t]}else{if(Zs(s,t))return o[t]=1,s[t];if(r!==ee&&Y(r,t))return o[t]=2,r[t];if((u=e.propsOptions[0])&&Y(u,t))return o[t]=3,i[t];if(n!==ee&&Y(n,t))return o[t]=4,n[t];_r&&(o[t]=0)}}const a=vn[t];let f,p;if(a)return t==="$attrs"&&me(e.attrs,"get",""),a(e);if((f=c.__cssModules)&&(f=f[t]))return f;if(n!==ee&&Y(n,t))return o[t]=4,n[t];if(p=l.config.globalProperties,Y(p,t))return p[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:i}=e;return Zs(r,t)?(r[t]=n,!0):s!==ee&&Y(s,t)?(s[t]=n,!0):Y(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:i}},o){let c;return!!n[o]||e!==ee&&Y(e,o)||Zs(t,o)||(c=i[0])&&Y(c,o)||Y(s,o)||Y(vn,o)||Y(r.config.globalProperties,o)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:Y(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Ai(e){return M(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let _r=!0;function Ya(e){const t=ei(e),n=e.proxy,s=e.ctx;_r=!1,t.beforeCreate&&xi(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:o,watch:c,provide:l,inject:u,created:a,beforeMount:f,mounted:p,beforeUpdate:m,updated:_,activated:E,deactivated:S,beforeDestroy:P,beforeUnmount:O,destroyed:L,unmounted:I,render:$,renderTracked:te,renderTriggered:W,errorCaptured:be,serverPrefetch:Fe,expose:ze,inheritAttrs:mt,components:At,directives:Je,filters:on}=t;if(u&&Qa(u,s,null),o)for(const Z in o){const z=o[Z];V(z)&&(s[Z]=z.bind(n))}if(r){const Z=r.call(n,n);re(Z)&&(e.data=Os(Z))}if(_r=!0,i)for(const Z in i){const z=i[Z],it=V(z)?z.bind(n,n):V(z.get)?z.get.bind(n,n):Ve,gt=!V(z)&&V(z.set)?z.set.bind(n):Ve,Ge=Pe({get:it,set:gt});Object.defineProperty(s,Z,{enumerable:!0,configurable:!0,get:()=>Ge.value,set:Re=>Ge.value=Re})}if(c)for(const Z in c)Rc(c[Z],s,n,Z);if(l){const Z=V(l)?l.call(n):l;Reflect.ownKeys(Z).forEach(z=>{Jn(z,Z[z])})}a&&xi(a,e,"c");function ue(Z,z){M(z)?z.forEach(it=>Z(it.bind(n))):z&&Z(z.bind(n))}if(ue(Ua,f),ue(Fa,p),ue(Ma,m),ue(qa,_),ue(Ia,E),ue(Ba,S),ue(Ka,be),ue(ja,te),ue(Ha,W),ue(Va,O),ue(Ec,I),ue($a,Fe),M(ze))if(ze.length){const Z=e.exposed||(e.exposed={});ze.forEach(z=>{Object.defineProperty(Z,z,{get:()=>n[z],set:it=>n[z]=it})})}else e.exposed||(e.exposed={});$&&e.render===Ve&&(e.render=$),mt!=null&&(e.inheritAttrs=mt),At&&(e.components=At),Je&&(e.directives=Je),Fe&&_c(e)}function Qa(e,t,n=Ve){M(e)&&(e=br(e));for(const s in e){const r=e[s];let i;re(r)?"default"in r?i=He(r.from||s,r.default,!0):i=He(r.from||s):i=He(r),_e(i)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>i.value,set:o=>i.value=o}):t[s]=i}}function xi(e,t,n){st(M(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Rc(e,t,n,s){let r=s.includes(".")?Dc(n,s):()=>n[s];if(le(e)){const i=t[e];V(i)&&Gn(r,i)}else if(V(e))Gn(r,e.bind(n));else if(re(e))if(M(e))e.forEach(i=>Rc(i,t,n,s));else{const i=V(e.handler)?e.handler.bind(n):t[e.handler];V(i)&&Gn(r,i,e)}}function ei(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:o}}=e.appContext,c=i.get(t);let l;return c?l=c:!r.length&&!n&&!s?l=t:(l={},r.length&&r.forEach(u=>ps(l,u,o,!0)),ps(l,t,o)),re(t)&&i.set(t,l),l}function ps(e,t,n,s=!1){const{mixins:r,extends:i}=t;i&&ps(e,i,n,!0),r&&r.forEach(o=>ps(e,o,n,!0));for(const o in t)if(!(s&&o==="expose")){const c=Za[o]||n&&n[o];e[o]=c?c(e[o],t[o]):t[o]}return e}const Za={data:Oi,props:Ci,emits:Ci,methods:mn,computed:mn,beforeCreate:we,created:we,beforeMount:we,mounted:we,beforeUpdate:we,updated:we,beforeDestroy:we,beforeUnmount:we,destroyed:we,unmounted:we,activated:we,deactivated:we,errorCaptured:we,serverPrefetch:we,components:mn,directives:mn,watch:tu,provide:Oi,inject:eu};function Oi(e,t){return t?e?function(){return he(V(e)?e.call(this,this):e,V(t)?t.call(this,this):t)}:t:e}function eu(e,t){return mn(br(e),br(t))}function br(e){if(M(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function we(e,t){return e?[...new Set([].concat(e,t))]:t}function mn(e,t){return e?he(Object.create(null),e,t):t}function Ci(e,t){return e?M(e)&&M(t)?[...new Set([...e,...t])]:he(Object.create(null),Ai(e),Ai(t??{})):t}function tu(e,t){if(!e)return t;if(!t)return e;const n=he(Object.create(null),e);for(const s in t)n[s]=we(e[s],t[s]);return n}function Sc(){return{app:null,config:{isNativeTag:Hl,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let nu=0;function su(e,t){return function(s,r=null){V(s)||(s=he({},s)),r!=null&&!re(r)&&(r=null);const i=Sc(),o=new WeakSet,c=[];let l=!1;const u=i.app={_uid:nu++,_component:s,_props:r,_container:null,_context:i,_instance:null,version:qu,get config(){return i.config},set config(a){},use(a,...f){return o.has(a)||(a&&V(a.install)?(o.add(a),a.install(u,...f)):V(a)&&(o.add(a),a(u,...f))),u},mixin(a){return i.mixins.includes(a)||i.mixins.push(a),u},component(a,f){return f?(i.components[a]=f,u):i.components[a]},directive(a,f){return f?(i.directives[a]=f,u):i.directives[a]},mount(a,f,p){if(!l){const m=u._ceVNode||ve(s,r);return m.appContext=i,p===!0?p="svg":p===!1&&(p=void 0),f&&t?t(m,a):e(m,a,p),l=!0,u._container=a,a.__vue_app__=u,Ls(m.component)}},onUnmount(a){c.push(a)},unmount(){l&&(st(c,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(a,f){return i.provides[a]=f,u},runWithContext(a){const f=Gt;Gt=u;try{return a()}finally{Gt=f}}};return u}}let Gt=null;function Jn(e,t){if(ge){let n=ge.provides;const s=ge.parent&&ge.parent.provides;s===n&&(n=ge.provides=Object.create(s)),n[e]=t}}function He(e,t,n=!1){const s=ge||ke;if(s||Gt){const r=Gt?Gt._context.provides:s?s.parent==null?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&V(t)?t.call(s&&s.proxy):t}}const Tc={},Ac=()=>Object.create(Tc),xc=e=>Object.getPrototypeOf(e)===Tc;function ru(e,t,n,s=!1){const r={},i=Ac();e.propsDefaults=Object.create(null),Oc(e,t,r,i);for(const o in e.propsOptions[0])o in r||(r[o]=void 0);n?e.props=s?r:oc(r):e.type.props?e.props=r:e.props=i,e.attrs=i}function iu(e,t,n,s){const{props:r,attrs:i,vnode:{patchFlag:o}}=e,c=X(r),[l]=e.propsOptions;let u=!1;if((s||o>0)&&!(o&16)){if(o&8){const a=e.vnode.dynamicProps;for(let f=0;f<a.length;f++){let p=a[f];if(ks(e.emitsOptions,p))continue;const m=t[p];if(l)if(Y(i,p))m!==i[p]&&(i[p]=m,u=!0);else{const _=Ue(p);r[_]=wr(l,c,_,m,e,!1)}else m!==i[p]&&(i[p]=m,u=!0)}}}else{Oc(e,t,r,i)&&(u=!0);let a;for(const f in c)(!t||!Y(t,f)&&((a=Dt(f))===f||!Y(t,a)))&&(l?n&&(n[f]!==void 0||n[a]!==void 0)&&(r[f]=wr(l,c,f,void 0,e,!0)):delete r[f]);if(i!==c)for(const f in i)(!t||!Y(t,f))&&(delete i[f],u=!0)}u&&at(e.attrs,"set","")}function Oc(e,t,n,s){const[r,i]=e.propsOptions;let o=!1,c;if(t)for(let l in t){if(_n(l))continue;const u=t[l];let a;r&&Y(r,a=Ue(l))?!i||!i.includes(a)?n[a]=u:(c||(c={}))[a]=u:ks(e.emitsOptions,l)||(!(l in s)||u!==s[l])&&(s[l]=u,o=!0)}if(i){const l=X(n),u=c||ee;for(let a=0;a<i.length;a++){const f=i[a];n[f]=wr(r,l,f,u[f],e,!Y(u,f))}}return o}function wr(e,t,n,s,r,i){const o=e[n];if(o!=null){const c=Y(o,"default");if(c&&s===void 0){const l=o.default;if(o.type!==Function&&!o.skipFactory&&V(l)){const{propsDefaults:u}=r;if(n in u)s=u[n];else{const a=Un(r);s=u[n]=l.call(null,t),a()}}else s=l;r.ce&&r.ce._setProp(n,s)}o[0]&&(i&&!c?s=!1:o[1]&&(s===""||s===Dt(n))&&(s=!0))}return s}const ou=new WeakMap;function Cc(e,t,n=!1){const s=n?ou:t.propsCache,r=s.get(e);if(r)return r;const i=e.props,o={},c=[];let l=!1;if(!V(e)){const a=f=>{l=!0;const[p,m]=Cc(f,t,!0);he(o,p),m&&c.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!i&&!l)return re(e)&&s.set(e,jt),jt;if(M(i))for(let a=0;a<i.length;a++){const f=Ue(i[a]);Pi(f)&&(o[f]=ee)}else if(i)for(const a in i){const f=Ue(a);if(Pi(f)){const p=i[a],m=o[f]=M(p)||V(p)?{type:p}:he({},p),_=m.type;let E=!1,S=!0;if(M(_))for(let P=0;P<_.length;++P){const O=_[P],L=V(O)&&O.name;if(L==="Boolean"){E=!0;break}else L==="String"&&(S=!1)}else E=V(_)&&_.name==="Boolean";m[0]=E,m[1]=S,(E||Y(m,"default"))&&c.push(f)}}const u=[o,c];return re(e)&&s.set(e,u),u}function Pi(e){return e[0]!=="$"&&!_n(e)}const ti=e=>e[0]==="_"||e==="$stable",ni=e=>M(e)?e.map(tt):[tt(e)],cu=(e,t,n)=>{if(t._n)return t;const s=ka((...r)=>ni(t(...r)),n);return s._c=!1,s},Pc=(e,t,n)=>{const s=e._ctx;for(const r in e){if(ti(r))continue;const i=e[r];if(V(i))t[r]=cu(r,i,s);else if(i!=null){const o=ni(i);t[r]=()=>o}}},kc=(e,t)=>{const n=ni(t);e.slots.default=()=>n},Nc=(e,t,n)=>{for(const s in t)(n||!ti(s))&&(e[s]=t[s])},lu=(e,t,n)=>{const s=e.slots=Ac();if(e.vnode.shapeFlag&32){const r=t._;r?(Nc(s,t,n),n&&qo(s,"_",r,!0)):Pc(t,s)}else t&&kc(e,t)},au=(e,t,n)=>{const{vnode:s,slots:r}=e;let i=!0,o=ee;if(s.shapeFlag&32){const c=t._;c?n&&c===1?i=!1:Nc(r,t,n):(i=!t.$stable,Pc(t,r)),o=t}else t&&(kc(e,t),o={default:1});if(i)for(const c in r)!ti(c)&&o[c]==null&&delete r[c]},Ce=Ru;function uu(e){return fu(e)}function fu(e,t){const n=As();n.__VUE__=!0;const{insert:s,remove:r,patchProp:i,createElement:o,createText:c,createComment:l,setText:u,setElementText:a,parentNode:f,nextSibling:p,setScopeId:m=Ve,insertStaticContent:_}=e,E=(h,d,g,v=null,b=null,R=null,C=void 0,x=null,A=!!d.dynamicChildren)=>{if(h===d)return;h&&!an(h,d)&&(v=w(h),Re(h,b,R,!0),h=null),d.patchFlag===-2&&(A=!1,d.dynamicChildren=null);const{type:T,ref:F,shapeFlag:N}=d;switch(T){case Ns:S(h,d,g,v);break;case St:P(h,d,g,v);break;case Xn:h==null&&O(d,g,v,C);break;case et:At(h,d,g,v,b,R,C,x,A);break;default:N&1?$(h,d,g,v,b,R,C,x,A):N&6?Je(h,d,g,v,b,R,C,x,A):(N&64||N&128)&&T.process(h,d,g,v,b,R,C,x,A,D)}F!=null&&b&&ds(F,h&&h.ref,R,d||h,!d)},S=(h,d,g,v)=>{if(h==null)s(d.el=c(d.children),g,v);else{const b=d.el=h.el;d.children!==h.children&&u(b,d.children)}},P=(h,d,g,v)=>{h==null?s(d.el=l(d.children||""),g,v):d.el=h.el},O=(h,d,g,v)=>{[h.el,h.anchor]=_(h.children,d,g,v,h.el,h.anchor)},L=({el:h,anchor:d},g,v)=>{let b;for(;h&&h!==d;)b=p(h),s(h,g,v),h=b;s(d,g,v)},I=({el:h,anchor:d})=>{let g;for(;h&&h!==d;)g=p(h),r(h),h=g;r(d)},$=(h,d,g,v,b,R,C,x,A)=>{d.type==="svg"?C="svg":d.type==="math"&&(C="mathml"),h==null?te(d,g,v,b,R,C,x,A):Fe(h,d,b,R,C,x,A)},te=(h,d,g,v,b,R,C,x)=>{let A,T;const{props:F,shapeFlag:N,transition:U,dirs:q}=h;if(A=h.el=o(h.type,R,F&&F.is,F),N&8?a(A,h.children):N&16&&be(h.children,A,null,v,b,er(h,R),C,x),q&&xt(h,null,v,"created"),W(A,h,h.scopeId,C,v),F){for(const ne in F)ne!=="value"&&!_n(ne)&&i(A,ne,null,F[ne],R,v);"value"in F&&i(A,"value",null,F.value,R),(T=F.onVnodeBeforeMount)&&Ye(T,v,h)}q&&xt(h,null,v,"beforeMount");const K=hu(b,U);K&&U.beforeEnter(A),s(A,d,g),((T=F&&F.onVnodeMounted)||K||q)&&Ce(()=>{T&&Ye(T,v,h),K&&U.enter(A),q&&xt(h,null,v,"mounted")},b)},W=(h,d,g,v,b)=>{if(g&&m(h,g),v)for(let R=0;R<v.length;R++)m(h,v[R]);if(b){let R=b.subTree;if(d===R||Fc(R.type)&&(R.ssContent===d||R.ssFallback===d)){const C=b.vnode;W(h,C,C.scopeId,C.slotScopeIds,b.parent)}}},be=(h,d,g,v,b,R,C,x,A=0)=>{for(let T=A;T<h.length;T++){const F=h[T]=x?bt(h[T]):tt(h[T]);E(null,F,d,g,v,b,R,C,x)}},Fe=(h,d,g,v,b,R,C)=>{const x=d.el=h.el;let{patchFlag:A,dynamicChildren:T,dirs:F}=d;A|=h.patchFlag&16;const N=h.props||ee,U=d.props||ee;let q;if(g&&Ot(g,!1),(q=U.onVnodeBeforeUpdate)&&Ye(q,g,d,h),F&&xt(d,h,g,"beforeUpdate"),g&&Ot(g,!0),(N.innerHTML&&U.innerHTML==null||N.textContent&&U.textContent==null)&&a(x,""),T?ze(h.dynamicChildren,T,x,g,v,er(d,b),R):C||z(h,d,x,null,g,v,er(d,b),R,!1),A>0){if(A&16)mt(x,N,U,g,b);else if(A&2&&N.class!==U.class&&i(x,"class",null,U.class,b),A&4&&i(x,"style",N.style,U.style,b),A&8){const K=d.dynamicProps;for(let ne=0;ne<K.length;ne++){const Q=K[ne],Ae=N[Q],pe=U[Q];(pe!==Ae||Q==="value")&&i(x,Q,Ae,pe,b,g)}}A&1&&h.children!==d.children&&a(x,d.children)}else!C&&T==null&&mt(x,N,U,g,b);((q=U.onVnodeUpdated)||F)&&Ce(()=>{q&&Ye(q,g,d,h),F&&xt(d,h,g,"updated")},v)},ze=(h,d,g,v,b,R,C)=>{for(let x=0;x<d.length;x++){const A=h[x],T=d[x],F=A.el&&(A.type===et||!an(A,T)||A.shapeFlag&70)?f(A.el):g;E(A,T,F,null,v,b,R,C,!0)}},mt=(h,d,g,v,b)=>{if(d!==g){if(d!==ee)for(const R in d)!_n(R)&&!(R in g)&&i(h,R,d[R],null,b,v);for(const R in g){if(_n(R))continue;const C=g[R],x=d[R];C!==x&&R!=="value"&&i(h,R,x,C,b,v)}"value"in g&&i(h,"value",d.value,g.value,b)}},At=(h,d,g,v,b,R,C,x,A)=>{const T=d.el=h?h.el:c(""),F=d.anchor=h?h.anchor:c("");let{patchFlag:N,dynamicChildren:U,slotScopeIds:q}=d;q&&(x=x?x.concat(q):q),h==null?(s(T,g,v),s(F,g,v),be(d.children||[],g,F,b,R,C,x,A)):N>0&&N&64&&U&&h.dynamicChildren?(ze(h.dynamicChildren,U,g,b,R,C,x),(d.key!=null||b&&d===b.subTree)&&Lc(h,d,!0)):z(h,d,g,F,b,R,C,x,A)},Je=(h,d,g,v,b,R,C,x,A)=>{d.slotScopeIds=x,h==null?d.shapeFlag&512?b.ctx.activate(d,g,v,C,A):on(d,g,v,b,R,C,A):Ft(h,d,A)},on=(h,d,g,v,b,R,C)=>{const x=h.component=Lu(h,v,b);if(bc(h)&&(x.ctx.renderer=D),Iu(x,!1,C),x.asyncDep){if(b&&b.registerDep(x,ue,C),!h.el){const A=x.subTree=ve(St);P(null,A,d,g)}}else ue(x,h,d,g,b,R,C)},Ft=(h,d,g)=>{const v=d.component=h.component;if(Eu(h,d,g))if(v.asyncDep&&!v.asyncResolved){Z(v,d,g);return}else v.next=d,v.update();else d.el=h.el,v.vnode=d},ue=(h,d,g,v,b,R,C)=>{const x=()=>{if(h.isMounted){let{next:N,bu:U,u:q,parent:K,vnode:ne}=h;{const xe=Ic(h);if(xe){N&&(N.el=ne.el,Z(h,N,C)),xe.asyncDep.then(()=>{h.isUnmounted||x()});return}}let Q=N,Ae;Ot(h,!1),N?(N.el=ne.el,Z(h,N,C)):N=ne,U&&zn(U),(Ae=N.props&&N.props.onVnodeBeforeUpdate)&&Ye(Ae,K,N,ne),Ot(h,!0);const pe=tr(h),Me=h.subTree;h.subTree=pe,E(Me,pe,f(Me.el),w(Me),h,b,R),N.el=pe.el,Q===null&&vu(h,pe.el),q&&Ce(q,b),(Ae=N.props&&N.props.onVnodeUpdated)&&Ce(()=>Ye(Ae,K,N,ne),b)}else{let N;const{el:U,props:q}=d,{bm:K,m:ne,parent:Q,root:Ae,type:pe}=h,Me=En(d);if(Ot(h,!1),K&&zn(K),!Me&&(N=q&&q.onVnodeBeforeMount)&&Ye(N,Q,d),Ot(h,!0),U&&ie){const xe=()=>{h.subTree=tr(h),ie(U,h.subTree,h,b,null)};Me&&pe.__asyncHydrate?pe.__asyncHydrate(U,h,xe):xe()}else{Ae.ce&&Ae.ce._injectChildStyle(pe);const xe=h.subTree=tr(h);E(null,xe,g,v,h,b,R),d.el=xe.el}if(ne&&Ce(ne,b),!Me&&(N=q&&q.onVnodeMounted)){const xe=d;Ce(()=>Ye(N,Q,xe),b)}(d.shapeFlag&256||Q&&En(Q.vnode)&&Q.vnode.shapeFlag&256)&&h.a&&Ce(h.a,b),h.isMounted=!0,d=g=v=null}};h.scope.on();const A=h.effect=new Wo(x);h.scope.off();const T=h.update=A.run.bind(A),F=h.job=A.runIfDirty.bind(A);F.i=h,F.id=h.uid,A.scheduler=()=>Qr(F),Ot(h,!0),T()},Z=(h,d,g)=>{d.component=h;const v=h.vnode.props;h.vnode=d,h.next=null,iu(h,d.props,v,g),au(h,d.children,g),ht(),Si(h),dt()},z=(h,d,g,v,b,R,C,x,A=!1)=>{const T=h&&h.children,F=h?h.shapeFlag:0,N=d.children,{patchFlag:U,shapeFlag:q}=d;if(U>0){if(U&128){gt(T,N,g,v,b,R,C,x,A);return}else if(U&256){it(T,N,g,v,b,R,C,x,A);return}}q&8?(F&16&&Le(T,b,R),N!==T&&a(g,N)):F&16?q&16?gt(T,N,g,v,b,R,C,x,A):Le(T,b,R,!0):(F&8&&a(g,""),q&16&&be(N,g,v,b,R,C,x,A))},it=(h,d,g,v,b,R,C,x,A)=>{h=h||jt,d=d||jt;const T=h.length,F=d.length,N=Math.min(T,F);let U;for(U=0;U<N;U++){const q=d[U]=A?bt(d[U]):tt(d[U]);E(h[U],q,g,null,b,R,C,x,A)}T>F?Le(h,b,R,!0,!1,N):be(d,g,v,b,R,C,x,A,N)},gt=(h,d,g,v,b,R,C,x,A)=>{let T=0;const F=d.length;let N=h.length-1,U=F-1;for(;T<=N&&T<=U;){const q=h[T],K=d[T]=A?bt(d[T]):tt(d[T]);if(an(q,K))E(q,K,g,null,b,R,C,x,A);else break;T++}for(;T<=N&&T<=U;){const q=h[N],K=d[U]=A?bt(d[U]):tt(d[U]);if(an(q,K))E(q,K,g,null,b,R,C,x,A);else break;N--,U--}if(T>N){if(T<=U){const q=U+1,K=q<F?d[q].el:v;for(;T<=U;)E(null,d[T]=A?bt(d[T]):tt(d[T]),g,K,b,R,C,x,A),T++}}else if(T>U)for(;T<=N;)Re(h[T],b,R,!0),T++;else{const q=T,K=T,ne=new Map;for(T=K;T<=U;T++){const Oe=d[T]=A?bt(d[T]):tt(d[T]);Oe.key!=null&&ne.set(Oe.key,T)}let Q,Ae=0;const pe=U-K+1;let Me=!1,xe=0;const cn=new Array(pe);for(T=0;T<pe;T++)cn[T]=0;for(T=q;T<=N;T++){const Oe=h[T];if(Ae>=pe){Re(Oe,b,R,!0);continue}let Xe;if(Oe.key!=null)Xe=ne.get(Oe.key);else for(Q=K;Q<=U;Q++)if(cn[Q-K]===0&&an(Oe,d[Q])){Xe=Q;break}Xe===void 0?Re(Oe,b,R,!0):(cn[Xe-K]=T+1,Xe>=xe?xe=Xe:Me=!0,E(Oe,d[Xe],g,null,b,R,C,x,A),Ae++)}const _i=Me?du(cn):jt;for(Q=_i.length-1,T=pe-1;T>=0;T--){const Oe=K+T,Xe=d[Oe],bi=Oe+1<F?d[Oe+1].el:v;cn[T]===0?E(null,Xe,g,bi,b,R,C,x,A):Me&&(Q<0||T!==_i[Q]?Ge(Xe,g,bi,2):Q--)}}},Ge=(h,d,g,v,b=null)=>{const{el:R,type:C,transition:x,children:A,shapeFlag:T}=h;if(T&6){Ge(h.component.subTree,d,g,v);return}if(T&128){h.suspense.move(d,g,v);return}if(T&64){C.move(h,d,g,D);return}if(C===et){s(R,d,g);for(let N=0;N<A.length;N++)Ge(A[N],d,g,v);s(h.anchor,d,g);return}if(C===Xn){L(h,d,g);return}if(v!==2&&T&1&&x)if(v===0)x.beforeEnter(R),s(R,d,g),Ce(()=>x.enter(R),b);else{const{leave:N,delayLeave:U,afterLeave:q}=x,K=()=>{h.ctx.isUnmounted?r(R):s(R,d,g)},ne=()=>{N(R,()=>{K(),q&&q()})};U?U(R,K,ne):ne()}else s(R,d,g)},Re=(h,d,g,v=!1,b=!1)=>{const{type:R,props:C,ref:x,children:A,dynamicChildren:T,shapeFlag:F,patchFlag:N,dirs:U,cacheIndex:q}=h;if(N===-2&&(b=!1),x!=null&&(ht(),ds(x,null,g,h,!0),dt()),q!=null&&(d.renderCache[q]=void 0),F&256){d.ctx.deactivate(h);return}const K=F&1&&U,ne=!En(h);let Q;if(ne&&(Q=C&&C.onVnodeBeforeUnmount)&&Ye(Q,d,h),F&6)Mn(h.component,g,v);else{if(F&128){h.suspense.unmount(g,v);return}K&&xt(h,null,d,"beforeUnmount"),F&64?h.type.remove(h,d,g,D,v):T&&!T.hasOnce&&(R!==et||N>0&&N&64)?Le(T,d,g,!1,!0):(R===et&&N&384||!b&&F&16)&&Le(A,d,g),v&&Mt(h)}(ne&&(Q=C&&C.onVnodeUnmounted)||K)&&Ce(()=>{Q&&Ye(Q,d,h),K&&xt(h,null,d,"unmounted")},g)},Mt=h=>{const{type:d,el:g,anchor:v,transition:b}=h;if(d===et){qt(g,v);return}if(d===Xn){I(h);return}const R=()=>{r(g),b&&!b.persisted&&b.afterLeave&&b.afterLeave()};if(h.shapeFlag&1&&b&&!b.persisted){const{leave:C,delayLeave:x}=b,A=()=>C(g,R);x?x(h.el,R,A):A()}else R()},qt=(h,d)=>{let g;for(;h!==d;)g=p(h),r(h),h=g;r(d)},Mn=(h,d,g)=>{const{bum:v,scope:b,job:R,subTree:C,um:x,m:A,a:T,parent:F,slots:{__:N}}=h;ki(A),ki(T),v&&zn(v),F&&M(N)&&N.forEach(U=>{F.renderCache[U]=void 0}),b.stop(),R&&(R.flags|=8,Re(C,h,d,g)),x&&Ce(x,d),Ce(()=>{h.isUnmounted=!0},d),d&&d.pendingBranch&&!d.isUnmounted&&h.asyncDep&&!h.asyncResolved&&h.suspenseId===d.pendingId&&(d.deps--,d.deps===0&&d.resolve())},Le=(h,d,g,v=!1,b=!1,R=0)=>{for(let C=R;C<h.length;C++)Re(h[C],d,g,v,b)},w=h=>{if(h.shapeFlag&6)return w(h.component.subTree);if(h.shapeFlag&128)return h.suspense.next();const d=p(h.anchor||h.el),g=d&&d[Na];return g?p(g):d};let B=!1;const k=(h,d,g)=>{h==null?d._vnode&&Re(d._vnode,null,null,!0):E(d._vnode||null,h,d,null,null,null,g),d._vnode=h,B||(B=!0,Si(),pc(),B=!1)},D={p:E,um:Re,m:Ge,r:Mt,mt:on,mc:be,pc:z,pbc:ze,n:w,o:e};let J,ie;return t&&([J,ie]=t(D)),{render:k,hydrate:J,createApp:su(k,J)}}function er({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Ot({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function hu(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Lc(e,t,n=!1){const s=e.children,r=t.children;if(M(s)&&M(r))for(let i=0;i<s.length;i++){const o=s[i];let c=r[i];c.shapeFlag&1&&!c.dynamicChildren&&((c.patchFlag<=0||c.patchFlag===32)&&(c=r[i]=bt(r[i]),c.el=o.el),!n&&c.patchFlag!==-2&&Lc(o,c)),c.type===Ns&&(c.el=o.el),c.type===St&&!c.el&&(c.el=o.el)}}function du(e){const t=e.slice(),n=[0];let s,r,i,o,c;const l=e.length;for(s=0;s<l;s++){const u=e[s];if(u!==0){if(r=n[n.length-1],e[r]<u){t[s]=r,n.push(s);continue}for(i=0,o=n.length-1;i<o;)c=i+o>>1,e[n[c]]<u?i=c+1:o=c;u<e[n[i]]&&(i>0&&(t[s]=n[i-1]),n[i]=s)}}for(i=n.length,o=n[i-1];i-- >0;)n[i]=o,o=t[o];return n}function Ic(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Ic(t)}function ki(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const pu=Symbol.for("v-scx"),mu=()=>He(pu);function Gn(e,t,n){return Bc(e,t,n)}function Bc(e,t,n=ee){const{immediate:s,deep:r,flush:i,once:o}=n,c=he({},n),l=t&&s||!t&&i!=="post";let u;if(kn){if(i==="sync"){const m=mu();u=m.__watcherHandles||(m.__watcherHandles=[])}else if(!l){const m=()=>{};return m.stop=Ve,m.resume=Ve,m.pause=Ve,m}}const a=ge;c.call=(m,_,E)=>st(m,a,_,E);let f=!1;i==="post"?c.scheduler=m=>{Ce(m,a&&a.suspense)}:i!=="sync"&&(f=!0,c.scheduler=(m,_)=>{_?m():Qr(m)}),c.augmentJob=m=>{t&&(m.flags|=4),f&&(m.flags|=2,a&&(m.id=a.uid,m.i=a))};const p=xa(e,t,c);return kn&&(u?u.push(p):l&&p()),p}function gu(e,t,n){const s=this.proxy,r=le(e)?e.includes(".")?Dc(s,e):()=>s[e]:e.bind(s,s);let i;V(t)?i=t:(i=t.handler,n=t);const o=Un(this),c=Bc(r,i.bind(s),n);return o(),c}function Dc(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const yu=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Ue(t)}Modifiers`]||e[`${Dt(t)}Modifiers`];function _u(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||ee;let r=n;const i=t.startsWith("update:"),o=i&&yu(s,t.slice(7));o&&(o.trim&&(r=n.map(a=>le(a)?a.trim():a)),o.number&&(r=n.map(ls)));let c,l=s[c=Js(t)]||s[c=Js(Ue(t))];!l&&i&&(l=s[c=Js(Dt(t))]),l&&st(l,e,6,r);const u=s[c+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[c])return;e.emitted[c]=!0,st(u,e,6,r)}}function Uc(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const i=e.emits;let o={},c=!1;if(!V(e)){const l=u=>{const a=Uc(u,t,!0);a&&(c=!0,he(o,a))};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!i&&!c?(re(e)&&s.set(e,null),null):(M(i)?i.forEach(l=>o[l]=null):he(o,i),re(e)&&s.set(e,o),o)}function ks(e,t){return!e||!Rs(t)?!1:(t=t.slice(2).replace(/Once$/,""),Y(e,t[0].toLowerCase()+t.slice(1))||Y(e,Dt(t))||Y(e,t))}function tr(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[i],slots:o,attrs:c,emit:l,render:u,renderCache:a,props:f,data:p,setupState:m,ctx:_,inheritAttrs:E}=e,S=hs(e);let P,O;try{if(n.shapeFlag&4){const I=r||s,$=I;P=tt(u.call($,I,a,f,m,p,_)),O=c}else{const I=t;P=tt(I.length>1?I(f,{attrs:c,slots:o,emit:l}):I(f,null)),O=t.props?c:bu(c)}}catch(I){Rn.length=0,Cs(I,e,1),P=ve(St)}let L=P;if(O&&E!==!1){const I=Object.keys(O),{shapeFlag:$}=L;I.length&&$&7&&(i&&I.some(Mr)&&(O=wu(O,i)),L=Yt(L,O,!1,!0))}return n.dirs&&(L=Yt(L,null,!1,!0),L.dirs=L.dirs?L.dirs.concat(n.dirs):n.dirs),n.transition&&Zr(L,n.transition),P=L,hs(S),P}const bu=e=>{let t;for(const n in e)(n==="class"||n==="style"||Rs(n))&&((t||(t={}))[n]=e[n]);return t},wu=(e,t)=>{const n={};for(const s in e)(!Mr(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function Eu(e,t,n){const{props:s,children:r,component:i}=e,{props:o,children:c,patchFlag:l}=t,u=i.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&l>=0){if(l&1024)return!0;if(l&16)return s?Ni(s,o,u):!!o;if(l&8){const a=t.dynamicProps;for(let f=0;f<a.length;f++){const p=a[f];if(o[p]!==s[p]&&!ks(u,p))return!0}}}else return(r||c)&&(!c||!c.$stable)?!0:s===o?!1:s?o?Ni(s,o,u):!0:!!o;return!1}function Ni(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const i=s[r];if(t[i]!==e[i]&&!ks(n,i))return!0}return!1}function vu({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const Fc=e=>e.__isSuspense;function Ru(e,t){t&&t.pendingBranch?M(e)?t.effects.push(...e):t.effects.push(e):Pa(e)}const et=Symbol.for("v-fgt"),Ns=Symbol.for("v-txt"),St=Symbol.for("v-cmt"),Xn=Symbol.for("v-stc"),Rn=[];let Ne=null;function gn(e=!1){Rn.push(Ne=e?null:[])}function Su(){Rn.pop(),Ne=Rn[Rn.length-1]||null}let Pn=1;function Li(e,t=!1){Pn+=e,e<0&&Ne&&t&&(Ne.hasOnce=!0)}function Mc(e){return e.dynamicChildren=Pn>0?Ne||jt:null,Su(),Pn>0&&Ne&&Ne.push(e),e}function Hn(e,t,n,s,r,i){return Mc(gs(e,t,n,s,r,i,!0))}function Tu(e,t,n,s,r){return Mc(ve(e,t,n,s,r,!0))}function ms(e){return e?e.__v_isVNode===!0:!1}function an(e,t){return e.type===t.type&&e.key===t.key}const qc=({key:e})=>e??null,Yn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?le(e)||_e(e)||V(e)?{i:ke,r:e,k:t,f:!!n}:e:null);function gs(e,t=null,n=null,s=0,r=null,i=e===et?0:1,o=!1,c=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&qc(t),ref:t&&Yn(t),scopeId:gc,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:ke};return c?(si(l,n),i&128&&e.normalize(l)):n&&(l.shapeFlag|=le(n)?8:16),Pn>0&&!o&&Ne&&(l.patchFlag>0||i&6)&&l.patchFlag!==32&&Ne.push(l),l}const ve=Au;function Au(e,t=null,n=null,s=0,r=null,i=!1){if((!e||e===za)&&(e=St),ms(e)){const c=Yt(e,t,!0);return n&&si(c,n),Pn>0&&!i&&Ne&&(c.shapeFlag&6?Ne[Ne.indexOf(e)]=c:Ne.push(c)),c.patchFlag=-2,c}if(Mu(e)&&(e=e.__vccOpts),t){t=xu(t);let{class:c,style:l}=t;c&&!le(c)&&(t.class=An(c)),re(l)&&(Xr(l)&&!M(l)&&(l=he({},l)),t.style=$r(l))}const o=le(e)?1:Fc(e)?128:La(e)?64:re(e)?4:V(e)?2:0;return gs(e,t,n,s,r,o,i,!0)}function xu(e){return e?Xr(e)||xc(e)?he({},e):e:null}function Yt(e,t,n=!1,s=!1){const{props:r,ref:i,patchFlag:o,children:c,transition:l}=e,u=t?Pu(r||{},t):r,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&qc(u),ref:t&&t.ref?n&&i?M(i)?i.concat(Yn(t)):[i,Yn(t)]:Yn(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:c,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==et?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Yt(e.ssContent),ssFallback:e.ssFallback&&Yt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&s&&Zr(a,l.clone(a)),a}function Ou(e=" ",t=0){return ve(Ns,null,e,t)}function hm(e,t){const n=ve(Xn,null,e);return n.staticCount=t,n}function Cu(e="",t=!1){return t?(gn(),Tu(St,null,e)):ve(St,null,e)}function tt(e){return e==null||typeof e=="boolean"?ve(St):M(e)?ve(et,null,e.slice()):ms(e)?bt(e):ve(Ns,null,String(e))}function bt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Yt(e)}function si(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(M(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),si(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!xc(t)?t._ctx=ke:r===3&&ke&&(ke.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else V(t)?(t={default:t,_ctx:ke},n=32):(t=String(t),s&64?(n=16,t=[Ou(t)]):n=8);e.children=t,e.shapeFlag|=n}function Pu(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=An([t.class,s.class]));else if(r==="style")t.style=$r([t.style,s.style]);else if(Rs(r)){const i=t[r],o=s[r];o&&i!==o&&!(M(i)&&i.includes(o))&&(t[r]=i?[].concat(i,o):o)}else r!==""&&(t[r]=s[r])}return t}function Ye(e,t,n,s=null){st(e,t,7,[n,s])}const ku=Sc();let Nu=0;function Lu(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||ku,i={uid:Nu++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Ko(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Cc(s,r),emitsOptions:Uc(s,r),emit:null,emitted:null,propsDefaults:ee,inheritAttrs:s.inheritAttrs,ctx:ee,data:ee,props:ee,attrs:ee,slots:ee,refs:ee,setupState:ee,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=_u.bind(null,i),e.ce&&e.ce(i),i}let ge=null,ys,Er;{const e=As(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),i=>{r.length>1?r.forEach(o=>o(i)):r[0](i)}};ys=t("__VUE_INSTANCE_SETTERS__",n=>ge=n),Er=t("__VUE_SSR_SETTERS__",n=>kn=n)}const Un=e=>{const t=ge;return ys(e),e.scope.on(),()=>{e.scope.off(),ys(t)}},Ii=()=>{ge&&ge.scope.off(),ys(null)};function Vc(e){return e.vnode.shapeFlag&4}let kn=!1;function Iu(e,t=!1,n=!1){t&&Er(t);const{props:s,children:r}=e.vnode,i=Vc(e);ru(e,s,i,t),lu(e,r,n||t);const o=i?Bu(e,t):void 0;return t&&Er(!1),o}function Bu(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Xa);const{setup:s}=n;if(s){ht();const r=e.setupContext=s.length>1?Uu(e):null,i=Un(e),o=Dn(s,e,0,[e.props,r]),c=Uo(o);if(dt(),i(),(c||e.sp)&&!En(e)&&_c(e),c){if(o.then(Ii,Ii),t)return o.then(l=>{Bi(e,l,t)}).catch(l=>{Cs(l,e,0)});e.asyncDep=o}else Bi(e,o,t)}else $c(e,t)}function Bi(e,t,n){V(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:re(t)&&(e.setupState=fc(t)),$c(e,n)}let Di;function $c(e,t,n){const s=e.type;if(!e.render){if(!t&&Di&&!s.render){const r=s.template||ei(e).template;if(r){const{isCustomElement:i,compilerOptions:o}=e.appContext.config,{delimiters:c,compilerOptions:l}=s,u=he(he({isCustomElement:i,delimiters:c},o),l);s.render=Di(r,u)}}e.render=s.render||Ve}{const r=Un(e);ht();try{Ya(e)}finally{dt(),r()}}}const Du={get(e,t){return me(e,"get",""),e[t]}};function Uu(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Du),slots:e.slots,emit:e.emit,expose:t}}function Ls(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(fc(lc(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in vn)return vn[n](e)},has(t,n){return n in t||n in vn}})):e.proxy}function Fu(e,t=!0){return V(e)?e.displayName||e.name:e.name||t&&e.__name}function Mu(e){return V(e)&&"__vccOpts"in e}const Pe=(e,t)=>Ta(e,t,kn);function Hc(e,t,n){const s=arguments.length;return s===2?re(t)&&!M(t)?ms(t)?ve(e,null,[t]):ve(e,t):ve(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&ms(n)&&(n=[n]),ve(e,t,n))}const qu="3.5.14";/**
* @vue/runtime-dom v3.5.14
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let vr;const Ui=typeof window<"u"&&window.trustedTypes;if(Ui)try{vr=Ui.createPolicy("vue",{createHTML:e=>e})}catch{}const jc=vr?e=>vr.createHTML(e):e=>e,Vu="http://www.w3.org/2000/svg",$u="http://www.w3.org/1998/Math/MathML",lt=typeof document<"u"?document:null,Fi=lt&&lt.createElement("template"),Hu={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?lt.createElementNS(Vu,e):t==="mathml"?lt.createElementNS($u,e):n?lt.createElement(e,{is:n}):lt.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>lt.createTextNode(e),createComment:e=>lt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>lt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,i){const o=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===i||!(r=r.nextSibling)););else{Fi.innerHTML=jc(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const c=Fi.content;if(s==="svg"||s==="mathml"){const l=c.firstChild;for(;l.firstChild;)c.appendChild(l.firstChild);c.removeChild(l)}t.insertBefore(c,n)}return[o?o.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},ju=Symbol("_vtc");function Ku(e,t,n){const s=e[ju];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const _s=Symbol("_vod"),Kc=Symbol("_vsh"),dm={beforeMount(e,{value:t},{transition:n}){e[_s]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):un(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:s}){!t!=!n&&(s?t?(s.beforeEnter(e),un(e,!0),s.enter(e)):s.leave(e,()=>{un(e,!1)}):un(e,t))},beforeUnmount(e,{value:t}){un(e,t)}};function un(e,t){e.style.display=t?e[_s]:"none",e[Kc]=!t}const Wu=Symbol(""),zu=/(^|;)\s*display\s*:/;function Ju(e,t,n){const s=e.style,r=le(n);let i=!1;if(n&&!r){if(t)if(le(t))for(const o of t.split(";")){const c=o.slice(0,o.indexOf(":")).trim();n[c]==null&&Qn(s,c,"")}else for(const o in t)n[o]==null&&Qn(s,o,"");for(const o in n)o==="display"&&(i=!0),Qn(s,o,n[o])}else if(r){if(t!==n){const o=s[Wu];o&&(n+=";"+o),s.cssText=n,i=zu.test(n)}}else t&&e.removeAttribute("style");_s in e&&(e[_s]=i?s.display:"",e[Kc]&&(s.display="none"))}const Mi=/\s*!important$/;function Qn(e,t,n){if(M(n))n.forEach(s=>Qn(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=Gu(e,t);Mi.test(n)?e.setProperty(Dt(s),n.replace(Mi,""),"important"):e[s]=n}}const qi=["Webkit","Moz","ms"],nr={};function Gu(e,t){const n=nr[t];if(n)return n;let s=Ue(t);if(s!=="filter"&&s in e)return nr[t]=s;s=Ts(s);for(let r=0;r<qi.length;r++){const i=qi[r]+s;if(i in e)return nr[t]=i}return t}const Vi="http://www.w3.org/1999/xlink";function $i(e,t,n,s,r,i=Zl(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Vi,t.slice(6,t.length)):e.setAttributeNS(Vi,t,n):n==null||i&&!Vo(n)?e.removeAttribute(t):e.setAttribute(t,i?"":nt(n)?String(n):n)}function Hi(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?jc(n):n);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const c=i==="OPTION"?e.getAttribute("value")||"":e.value,l=n==null?e.type==="checkbox"?"on":"":String(n);(c!==l||!("_value"in e))&&(e.value=l),n==null&&e.removeAttribute(t),e._value=n;return}let o=!1;if(n===""||n==null){const c=typeof e[t];c==="boolean"?n=Vo(n):n==null&&c==="string"?(n="",o=!0):c==="number"&&(n=0,o=!0)}try{e[t]=n}catch{}o&&e.removeAttribute(r||t)}function ft(e,t,n,s){e.addEventListener(t,n,s)}function Xu(e,t,n,s){e.removeEventListener(t,n,s)}const ji=Symbol("_vei");function Yu(e,t,n,s,r=null){const i=e[ji]||(e[ji]={}),o=i[t];if(s&&o)o.value=s;else{const[c,l]=Qu(t);if(s){const u=i[t]=tf(s,r);ft(e,c,u,l)}else o&&(Xu(e,c,o,l),i[t]=void 0)}}const Ki=/(?:Once|Passive|Capture)$/;function Qu(e){let t;if(Ki.test(e)){t={};let s;for(;s=e.match(Ki);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Dt(e.slice(2)),t]}let sr=0;const Zu=Promise.resolve(),ef=()=>sr||(Zu.then(()=>sr=0),sr=Date.now());function tf(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;st(nf(s,n.value),t,5,[s])};return n.value=e,n.attached=ef(),n}function nf(e,t){if(M(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const Wi=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,sf=(e,t,n,s,r,i)=>{const o=r==="svg";t==="class"?Ku(e,s,o):t==="style"?Ju(e,n,s):Rs(t)?Mr(t)||Yu(e,t,n,s,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):rf(e,t,s,o))?(Hi(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&$i(e,t,s,o,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!le(s))?Hi(e,Ue(t),s,i,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),$i(e,t,s,o))};function rf(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&Wi(t)&&V(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return Wi(t)&&le(n)?!1:t in e}const Tt=e=>{const t=e.props["onUpdate:modelValue"]||!1;return M(t)?n=>zn(t,n):t};function of(e){e.target.composing=!0}function zi(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const De=Symbol("_assign"),Ji={created(e,{modifiers:{lazy:t,trim:n,number:s}},r){e[De]=Tt(r);const i=s||r.props&&r.props.type==="number";ft(e,t?"change":"input",o=>{if(o.target.composing)return;let c=e.value;n&&(c=c.trim()),i&&(c=ls(c)),e[De](c)}),n&&ft(e,"change",()=>{e.value=e.value.trim()}),t||(ft(e,"compositionstart",of),ft(e,"compositionend",zi),ft(e,"change",zi))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:r,number:i}},o){if(e[De]=Tt(o),e.composing)return;const c=(i||e.type==="number")&&!/^0\d/.test(e.value)?ls(e.value):e.value,l=t??"";c!==l&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||r&&e.value.trim()===l)||(e.value=l))}},cf={deep:!0,created(e,t,n){e[De]=Tt(n),ft(e,"change",()=>{const s=e._modelValue,r=Qt(e),i=e.checked,o=e[De];if(M(s)){const c=Hr(s,r),l=c!==-1;if(i&&!l)o(s.concat(r));else if(!i&&l){const u=[...s];u.splice(c,1),o(u)}}else if(tn(s)){const c=new Set(s);i?c.add(r):c.delete(r),o(c)}else o(Wc(e,i))})},mounted:Gi,beforeUpdate(e,t,n){e[De]=Tt(n),Gi(e,t,n)}};function Gi(e,{value:t,oldValue:n},s){e._modelValue=t;let r;if(M(t))r=Hr(t,s.props.value)>-1;else if(tn(t))r=t.has(s.props.value);else{if(t===n)return;r=Nt(t,Wc(e,!0))}e.checked!==r&&(e.checked=r)}const lf={created(e,{value:t},n){e.checked=Nt(t,n.props.value),e[De]=Tt(n),ft(e,"change",()=>{e[De](Qt(e))})},beforeUpdate(e,{value:t,oldValue:n},s){e[De]=Tt(s),t!==n&&(e.checked=Nt(t,s.props.value))}},af={deep:!0,created(e,{value:t,modifiers:{number:n}},s){const r=tn(t);ft(e,"change",()=>{const i=Array.prototype.filter.call(e.options,o=>o.selected).map(o=>n?ls(Qt(o)):Qt(o));e[De](e.multiple?r?new Set(i):i:i[0]),e._assigning=!0,Yr(()=>{e._assigning=!1})}),e[De]=Tt(s)},mounted(e,{value:t}){Xi(e,t)},beforeUpdate(e,t,n){e[De]=Tt(n)},updated(e,{value:t}){e._assigning||Xi(e,t)}};function Xi(e,t){const n=e.multiple,s=M(t);if(!(n&&!s&&!tn(t))){for(let r=0,i=e.options.length;r<i;r++){const o=e.options[r],c=Qt(o);if(n)if(s){const l=typeof c;l==="string"||l==="number"?o.selected=t.some(u=>String(u)===String(c)):o.selected=Hr(t,c)>-1}else o.selected=t.has(c);else if(Nt(Qt(o),t)){e.selectedIndex!==r&&(e.selectedIndex=r);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function Qt(e){return"_value"in e?e._value:e.value}function Wc(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const pm={created(e,t,n){jn(e,t,n,null,"created")},mounted(e,t,n){jn(e,t,n,null,"mounted")},beforeUpdate(e,t,n,s){jn(e,t,n,s,"beforeUpdate")},updated(e,t,n,s){jn(e,t,n,s,"updated")}};function uf(e,t){switch(e){case"SELECT":return af;case"TEXTAREA":return Ji;default:switch(t){case"checkbox":return cf;case"radio":return lf;default:return Ji}}}function jn(e,t,n,s,r){const o=uf(e.tagName,n.props&&n.props.type)[r];o&&o(e,t,n,s)}const ff=["ctrl","shift","alt","meta"],hf={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>ff.some(n=>e[`${n}Key`]&&!t.includes(n))},mm=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(r,...i)=>{for(let o=0;o<t.length;o++){const c=hf[t[o]];if(c&&c(r,t))return}return e(r,...i)})},df=he({patchProp:sf},Hu);let Yi;function pf(){return Yi||(Yi=uu(df))}const mf=(...e)=>{const t=pf().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=yf(s);if(!r)return;const i=t._component;!V(i)&&!i.render&&!i.template&&(i.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const o=n(r,!1,gf(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),o},t};function gf(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function yf(e){return le(e)?document.querySelector(e):e}var _f=!1;/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const bf=Symbol();var Qi;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(Qi||(Qi={}));function wf(){const e=ta(!0),t=e.run(()=>ac({}));let n=[],s=[];const r=lc({install(i){r._a=i,i.provide(bf,r),i.config.globalProperties.$pinia=r,s.forEach(o=>n.push(o)),s=[]},use(i){return!this._a&&!_f?s.push(i):n.push(i),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Ht=typeof document<"u";function zc(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Ef(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&zc(e.default)}const G=Object.assign;function rr(e,t){const n={};for(const s in t){const r=t[s];n[s]=Ke(r)?r.map(e):e(r)}return n}const Sn=()=>{},Ke=Array.isArray,Jc=/#/g,vf=/&/g,Rf=/\//g,Sf=/=/g,Tf=/\?/g,Gc=/\+/g,Af=/%5B/g,xf=/%5D/g,Xc=/%5E/g,Of=/%60/g,Yc=/%7B/g,Cf=/%7C/g,Qc=/%7D/g,Pf=/%20/g;function ri(e){return encodeURI(""+e).replace(Cf,"|").replace(Af,"[").replace(xf,"]")}function kf(e){return ri(e).replace(Yc,"{").replace(Qc,"}").replace(Xc,"^")}function Rr(e){return ri(e).replace(Gc,"%2B").replace(Pf,"+").replace(Jc,"%23").replace(vf,"%26").replace(Of,"`").replace(Yc,"{").replace(Qc,"}").replace(Xc,"^")}function Nf(e){return Rr(e).replace(Sf,"%3D")}function Lf(e){return ri(e).replace(Jc,"%23").replace(Tf,"%3F")}function If(e){return e==null?"":Lf(e).replace(Rf,"%2F")}function Nn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Bf=/\/$/,Df=e=>e.replace(Bf,"");function ir(e,t,n="/"){let s,r={},i="",o="";const c=t.indexOf("#");let l=t.indexOf("?");return c<l&&c>=0&&(l=-1),l>-1&&(s=t.slice(0,l),i=t.slice(l+1,c>-1?c:t.length),r=e(i)),c>-1&&(s=s||t.slice(0,c),o=t.slice(c,t.length)),s=qf(s??t,n),{fullPath:s+(i&&"?")+i+o,path:s,query:r,hash:Nn(o)}}function Uf(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Zi(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Ff(e,t,n){const s=t.matched.length-1,r=n.matched.length-1;return s>-1&&s===r&&Zt(t.matched[s],n.matched[r])&&Zc(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Zt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Zc(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Mf(e[n],t[n]))return!1;return!0}function Mf(e,t){return Ke(e)?eo(e,t):Ke(t)?eo(t,e):e===t}function eo(e,t){return Ke(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function qf(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),r=s[s.length-1];(r===".."||r===".")&&s.push("");let i=n.length-1,o,c;for(o=0;o<s.length;o++)if(c=s[o],c!==".")if(c==="..")i>1&&i--;else break;return n.slice(0,i).join("/")+"/"+s.slice(o).join("/")}const yt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Ln;(function(e){e.pop="pop",e.push="push"})(Ln||(Ln={}));var Tn;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Tn||(Tn={}));function Vf(e){if(!e)if(Ht){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Df(e)}const $f=/^[^#]+#/;function Hf(e,t){return e.replace($f,"#")+t}function jf(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const Is=()=>({left:window.scrollX,top:window.scrollY});function Kf(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=jf(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function to(e,t){return(history.state?history.state.position-t:-1)+e}const Sr=new Map;function Wf(e,t){Sr.set(e,t)}function zf(e){const t=Sr.get(e);return Sr.delete(e),t}let Jf=()=>location.protocol+"//"+location.host;function el(e,t){const{pathname:n,search:s,hash:r}=t,i=e.indexOf("#");if(i>-1){let c=r.includes(e.slice(i))?e.slice(i).length:1,l=r.slice(c);return l[0]!=="/"&&(l="/"+l),Zi(l,"")}return Zi(n,e)+s+r}function Gf(e,t,n,s){let r=[],i=[],o=null;const c=({state:p})=>{const m=el(e,location),_=n.value,E=t.value;let S=0;if(p){if(n.value=m,t.value=p,o&&o===_){o=null;return}S=E?p.position-E.position:0}else s(m);r.forEach(P=>{P(n.value,_,{delta:S,type:Ln.pop,direction:S?S>0?Tn.forward:Tn.back:Tn.unknown})})};function l(){o=n.value}function u(p){r.push(p);const m=()=>{const _=r.indexOf(p);_>-1&&r.splice(_,1)};return i.push(m),m}function a(){const{history:p}=window;p.state&&p.replaceState(G({},p.state,{scroll:Is()}),"")}function f(){for(const p of i)p();i=[],window.removeEventListener("popstate",c),window.removeEventListener("beforeunload",a)}return window.addEventListener("popstate",c),window.addEventListener("beforeunload",a,{passive:!0}),{pauseListeners:l,listen:u,destroy:f}}function no(e,t,n,s=!1,r=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:r?Is():null}}function Xf(e){const{history:t,location:n}=window,s={value:el(e,n)},r={value:t.state};r.value||i(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function i(l,u,a){const f=e.indexOf("#"),p=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+l:Jf()+e+l;try{t[a?"replaceState":"pushState"](u,"",p),r.value=u}catch(m){console.error(m),n[a?"replace":"assign"](p)}}function o(l,u){const a=G({},t.state,no(r.value.back,l,r.value.forward,!0),u,{position:r.value.position});i(l,a,!0),s.value=l}function c(l,u){const a=G({},r.value,t.state,{forward:l,scroll:Is()});i(a.current,a,!0);const f=G({},no(s.value,l,null),{position:a.position+1},u);i(l,f,!1),s.value=l}return{location:s,state:r,push:c,replace:o}}function Yf(e){e=Vf(e);const t=Xf(e),n=Gf(e,t.state,t.location,t.replace);function s(i,o=!0){o||n.pauseListeners(),history.go(i)}const r=G({location:"",base:e,go:s,createHref:Hf.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function Qf(e){return typeof e=="string"||e&&typeof e=="object"}function tl(e){return typeof e=="string"||typeof e=="symbol"}const nl=Symbol("");var so;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(so||(so={}));function en(e,t){return G(new Error,{type:e,[nl]:!0},t)}function ct(e,t){return e instanceof Error&&nl in e&&(t==null||!!(e.type&t))}const ro="[^/]+?",Zf={sensitive:!1,strict:!1,start:!0,end:!0},eh=/[.+*?^${}()[\]/\\]/g;function th(e,t){const n=G({},Zf,t),s=[];let r=n.start?"^":"";const i=[];for(const u of e){const a=u.length?[]:[90];n.strict&&!u.length&&(r+="/");for(let f=0;f<u.length;f++){const p=u[f];let m=40+(n.sensitive?.25:0);if(p.type===0)f||(r+="/"),r+=p.value.replace(eh,"\\$&"),m+=40;else if(p.type===1){const{value:_,repeatable:E,optional:S,regexp:P}=p;i.push({name:_,repeatable:E,optional:S});const O=P||ro;if(O!==ro){m+=10;try{new RegExp(`(${O})`)}catch(I){throw new Error(`Invalid custom RegExp for param "${_}" (${O}): `+I.message)}}let L=E?`((?:${O})(?:/(?:${O}))*)`:`(${O})`;f||(L=S&&u.length<2?`(?:/${L})`:"/"+L),S&&(L+="?"),r+=L,m+=20,S&&(m+=-8),E&&(m+=-20),O===".*"&&(m+=-50)}a.push(m)}s.push(a)}if(n.strict&&n.end){const u=s.length-1;s[u][s[u].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const o=new RegExp(r,n.sensitive?"":"i");function c(u){const a=u.match(o),f={};if(!a)return null;for(let p=1;p<a.length;p++){const m=a[p]||"",_=i[p-1];f[_.name]=m&&_.repeatable?m.split("/"):m}return f}function l(u){let a="",f=!1;for(const p of e){(!f||!a.endsWith("/"))&&(a+="/"),f=!1;for(const m of p)if(m.type===0)a+=m.value;else if(m.type===1){const{value:_,repeatable:E,optional:S}=m,P=_ in u?u[_]:"";if(Ke(P)&&!E)throw new Error(`Provided param "${_}" is an array but it is not repeatable (* or + modifiers)`);const O=Ke(P)?P.join("/"):P;if(!O)if(S)p.length<2&&(a.endsWith("/")?a=a.slice(0,-1):f=!0);else throw new Error(`Missing required param "${_}"`);a+=O}}return a||"/"}return{re:o,score:s,keys:i,parse:c,stringify:l}}function nh(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===40+40?-1:1:e.length>t.length?t.length===1&&t[0]===40+40?1:-1:0}function sl(e,t){let n=0;const s=e.score,r=t.score;for(;n<s.length&&n<r.length;){const i=nh(s[n],r[n]);if(i)return i;n++}if(Math.abs(r.length-s.length)===1){if(io(s))return 1;if(io(r))return-1}return r.length-s.length}function io(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const sh={type:0,value:""},rh=/[a-zA-Z0-9_]/;function ih(e){if(!e)return[[]];if(e==="/")return[[sh]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${u}": ${m}`)}let n=0,s=n;const r=[];let i;function o(){i&&r.push(i),i=[]}let c=0,l,u="",a="";function f(){u&&(n===0?i.push({type:0,value:u}):n===1||n===2||n===3?(i.length>1&&(l==="*"||l==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:u,regexp:a,repeatable:l==="*"||l==="+",optional:l==="*"||l==="?"})):t("Invalid state to consume buffer"),u="")}function p(){u+=l}for(;c<e.length;){if(l=e[c++],l==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:l==="/"?(u&&f(),o()):l===":"?(f(),n=1):p();break;case 4:p(),n=s;break;case 1:l==="("?n=2:rh.test(l)?p():(f(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&c--);break;case 2:l===")"?a[a.length-1]=="\\"?a=a.slice(0,-1)+l:n=3:a+=l;break;case 3:f(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&c--,a="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${u}"`),f(),o(),r}function oh(e,t,n){const s=th(ih(e.path),n),r=G(s,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function ch(e,t){const n=[],s=new Map;t=ao({strict:!1,end:!0,sensitive:!1},t);function r(f){return s.get(f)}function i(f,p,m){const _=!m,E=co(f);E.aliasOf=m&&m.record;const S=ao(t,f),P=[E];if("alias"in f){const I=typeof f.alias=="string"?[f.alias]:f.alias;for(const $ of I)P.push(co(G({},E,{components:m?m.record.components:E.components,path:$,aliasOf:m?m.record:E})))}let O,L;for(const I of P){const{path:$}=I;if(p&&$[0]!=="/"){const te=p.record.path,W=te[te.length-1]==="/"?"":"/";I.path=p.record.path+($&&W+$)}if(O=oh(I,p,S),m?m.alias.push(O):(L=L||O,L!==O&&L.alias.push(O),_&&f.name&&!lo(O)&&o(f.name)),rl(O)&&l(O),E.children){const te=E.children;for(let W=0;W<te.length;W++)i(te[W],O,m&&m.children[W])}m=m||O}return L?()=>{o(L)}:Sn}function o(f){if(tl(f)){const p=s.get(f);p&&(s.delete(f),n.splice(n.indexOf(p),1),p.children.forEach(o),p.alias.forEach(o))}else{const p=n.indexOf(f);p>-1&&(n.splice(p,1),f.record.name&&s.delete(f.record.name),f.children.forEach(o),f.alias.forEach(o))}}function c(){return n}function l(f){const p=uh(f,n);n.splice(p,0,f),f.record.name&&!lo(f)&&s.set(f.record.name,f)}function u(f,p){let m,_={},E,S;if("name"in f&&f.name){if(m=s.get(f.name),!m)throw en(1,{location:f});S=m.record.name,_=G(oo(p.params,m.keys.filter(L=>!L.optional).concat(m.parent?m.parent.keys.filter(L=>L.optional):[]).map(L=>L.name)),f.params&&oo(f.params,m.keys.map(L=>L.name))),E=m.stringify(_)}else if(f.path!=null)E=f.path,m=n.find(L=>L.re.test(E)),m&&(_=m.parse(E),S=m.record.name);else{if(m=p.name?s.get(p.name):n.find(L=>L.re.test(p.path)),!m)throw en(1,{location:f,currentLocation:p});S=m.record.name,_=G({},p.params,f.params),E=m.stringify(_)}const P=[];let O=m;for(;O;)P.unshift(O.record),O=O.parent;return{name:S,path:E,params:_,matched:P,meta:ah(P)}}e.forEach(f=>i(f));function a(){n.length=0,s.clear()}return{addRoute:i,resolve:u,removeRoute:o,clearRoutes:a,getRoutes:c,getRecordMatcher:r}}function oo(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function co(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:lh(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function lh(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function lo(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function ah(e){return e.reduce((t,n)=>G(t,n.meta),{})}function ao(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function uh(e,t){let n=0,s=t.length;for(;n!==s;){const i=n+s>>1;sl(e,t[i])<0?s=i:n=i+1}const r=fh(e);return r&&(s=t.lastIndexOf(r,s-1)),s}function fh(e){let t=e;for(;t=t.parent;)if(rl(t)&&sl(e,t)===0)return t}function rl({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function hh(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<s.length;++r){const i=s[r].replace(Gc," "),o=i.indexOf("="),c=Nn(o<0?i:i.slice(0,o)),l=o<0?null:Nn(i.slice(o+1));if(c in t){let u=t[c];Ke(u)||(u=t[c]=[u]),u.push(l)}else t[c]=l}return t}function uo(e){let t="";for(let n in e){const s=e[n];if(n=Nf(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(Ke(s)?s.map(i=>i&&Rr(i)):[s&&Rr(s)]).forEach(i=>{i!==void 0&&(t+=(t.length?"&":"")+n,i!=null&&(t+="="+i))})}return t}function dh(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=Ke(s)?s.map(r=>r==null?null:""+r):s==null?s:""+s)}return t}const ph=Symbol(""),fo=Symbol(""),Bs=Symbol(""),ii=Symbol(""),Tr=Symbol("");function fn(){let e=[];function t(s){return e.push(s),()=>{const r=e.indexOf(s);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function wt(e,t,n,s,r,i=o=>o()){const o=s&&(s.enterCallbacks[r]=s.enterCallbacks[r]||[]);return()=>new Promise((c,l)=>{const u=p=>{p===!1?l(en(4,{from:n,to:t})):p instanceof Error?l(p):Qf(p)?l(en(2,{from:t,to:p})):(o&&s.enterCallbacks[r]===o&&typeof p=="function"&&o.push(p),c())},a=i(()=>e.call(s&&s.instances[r],t,n,u));let f=Promise.resolve(a);e.length<3&&(f=f.then(u)),f.catch(p=>l(p))})}function or(e,t,n,s,r=i=>i()){const i=[];for(const o of e)for(const c in o.components){let l=o.components[c];if(!(t!=="beforeRouteEnter"&&!o.instances[c]))if(zc(l)){const a=(l.__vccOpts||l)[t];a&&i.push(wt(a,n,s,o,c,r))}else{let u=l();i.push(()=>u.then(a=>{if(!a)throw new Error(`Couldn't resolve component "${c}" at "${o.path}"`);const f=Ef(a)?a.default:a;o.mods[c]=a,o.components[c]=f;const m=(f.__vccOpts||f)[t];return m&&wt(m,n,s,o,c,r)()}))}}return i}function ho(e){const t=He(Bs),n=He(ii),s=Pe(()=>{const l=zt(e.to);return t.resolve(l)}),r=Pe(()=>{const{matched:l}=s.value,{length:u}=l,a=l[u-1],f=n.matched;if(!a||!f.length)return-1;const p=f.findIndex(Zt.bind(null,a));if(p>-1)return p;const m=po(l[u-2]);return u>1&&po(a)===m&&f[f.length-1].path!==m?f.findIndex(Zt.bind(null,l[u-2])):p}),i=Pe(()=>r.value>-1&&bh(n.params,s.value.params)),o=Pe(()=>r.value>-1&&r.value===n.matched.length-1&&Zc(n.params,s.value.params));function c(l={}){if(_h(l)){const u=t[zt(e.replace)?"replace":"push"](zt(e.to)).catch(Sn);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>u),u}return Promise.resolve()}return{route:s,href:Pe(()=>s.value.href),isActive:i,isExactActive:o,navigate:c}}function mh(e){return e.length===1?e[0]:e}const gh=yc({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:ho,setup(e,{slots:t}){const n=Os(ho(e)),{options:s}=He(Bs),r=Pe(()=>({[mo(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[mo(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const i=t.default&&mh(t.default(n));return e.custom?i:Hc("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},i)}}}),yh=gh;function _h(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function bh(e,t){for(const n in t){const s=t[n],r=e[n];if(typeof s=="string"){if(s!==r)return!1}else if(!Ke(r)||r.length!==s.length||s.some((i,o)=>i!==r[o]))return!1}return!0}function po(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const mo=(e,t,n)=>e??t??n,wh=yc({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=He(Tr),r=Pe(()=>e.route||s.value),i=He(fo,0),o=Pe(()=>{let u=zt(i);const{matched:a}=r.value;let f;for(;(f=a[u])&&!f.components;)u++;return u}),c=Pe(()=>r.value.matched[o.value]);Jn(fo,Pe(()=>o.value+1)),Jn(ph,c),Jn(Tr,r);const l=ac();return Gn(()=>[l.value,c.value,e.name],([u,a,f],[p,m,_])=>{a&&(a.instances[f]=u,m&&m!==a&&u&&u===p&&(a.leaveGuards.size||(a.leaveGuards=m.leaveGuards),a.updateGuards.size||(a.updateGuards=m.updateGuards))),u&&a&&(!m||!Zt(a,m)||!p)&&(a.enterCallbacks[f]||[]).forEach(E=>E(u))},{flush:"post"}),()=>{const u=r.value,a=e.name,f=c.value,p=f&&f.components[a];if(!p)return go(n.default,{Component:p,route:u});const m=f.props[a],_=m?m===!0?u.params:typeof m=="function"?m(u):m:null,S=Hc(p,G({},_,t,{onVnodeUnmounted:P=>{P.component.isUnmounted&&(f.instances[a]=null)},ref:l}));return go(n.default,{Component:S,route:u})||S}}});function go(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Eh=wh;function vh(e){const t=ch(e.routes,e),n=e.parseQuery||hh,s=e.stringifyQuery||uo,r=e.history,i=fn(),o=fn(),c=fn(),l=Ea(yt);let u=yt;Ht&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const a=rr.bind(null,w=>""+w),f=rr.bind(null,If),p=rr.bind(null,Nn);function m(w,B){let k,D;return tl(w)?(k=t.getRecordMatcher(w),D=B):D=w,t.addRoute(D,k)}function _(w){const B=t.getRecordMatcher(w);B&&t.removeRoute(B)}function E(){return t.getRoutes().map(w=>w.record)}function S(w){return!!t.getRecordMatcher(w)}function P(w,B){if(B=G({},B||l.value),typeof w=="string"){const d=ir(n,w,B.path),g=t.resolve({path:d.path},B),v=r.createHref(d.fullPath);return G(d,g,{params:p(g.params),hash:Nn(d.hash),redirectedFrom:void 0,href:v})}let k;if(w.path!=null)k=G({},w,{path:ir(n,w.path,B.path).path});else{const d=G({},w.params);for(const g in d)d[g]==null&&delete d[g];k=G({},w,{params:f(d)}),B.params=f(B.params)}const D=t.resolve(k,B),J=w.hash||"";D.params=a(p(D.params));const ie=Uf(s,G({},w,{hash:kf(J),path:D.path})),h=r.createHref(ie);return G({fullPath:ie,hash:J,query:s===uo?dh(w.query):w.query||{}},D,{redirectedFrom:void 0,href:h})}function O(w){return typeof w=="string"?ir(n,w,l.value.path):G({},w)}function L(w,B){if(u!==w)return en(8,{from:B,to:w})}function I(w){return W(w)}function $(w){return I(G(O(w),{replace:!0}))}function te(w){const B=w.matched[w.matched.length-1];if(B&&B.redirect){const{redirect:k}=B;let D=typeof k=="function"?k(w):k;return typeof D=="string"&&(D=D.includes("?")||D.includes("#")?D=O(D):{path:D},D.params={}),G({query:w.query,hash:w.hash,params:D.path!=null?{}:w.params},D)}}function W(w,B){const k=u=P(w),D=l.value,J=w.state,ie=w.force,h=w.replace===!0,d=te(k);if(d)return W(G(O(d),{state:typeof d=="object"?G({},J,d.state):J,force:ie,replace:h}),B||k);const g=k;g.redirectedFrom=B;let v;return!ie&&Ff(s,D,k)&&(v=en(16,{to:g,from:D}),Ge(D,D,!0,!1)),(v?Promise.resolve(v):ze(g,D)).catch(b=>ct(b)?ct(b,2)?b:gt(b):z(b,g,D)).then(b=>{if(b){if(ct(b,2))return W(G({replace:h},O(b.to),{state:typeof b.to=="object"?G({},J,b.to.state):J,force:ie}),B||g)}else b=At(g,D,!0,h,J);return mt(g,D,b),b})}function be(w,B){const k=L(w,B);return k?Promise.reject(k):Promise.resolve()}function Fe(w){const B=qt.values().next().value;return B&&typeof B.runWithContext=="function"?B.runWithContext(w):w()}function ze(w,B){let k;const[D,J,ie]=Rh(w,B);k=or(D.reverse(),"beforeRouteLeave",w,B);for(const d of D)d.leaveGuards.forEach(g=>{k.push(wt(g,w,B))});const h=be.bind(null,w,B);return k.push(h),Le(k).then(()=>{k=[];for(const d of i.list())k.push(wt(d,w,B));return k.push(h),Le(k)}).then(()=>{k=or(J,"beforeRouteUpdate",w,B);for(const d of J)d.updateGuards.forEach(g=>{k.push(wt(g,w,B))});return k.push(h),Le(k)}).then(()=>{k=[];for(const d of ie)if(d.beforeEnter)if(Ke(d.beforeEnter))for(const g of d.beforeEnter)k.push(wt(g,w,B));else k.push(wt(d.beforeEnter,w,B));return k.push(h),Le(k)}).then(()=>(w.matched.forEach(d=>d.enterCallbacks={}),k=or(ie,"beforeRouteEnter",w,B,Fe),k.push(h),Le(k))).then(()=>{k=[];for(const d of o.list())k.push(wt(d,w,B));return k.push(h),Le(k)}).catch(d=>ct(d,8)?d:Promise.reject(d))}function mt(w,B,k){c.list().forEach(D=>Fe(()=>D(w,B,k)))}function At(w,B,k,D,J){const ie=L(w,B);if(ie)return ie;const h=B===yt,d=Ht?history.state:{};k&&(D||h?r.replace(w.fullPath,G({scroll:h&&d&&d.scroll},J)):r.push(w.fullPath,J)),l.value=w,Ge(w,B,k,h),gt()}let Je;function on(){Je||(Je=r.listen((w,B,k)=>{if(!Mn.listening)return;const D=P(w),J=te(D);if(J){W(G(J,{replace:!0,force:!0}),D).catch(Sn);return}u=D;const ie=l.value;Ht&&Wf(to(ie.fullPath,k.delta),Is()),ze(D,ie).catch(h=>ct(h,12)?h:ct(h,2)?(W(G(O(h.to),{force:!0}),D).then(d=>{ct(d,20)&&!k.delta&&k.type===Ln.pop&&r.go(-1,!1)}).catch(Sn),Promise.reject()):(k.delta&&r.go(-k.delta,!1),z(h,D,ie))).then(h=>{h=h||At(D,ie,!1),h&&(k.delta&&!ct(h,8)?r.go(-k.delta,!1):k.type===Ln.pop&&ct(h,20)&&r.go(-1,!1)),mt(D,ie,h)}).catch(Sn)}))}let Ft=fn(),ue=fn(),Z;function z(w,B,k){gt(w);const D=ue.list();return D.length?D.forEach(J=>J(w,B,k)):console.error(w),Promise.reject(w)}function it(){return Z&&l.value!==yt?Promise.resolve():new Promise((w,B)=>{Ft.add([w,B])})}function gt(w){return Z||(Z=!w,on(),Ft.list().forEach(([B,k])=>w?k(w):B()),Ft.reset()),w}function Ge(w,B,k,D){const{scrollBehavior:J}=e;if(!Ht||!J)return Promise.resolve();const ie=!k&&zf(to(w.fullPath,0))||(D||!k)&&history.state&&history.state.scroll||null;return Yr().then(()=>J(w,B,ie)).then(h=>h&&Kf(h)).catch(h=>z(h,w,B))}const Re=w=>r.go(w);let Mt;const qt=new Set,Mn={currentRoute:l,listening:!0,addRoute:m,removeRoute:_,clearRoutes:t.clearRoutes,hasRoute:S,getRoutes:E,resolve:P,options:e,push:I,replace:$,go:Re,back:()=>Re(-1),forward:()=>Re(1),beforeEach:i.add,beforeResolve:o.add,afterEach:c.add,onError:ue.add,isReady:it,install(w){const B=this;w.component("RouterLink",yh),w.component("RouterView",Eh),w.config.globalProperties.$router=B,Object.defineProperty(w.config.globalProperties,"$route",{enumerable:!0,get:()=>zt(l)}),Ht&&!Mt&&l.value===yt&&(Mt=!0,I(r.location).catch(J=>{}));const k={};for(const J in yt)Object.defineProperty(k,J,{get:()=>l.value[J],enumerable:!0});w.provide(Bs,B),w.provide(ii,oc(k)),w.provide(Tr,l);const D=w.unmount;qt.add(w),w.unmount=function(){qt.delete(w),qt.size<1&&(u=yt,Je&&Je(),Je=null,l.value=yt,Mt=!1,Z=!1),D()}}};function Le(w){return w.reduce((B,k)=>B.then(()=>Fe(k)),Promise.resolve())}return Mn}function Rh(e,t){const n=[],s=[],r=[],i=Math.max(t.matched.length,e.matched.length);for(let o=0;o<i;o++){const c=t.matched[o];c&&(e.matched.find(u=>Zt(u,c))?s.push(c):n.push(c));const l=e.matched[o];l&&(t.matched.find(u=>Zt(u,l))||r.push(l))}return[n,s,r]}function Sh(){return He(Bs)}function Th(e){return He(ii)}const Ah=(e,t)=>{const n=e.__vccOpts||e;for(const[s,r]of t)n[s]=r;return n},xh={name:"App",setup(){const e=Th(),t=Sh(),n=Pe(()=>e.name),s=[{label:"首页",icon:"fas fa-home",route:"home"},{label:"行情",icon:"fas fa-chart-line",route:"market"},{label:"OKX",icon:"fas fa-exchange-alt",route:"okx"},{label:"币安",icon:"fas fa-coins",route:"binance"},{label:"我的",icon:"fas fa-user",route:"user"}],r=Pe(()=>!["login","register"].includes(e.name));return{currentRoute:n,navItems:s,showFooter:r,navigateTo:o=>{t.push({name:o})}}}},Oh={class:"app-container"},Ch={key:0,class:"footer"},Ph=["onClick"];function kh(e,t,n,s,r,i){const o=Wa("router-view");return gn(),Hn("div",Oh,[ve(o),s.showFooter?(gn(),Hn("footer",Ch,[(gn(!0),Hn(et,null,Ga(s.navItems,c=>(gn(),Hn("div",{key:c.route,class:An(["nav-item",{active:s.currentRoute===c.route}]),onClick:l=>s.navigateTo(c.route)},[gs("i",{class:An(c.icon)},null,2),gs("span",null,Ho(c.label),1)],10,Ph))),128))])):Cu("",!0)])}const Nh=Ah(xh,[["render",kh]]),Lh="modulepreload",Ih=function(e){return"/"+e},yo={},oe=function(t,n,s){if(!n||n.length===0)return t();const r=document.getElementsByTagName("link");return Promise.all(n.map(i=>{if(i=Ih(i),i in yo)return;yo[i]=!0;const o=i.endsWith(".css"),c=o?'[rel="stylesheet"]':"";if(!!s)for(let a=r.length-1;a>=0;a--){const f=r[a];if(f.href===i&&(!o||f.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${i}"]${c}`))return;const u=document.createElement("link");if(u.rel=o?"stylesheet":Lh,o||(u.as="script",u.crossOrigin=""),u.href=i,document.head.appendChild(u),o)return new Promise((a,f)=>{u.addEventListener("load",a),u.addEventListener("error",()=>f(new Error(`Unable to preload CSS for ${i}`)))})})).then(()=>t()).catch(i=>{const o=new Event("vite:preloadError",{cancelable:!0});if(o.payload=i,window.dispatchEvent(o),!o.defaultPrevented)throw i})},Ds=()=>{const e=localStorage.getItem("token"),t=localStorage.getItem("user");return!!(e&&t)},fe=(e,t,n)=>{Ds()?n():n({name:"login"})},Bh=(e,t,n)=>{Ds()?n({name:"home"}):n()},hn=(e,t,n)=>{if(Ds()){const s=localStorage.getItem("user"),r=s?JSON.parse(s):null;r&&r.isAdmin?n():(alert("需要管理员权限"),n({name:"home"}))}else n({name:"login"})},il=vh({history:Yf("/"),routes:[{path:"/login",name:"login",component:()=>oe(()=>import("./LoginView-b833a774.js"),["assets/LoginView-b833a774.js","assets/LoginView-cf43061d.css"]),beforeEnter:Bh},{path:"/",name:"home",component:()=>oe(()=>import("./HomeView-90d706c4.js"),["assets/HomeView-90d706c4.js","assets/HomeView-1c331922.css"]),beforeEnter:fe},{path:"/market",name:"market",component:()=>oe(()=>import("./MarketView-93112cae.js"),["assets/MarketView-93112cae.js","assets/MarketView-b3f00077.css"]),beforeEnter:fe},{path:"/okx",name:"okx",component:()=>oe(()=>import("./OkxView-dc9f0665.js"),["assets/OkxView-dc9f0665.js","assets/strategyPermissionService-52b945bf.js","assets/OkxView-f922793e.css"]),beforeEnter:fe},{path:"/binance",name:"binance",component:()=>oe(()=>import("./BinanceView-60501806.js"),["assets/BinanceView-60501806.js","assets/BinanceStrategyForm-3bd746fe.js","assets/strategyPermissionService-52b945bf.js","assets/BinanceStrategyForm-efaf3845.css","assets/BinanceView-d7bfb9d8.css"]),beforeEnter:fe},{path:"/market-exchange",name:"market-exchange",component:()=>oe(()=>import("./MarketExchangeView-a85fbcea.js"),["assets/MarketExchangeView-a85fbcea.js","assets/BinanceStrategyForm-3bd746fe.js","assets/strategyPermissionService-52b945bf.js","assets/BinanceStrategyForm-efaf3845.css","assets/MarketExchangeView-a741f442.css"]),beforeEnter:fe},{path:"/user",name:"user",component:()=>oe(()=>import("./UserView-4aeef381.js"),["assets/UserView-4aeef381.js","assets/UserView-19d85c31.css"]),beforeEnter:fe},{path:"/exchange-register",name:"exchange-register",component:()=>oe(()=>import("./ExchangeRegisterView-c2120d43.js"),["assets/ExchangeRegisterView-c2120d43.js","assets/ExchangeRegisterView-86331e1a.css"]),beforeEnter:fe},{path:"/api-intro",name:"api-intro",component:()=>oe(()=>import("./ApiIntroView-f4999a6e.js"),["assets/ApiIntroView-f4999a6e.js","assets/ApiIntroView-435b23c6.css"]),beforeEnter:fe},{path:"/strategy-intro",name:"strategy-intro",component:()=>oe(()=>import("./StrategyIntroView-b3e9ed86.js"),["assets/StrategyIntroView-b3e9ed86.js","assets/StrategyIntroView-2397efc5.css"]),beforeEnter:fe},{path:"/beginner-guide",name:"beginner-guide",component:()=>oe(()=>import("./BeginnerGuideView-9beaee02.js"),["assets/BeginnerGuideView-9beaee02.js","assets/BeginnerGuideView-87a80ea3.css"]),beforeEnter:fe},{path:"/invite-friends",name:"invite-friends",component:()=>oe(()=>import("./InviteFriendsView-c00ae684.js"),["assets/InviteFriendsView-c00ae684.js","assets/InviteFriendsView-9dfc45dc.css"]),beforeEnter:fe},{path:"/my-earnings",name:"my-earnings",component:()=>oe(()=>import("./MyEarningsView-2908ae1d.js"),["assets/MyEarningsView-2908ae1d.js","assets/MyEarningsView-c576fa2e.css"]),beforeEnter:fe},{path:"/ranking",name:"ranking",component:()=>oe(()=>import("./RankingView-144ff8f9.js"),["assets/RankingView-144ff8f9.js","assets/RankingView-896852e7.css"]),beforeEnter:fe},{path:"/ranking-management",name:"ranking-management",component:()=>oe(()=>import("./RankingManagementView-e4634cd1.js"),["assets/RankingManagementView-e4634cd1.js","assets/RankingManagementView-e6b359b8.css"]),beforeEnter:hn},{path:"/contact-service",name:"contact-service",component:()=>oe(()=>import("./ContactServiceView-fcfc26d0.js"),["assets/ContactServiceView-fcfc26d0.js","assets/ContactServiceView-6199aed9.css"]),beforeEnter:fe},{path:"/change-password",name:"change-password",component:()=>oe(()=>import("./ChangePasswordView-28d0771a.js"),["assets/ChangePasswordView-28d0771a.js","assets/ChangePasswordView-a470e02a.css"]),beforeEnter:fe},{path:"/membership",name:"membership",component:()=>oe(()=>import("./MembershipView-fb0eb827.js"),["assets/MembershipView-fb0eb827.js","assets/MembershipView-c039eb7a.css"]),beforeEnter:fe},{path:"/membership-management",name:"membership-management",component:()=>oe(()=>import("./MembershipManagementView-5cf8a3fc.js"),["assets/MembershipManagementView-5cf8a3fc.js","assets/MembershipManagementView-e523a220.css"]),beforeEnter:hn},{path:"/user-management",name:"user-management",component:()=>oe(()=>import("./UserManagementView-cd660d5f.js"),["assets/UserManagementView-cd660d5f.js","assets/UserManagementView-7252348f.css"]),beforeEnter:hn},{path:"/faq",name:"faq",component:()=>oe(()=>import("./FAQView-d264fca0.js"),["assets/FAQView-d264fca0.js","assets/FAQView-839f5b65.css"]),beforeEnter:fe},{path:"/strategy-backtest",name:"strategy-backtest",component:()=>oe(()=>import("./StrategyBacktestView-6f14954c.js"),["assets/StrategyBacktestView-6f14954c.js","assets/strategyPermissionService-52b945bf.js","assets/StrategyBacktestView-29aba6ee.css"]),beforeEnter:fe},{path:"/admin/strategy-permissions",name:"strategy-permissions",component:()=>oe(()=>import("./StrategyPermissionView-9b0249ef.js"),["assets/StrategyPermissionView-9b0249ef.js","assets/StrategyPermissionView-c5d0f52b.css"]),beforeEnter:hn},{path:"/content-management",name:"content-management",component:()=>oe(()=>import("./ContentManagementView-287a3fb9.js"),["assets/ContentManagementView-287a3fb9.js","assets/ContentManagementView-7495fcf5.css"]),beforeEnter:hn}]});il.beforeEach((e,t,n)=>{e.matched.length===0?Ds()?n({name:"home"}):n({name:"login"}):n()});function ol(e,t){return function(){return e.apply(t,arguments)}}const{toString:Dh}=Object.prototype,{getPrototypeOf:oi}=Object,{iterator:Us,toStringTag:cl}=Symbol,Fs=(e=>t=>{const n=Dh.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),We=e=>(e=e.toLowerCase(),t=>Fs(t)===e),Ms=e=>t=>typeof t===e,{isArray:nn}=Array,In=Ms("undefined");function Uh(e){return e!==null&&!In(e)&&e.constructor!==null&&!In(e.constructor)&&Te(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const ll=We("ArrayBuffer");function Fh(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&ll(e.buffer),t}const Mh=Ms("string"),Te=Ms("function"),al=Ms("number"),qs=e=>e!==null&&typeof e=="object",qh=e=>e===!0||e===!1,Zn=e=>{if(Fs(e)!=="object")return!1;const t=oi(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(cl in e)&&!(Us in e)},Vh=We("Date"),$h=We("File"),Hh=We("Blob"),jh=We("FileList"),Kh=e=>qs(e)&&Te(e.pipe),Wh=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Te(e.append)&&((t=Fs(e))==="formdata"||t==="object"&&Te(e.toString)&&e.toString()==="[object FormData]"))},zh=We("URLSearchParams"),[Jh,Gh,Xh,Yh]=["ReadableStream","Request","Response","Headers"].map(We),Qh=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Fn(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let s,r;if(typeof e!="object"&&(e=[e]),nn(e))for(s=0,r=e.length;s<r;s++)t.call(null,e[s],s,e);else{const i=n?Object.getOwnPropertyNames(e):Object.keys(e),o=i.length;let c;for(s=0;s<o;s++)c=i[s],t.call(null,e[c],c,e)}}function ul(e,t){t=t.toLowerCase();const n=Object.keys(e);let s=n.length,r;for(;s-- >0;)if(r=n[s],t===r.toLowerCase())return r;return null}const Pt=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),fl=e=>!In(e)&&e!==Pt;function Ar(){const{caseless:e}=fl(this)&&this||{},t={},n=(s,r)=>{const i=e&&ul(t,r)||r;Zn(t[i])&&Zn(s)?t[i]=Ar(t[i],s):Zn(s)?t[i]=Ar({},s):nn(s)?t[i]=s.slice():t[i]=s};for(let s=0,r=arguments.length;s<r;s++)arguments[s]&&Fn(arguments[s],n);return t}const Zh=(e,t,n,{allOwnKeys:s}={})=>(Fn(t,(r,i)=>{n&&Te(r)?e[i]=ol(r,n):e[i]=r},{allOwnKeys:s}),e),ed=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),td=(e,t,n,s)=>{e.prototype=Object.create(t.prototype,s),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},nd=(e,t,n,s)=>{let r,i,o;const c={};if(t=t||{},e==null)return t;do{for(r=Object.getOwnPropertyNames(e),i=r.length;i-- >0;)o=r[i],(!s||s(o,e,t))&&!c[o]&&(t[o]=e[o],c[o]=!0);e=n!==!1&&oi(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},sd=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const s=e.indexOf(t,n);return s!==-1&&s===n},rd=e=>{if(!e)return null;if(nn(e))return e;let t=e.length;if(!al(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},id=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&oi(Uint8Array)),od=(e,t)=>{const s=(e&&e[Us]).call(e);let r;for(;(r=s.next())&&!r.done;){const i=r.value;t.call(e,i[0],i[1])}},cd=(e,t)=>{let n;const s=[];for(;(n=e.exec(t))!==null;)s.push(n);return s},ld=We("HTMLFormElement"),ad=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,s,r){return s.toUpperCase()+r}),_o=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),ud=We("RegExp"),hl=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),s={};Fn(n,(r,i)=>{let o;(o=t(r,i,e))!==!1&&(s[i]=o||r)}),Object.defineProperties(e,s)},fd=e=>{hl(e,(t,n)=>{if(Te(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const s=e[n];if(Te(s)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},hd=(e,t)=>{const n={},s=r=>{r.forEach(i=>{n[i]=!0})};return nn(e)?s(e):s(String(e).split(t)),n},dd=()=>{},pd=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function md(e){return!!(e&&Te(e.append)&&e[cl]==="FormData"&&e[Us])}const gd=e=>{const t=new Array(10),n=(s,r)=>{if(qs(s)){if(t.indexOf(s)>=0)return;if(!("toJSON"in s)){t[r]=s;const i=nn(s)?[]:{};return Fn(s,(o,c)=>{const l=n(o,r+1);!In(l)&&(i[c]=l)}),t[r]=void 0,i}}return s};return n(e,0)},yd=We("AsyncFunction"),_d=e=>e&&(qs(e)||Te(e))&&Te(e.then)&&Te(e.catch),dl=((e,t)=>e?setImmediate:t?((n,s)=>(Pt.addEventListener("message",({source:r,data:i})=>{r===Pt&&i===n&&s.length&&s.shift()()},!1),r=>{s.push(r),Pt.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",Te(Pt.postMessage)),bd=typeof queueMicrotask<"u"?queueMicrotask.bind(Pt):typeof process<"u"&&process.nextTick||dl,wd=e=>e!=null&&Te(e[Us]),y={isArray:nn,isArrayBuffer:ll,isBuffer:Uh,isFormData:Wh,isArrayBufferView:Fh,isString:Mh,isNumber:al,isBoolean:qh,isObject:qs,isPlainObject:Zn,isReadableStream:Jh,isRequest:Gh,isResponse:Xh,isHeaders:Yh,isUndefined:In,isDate:Vh,isFile:$h,isBlob:Hh,isRegExp:ud,isFunction:Te,isStream:Kh,isURLSearchParams:zh,isTypedArray:id,isFileList:jh,forEach:Fn,merge:Ar,extend:Zh,trim:Qh,stripBOM:ed,inherits:td,toFlatObject:nd,kindOf:Fs,kindOfTest:We,endsWith:sd,toArray:rd,forEachEntry:od,matchAll:cd,isHTMLForm:ld,hasOwnProperty:_o,hasOwnProp:_o,reduceDescriptors:hl,freezeMethods:fd,toObjectSet:hd,toCamelCase:ad,noop:dd,toFiniteNumber:pd,findKey:ul,global:Pt,isContextDefined:fl,isSpecCompliantForm:md,toJSONObject:gd,isAsyncFn:yd,isThenable:_d,setImmediate:dl,asap:bd,isIterable:wd};function H(e,t,n,s,r){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),s&&(this.request=s),r&&(this.response=r,this.status=r.status?r.status:null)}y.inherits(H,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:y.toJSONObject(this.config),code:this.code,status:this.status}}});const pl=H.prototype,ml={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{ml[e]={value:e}});Object.defineProperties(H,ml);Object.defineProperty(pl,"isAxiosError",{value:!0});H.from=(e,t,n,s,r,i)=>{const o=Object.create(pl);return y.toFlatObject(e,o,function(l){return l!==Error.prototype},c=>c!=="isAxiosError"),H.call(o,e.message,t,n,s,r),o.cause=e,o.name=e.name,i&&Object.assign(o,i),o};const Ed=null;function xr(e){return y.isPlainObject(e)||y.isArray(e)}function gl(e){return y.endsWith(e,"[]")?e.slice(0,-2):e}function bo(e,t,n){return e?e.concat(t).map(function(r,i){return r=gl(r),!n&&i?"["+r+"]":r}).join(n?".":""):t}function vd(e){return y.isArray(e)&&!e.some(xr)}const Rd=y.toFlatObject(y,{},null,function(t){return/^is[A-Z]/.test(t)});function Vs(e,t,n){if(!y.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=y.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(E,S){return!y.isUndefined(S[E])});const s=n.metaTokens,r=n.visitor||a,i=n.dots,o=n.indexes,l=(n.Blob||typeof Blob<"u"&&Blob)&&y.isSpecCompliantForm(t);if(!y.isFunction(r))throw new TypeError("visitor must be a function");function u(_){if(_===null)return"";if(y.isDate(_))return _.toISOString();if(!l&&y.isBlob(_))throw new H("Blob is not supported. Use a Buffer instead.");return y.isArrayBuffer(_)||y.isTypedArray(_)?l&&typeof Blob=="function"?new Blob([_]):Buffer.from(_):_}function a(_,E,S){let P=_;if(_&&!S&&typeof _=="object"){if(y.endsWith(E,"{}"))E=s?E:E.slice(0,-2),_=JSON.stringify(_);else if(y.isArray(_)&&vd(_)||(y.isFileList(_)||y.endsWith(E,"[]"))&&(P=y.toArray(_)))return E=gl(E),P.forEach(function(L,I){!(y.isUndefined(L)||L===null)&&t.append(o===!0?bo([E],I,i):o===null?E:E+"[]",u(L))}),!1}return xr(_)?!0:(t.append(bo(S,E,i),u(_)),!1)}const f=[],p=Object.assign(Rd,{defaultVisitor:a,convertValue:u,isVisitable:xr});function m(_,E){if(!y.isUndefined(_)){if(f.indexOf(_)!==-1)throw Error("Circular reference detected in "+E.join("."));f.push(_),y.forEach(_,function(P,O){(!(y.isUndefined(P)||P===null)&&r.call(t,P,y.isString(O)?O.trim():O,E,p))===!0&&m(P,E?E.concat(O):[O])}),f.pop()}}if(!y.isObject(e))throw new TypeError("data must be an object");return m(e),t}function wo(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(s){return t[s]})}function ci(e,t){this._pairs=[],e&&Vs(e,this,t)}const yl=ci.prototype;yl.append=function(t,n){this._pairs.push([t,n])};yl.toString=function(t){const n=t?function(s){return t.call(this,s,wo)}:wo;return this._pairs.map(function(r){return n(r[0])+"="+n(r[1])},"").join("&")};function Sd(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function _l(e,t,n){if(!t)return e;const s=n&&n.encode||Sd;y.isFunction(n)&&(n={serialize:n});const r=n&&n.serialize;let i;if(r?i=r(t,n):i=y.isURLSearchParams(t)?t.toString():new ci(t,n).toString(s),i){const o=e.indexOf("#");o!==-1&&(e=e.slice(0,o)),e+=(e.indexOf("?")===-1?"?":"&")+i}return e}class Td{constructor(){this.handlers=[]}use(t,n,s){return this.handlers.push({fulfilled:t,rejected:n,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){y.forEach(this.handlers,function(s){s!==null&&t(s)})}}const Eo=Td,bl={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Ad=typeof URLSearchParams<"u"?URLSearchParams:ci,xd=typeof FormData<"u"?FormData:null,Od=typeof Blob<"u"?Blob:null,Cd={isBrowser:!0,classes:{URLSearchParams:Ad,FormData:xd,Blob:Od},protocols:["http","https","file","blob","url","data"]},li=typeof window<"u"&&typeof document<"u",Or=typeof navigator=="object"&&navigator||void 0,Pd=li&&(!Or||["ReactNative","NativeScript","NS"].indexOf(Or.product)<0),kd=(()=>typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),Nd=li&&window.location.href||"http://localhost",Ld=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:li,hasStandardBrowserEnv:Pd,hasStandardBrowserWebWorkerEnv:kd,navigator:Or,origin:Nd},Symbol.toStringTag,{value:"Module"})),ye={...Ld,...Cd};function Id(e,t){return Vs(e,new ye.classes.URLSearchParams,Object.assign({visitor:function(n,s,r,i){return ye.isNode&&y.isBuffer(n)?(this.append(s,n.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)}},t))}function Bd(e){return y.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Dd(e){const t={},n=Object.keys(e);let s;const r=n.length;let i;for(s=0;s<r;s++)i=n[s],t[i]=e[i];return t}function wl(e){function t(n,s,r,i){let o=n[i++];if(o==="__proto__")return!0;const c=Number.isFinite(+o),l=i>=n.length;return o=!o&&y.isArray(r)?r.length:o,l?(y.hasOwnProp(r,o)?r[o]=[r[o],s]:r[o]=s,!c):((!r[o]||!y.isObject(r[o]))&&(r[o]=[]),t(n,s,r[o],i)&&y.isArray(r[o])&&(r[o]=Dd(r[o])),!c)}if(y.isFormData(e)&&y.isFunction(e.entries)){const n={};return y.forEachEntry(e,(s,r)=>{t(Bd(s),r,n,0)}),n}return null}function Ud(e,t,n){if(y.isString(e))try{return(t||JSON.parse)(e),y.trim(e)}catch(s){if(s.name!=="SyntaxError")throw s}return(n||JSON.stringify)(e)}const ai={transitional:bl,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const s=n.getContentType()||"",r=s.indexOf("application/json")>-1,i=y.isObject(t);if(i&&y.isHTMLForm(t)&&(t=new FormData(t)),y.isFormData(t))return r?JSON.stringify(wl(t)):t;if(y.isArrayBuffer(t)||y.isBuffer(t)||y.isStream(t)||y.isFile(t)||y.isBlob(t)||y.isReadableStream(t))return t;if(y.isArrayBufferView(t))return t.buffer;if(y.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let c;if(i){if(s.indexOf("application/x-www-form-urlencoded")>-1)return Id(t,this.formSerializer).toString();if((c=y.isFileList(t))||s.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return Vs(c?{"files[]":t}:t,l&&new l,this.formSerializer)}}return i||r?(n.setContentType("application/json",!1),Ud(t)):t}],transformResponse:[function(t){const n=this.transitional||ai.transitional,s=n&&n.forcedJSONParsing,r=this.responseType==="json";if(y.isResponse(t)||y.isReadableStream(t))return t;if(t&&y.isString(t)&&(s&&!this.responseType||r)){const o=!(n&&n.silentJSONParsing)&&r;try{return JSON.parse(t)}catch(c){if(o)throw c.name==="SyntaxError"?H.from(c,H.ERR_BAD_RESPONSE,this,null,this.response):c}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:ye.classes.FormData,Blob:ye.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};y.forEach(["delete","get","head","post","put","patch"],e=>{ai.headers[e]={}});const ui=ai,Fd=y.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Md=e=>{const t={};let n,s,r;return e&&e.split(`
`).forEach(function(o){r=o.indexOf(":"),n=o.substring(0,r).trim().toLowerCase(),s=o.substring(r+1).trim(),!(!n||t[n]&&Fd[n])&&(n==="set-cookie"?t[n]?t[n].push(s):t[n]=[s]:t[n]=t[n]?t[n]+", "+s:s)}),t},vo=Symbol("internals");function dn(e){return e&&String(e).trim().toLowerCase()}function es(e){return e===!1||e==null?e:y.isArray(e)?e.map(es):String(e)}function qd(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=n.exec(e);)t[s[1]]=s[2];return t}const Vd=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function cr(e,t,n,s,r){if(y.isFunction(s))return s.call(this,t,n);if(r&&(t=n),!!y.isString(t)){if(y.isString(s))return t.indexOf(s)!==-1;if(y.isRegExp(s))return s.test(t)}}function $d(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,s)=>n.toUpperCase()+s)}function Hd(e,t){const n=y.toCamelCase(" "+t);["get","set","has"].forEach(s=>{Object.defineProperty(e,s+n,{value:function(r,i,o){return this[s].call(this,t,r,i,o)},configurable:!0})})}class $s{constructor(t){t&&this.set(t)}set(t,n,s){const r=this;function i(c,l,u){const a=dn(l);if(!a)throw new Error("header name must be a non-empty string");const f=y.findKey(r,a);(!f||r[f]===void 0||u===!0||u===void 0&&r[f]!==!1)&&(r[f||l]=es(c))}const o=(c,l)=>y.forEach(c,(u,a)=>i(u,a,l));if(y.isPlainObject(t)||t instanceof this.constructor)o(t,n);else if(y.isString(t)&&(t=t.trim())&&!Vd(t))o(Md(t),n);else if(y.isObject(t)&&y.isIterable(t)){let c={},l,u;for(const a of t){if(!y.isArray(a))throw TypeError("Object iterator must return a key-value pair");c[u=a[0]]=(l=c[u])?y.isArray(l)?[...l,a[1]]:[l,a[1]]:a[1]}o(c,n)}else t!=null&&i(n,t,s);return this}get(t,n){if(t=dn(t),t){const s=y.findKey(this,t);if(s){const r=this[s];if(!n)return r;if(n===!0)return qd(r);if(y.isFunction(n))return n.call(this,r,s);if(y.isRegExp(n))return n.exec(r);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=dn(t),t){const s=y.findKey(this,t);return!!(s&&this[s]!==void 0&&(!n||cr(this,this[s],s,n)))}return!1}delete(t,n){const s=this;let r=!1;function i(o){if(o=dn(o),o){const c=y.findKey(s,o);c&&(!n||cr(s,s[c],c,n))&&(delete s[c],r=!0)}}return y.isArray(t)?t.forEach(i):i(t),r}clear(t){const n=Object.keys(this);let s=n.length,r=!1;for(;s--;){const i=n[s];(!t||cr(this,this[i],i,t,!0))&&(delete this[i],r=!0)}return r}normalize(t){const n=this,s={};return y.forEach(this,(r,i)=>{const o=y.findKey(s,i);if(o){n[o]=es(r),delete n[i];return}const c=t?$d(i):String(i).trim();c!==i&&delete n[i],n[c]=es(r),s[c]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return y.forEach(this,(s,r)=>{s!=null&&s!==!1&&(n[r]=t&&y.isArray(s)?s.join(", "):s)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const s=new this(t);return n.forEach(r=>s.set(r)),s}static accessor(t){const s=(this[vo]=this[vo]={accessors:{}}).accessors,r=this.prototype;function i(o){const c=dn(o);s[c]||(Hd(r,o),s[c]=!0)}return y.isArray(t)?t.forEach(i):i(t),this}}$s.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);y.reduceDescriptors($s.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(s){this[n]=s}}});y.freezeMethods($s);const je=$s;function lr(e,t){const n=this||ui,s=t||n,r=je.from(s.headers);let i=s.data;return y.forEach(e,function(c){i=c.call(n,i,r.normalize(),t?t.status:void 0)}),r.normalize(),i}function El(e){return!!(e&&e.__CANCEL__)}function sn(e,t,n){H.call(this,e??"canceled",H.ERR_CANCELED,t,n),this.name="CanceledError"}y.inherits(sn,H,{__CANCEL__:!0});function vl(e,t,n){const s=n.config.validateStatus;!n.status||!s||s(n.status)?e(n):t(new H("Request failed with status code "+n.status,[H.ERR_BAD_REQUEST,H.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function jd(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Kd(e,t){e=e||10;const n=new Array(e),s=new Array(e);let r=0,i=0,o;return t=t!==void 0?t:1e3,function(l){const u=Date.now(),a=s[i];o||(o=u),n[r]=l,s[r]=u;let f=i,p=0;for(;f!==r;)p+=n[f++],f=f%e;if(r=(r+1)%e,r===i&&(i=(i+1)%e),u-o<t)return;const m=a&&u-a;return m?Math.round(p*1e3/m):void 0}}function Wd(e,t){let n=0,s=1e3/t,r,i;const o=(u,a=Date.now())=>{n=a,r=null,i&&(clearTimeout(i),i=null),e.apply(null,u)};return[(...u)=>{const a=Date.now(),f=a-n;f>=s?o(u,a):(r=u,i||(i=setTimeout(()=>{i=null,o(r)},s-f)))},()=>r&&o(r)]}const bs=(e,t,n=3)=>{let s=0;const r=Kd(50,250);return Wd(i=>{const o=i.loaded,c=i.lengthComputable?i.total:void 0,l=o-s,u=r(l),a=o<=c;s=o;const f={loaded:o,total:c,progress:c?o/c:void 0,bytes:l,rate:u||void 0,estimated:u&&c&&a?(c-o)/u:void 0,event:i,lengthComputable:c!=null,[t?"download":"upload"]:!0};e(f)},n)},Ro=(e,t)=>{const n=e!=null;return[s=>t[0]({lengthComputable:n,total:e,loaded:s}),t[1]]},So=e=>(...t)=>y.asap(()=>e(...t)),zd=ye.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,ye.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(ye.origin),ye.navigator&&/(msie|trident)/i.test(ye.navigator.userAgent)):()=>!0,Jd=ye.hasStandardBrowserEnv?{write(e,t,n,s,r,i){const o=[e+"="+encodeURIComponent(t)];y.isNumber(n)&&o.push("expires="+new Date(n).toGMTString()),y.isString(s)&&o.push("path="+s),y.isString(r)&&o.push("domain="+r),i===!0&&o.push("secure"),document.cookie=o.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Gd(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Xd(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Rl(e,t,n){let s=!Gd(t);return e&&(s||n==!1)?Xd(e,t):t}const To=e=>e instanceof je?{...e}:e;function Lt(e,t){t=t||{};const n={};function s(u,a,f,p){return y.isPlainObject(u)&&y.isPlainObject(a)?y.merge.call({caseless:p},u,a):y.isPlainObject(a)?y.merge({},a):y.isArray(a)?a.slice():a}function r(u,a,f,p){if(y.isUndefined(a)){if(!y.isUndefined(u))return s(void 0,u,f,p)}else return s(u,a,f,p)}function i(u,a){if(!y.isUndefined(a))return s(void 0,a)}function o(u,a){if(y.isUndefined(a)){if(!y.isUndefined(u))return s(void 0,u)}else return s(void 0,a)}function c(u,a,f){if(f in t)return s(u,a);if(f in e)return s(void 0,u)}const l={url:i,method:i,data:i,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:c,headers:(u,a,f)=>r(To(u),To(a),f,!0)};return y.forEach(Object.keys(Object.assign({},e,t)),function(a){const f=l[a]||r,p=f(e[a],t[a],a);y.isUndefined(p)&&f!==c||(n[a]=p)}),n}const Sl=e=>{const t=Lt({},e);let{data:n,withXSRFToken:s,xsrfHeaderName:r,xsrfCookieName:i,headers:o,auth:c}=t;t.headers=o=je.from(o),t.url=_l(Rl(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&o.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):"")));let l;if(y.isFormData(n)){if(ye.hasStandardBrowserEnv||ye.hasStandardBrowserWebWorkerEnv)o.setContentType(void 0);else if((l=o.getContentType())!==!1){const[u,...a]=l?l.split(";").map(f=>f.trim()).filter(Boolean):[];o.setContentType([u||"multipart/form-data",...a].join("; "))}}if(ye.hasStandardBrowserEnv&&(s&&y.isFunction(s)&&(s=s(t)),s||s!==!1&&zd(t.url))){const u=r&&i&&Jd.read(i);u&&o.set(r,u)}return t},Yd=typeof XMLHttpRequest<"u",Qd=Yd&&function(e){return new Promise(function(n,s){const r=Sl(e);let i=r.data;const o=je.from(r.headers).normalize();let{responseType:c,onUploadProgress:l,onDownloadProgress:u}=r,a,f,p,m,_;function E(){m&&m(),_&&_(),r.cancelToken&&r.cancelToken.unsubscribe(a),r.signal&&r.signal.removeEventListener("abort",a)}let S=new XMLHttpRequest;S.open(r.method.toUpperCase(),r.url,!0),S.timeout=r.timeout;function P(){if(!S)return;const L=je.from("getAllResponseHeaders"in S&&S.getAllResponseHeaders()),$={data:!c||c==="text"||c==="json"?S.responseText:S.response,status:S.status,statusText:S.statusText,headers:L,config:e,request:S};vl(function(W){n(W),E()},function(W){s(W),E()},$),S=null}"onloadend"in S?S.onloadend=P:S.onreadystatechange=function(){!S||S.readyState!==4||S.status===0&&!(S.responseURL&&S.responseURL.indexOf("file:")===0)||setTimeout(P)},S.onabort=function(){S&&(s(new H("Request aborted",H.ECONNABORTED,e,S)),S=null)},S.onerror=function(){s(new H("Network Error",H.ERR_NETWORK,e,S)),S=null},S.ontimeout=function(){let I=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const $=r.transitional||bl;r.timeoutErrorMessage&&(I=r.timeoutErrorMessage),s(new H(I,$.clarifyTimeoutError?H.ETIMEDOUT:H.ECONNABORTED,e,S)),S=null},i===void 0&&o.setContentType(null),"setRequestHeader"in S&&y.forEach(o.toJSON(),function(I,$){S.setRequestHeader($,I)}),y.isUndefined(r.withCredentials)||(S.withCredentials=!!r.withCredentials),c&&c!=="json"&&(S.responseType=r.responseType),u&&([p,_]=bs(u,!0),S.addEventListener("progress",p)),l&&S.upload&&([f,m]=bs(l),S.upload.addEventListener("progress",f),S.upload.addEventListener("loadend",m)),(r.cancelToken||r.signal)&&(a=L=>{S&&(s(!L||L.type?new sn(null,e,S):L),S.abort(),S=null)},r.cancelToken&&r.cancelToken.subscribe(a),r.signal&&(r.signal.aborted?a():r.signal.addEventListener("abort",a)));const O=jd(r.url);if(O&&ye.protocols.indexOf(O)===-1){s(new H("Unsupported protocol "+O+":",H.ERR_BAD_REQUEST,e));return}S.send(i||null)})},Zd=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let s=new AbortController,r;const i=function(u){if(!r){r=!0,c();const a=u instanceof Error?u:this.reason;s.abort(a instanceof H?a:new sn(a instanceof Error?a.message:a))}};let o=t&&setTimeout(()=>{o=null,i(new H(`timeout ${t} of ms exceeded`,H.ETIMEDOUT))},t);const c=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(i):u.removeEventListener("abort",i)}),e=null)};e.forEach(u=>u.addEventListener("abort",i));const{signal:l}=s;return l.unsubscribe=()=>y.asap(c),l}},ep=Zd,tp=function*(e,t){let n=e.byteLength;if(!t||n<t){yield e;return}let s=0,r;for(;s<n;)r=s+t,yield e.slice(s,r),s=r},np=async function*(e,t){for await(const n of sp(e))yield*tp(n,t)},sp=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:s}=await t.read();if(n)break;yield s}}finally{await t.cancel()}},Ao=(e,t,n,s)=>{const r=np(e,t);let i=0,o,c=l=>{o||(o=!0,s&&s(l))};return new ReadableStream({async pull(l){try{const{done:u,value:a}=await r.next();if(u){c(),l.close();return}let f=a.byteLength;if(n){let p=i+=f;n(p)}l.enqueue(new Uint8Array(a))}catch(u){throw c(u),u}},cancel(l){return c(l),r.return()}},{highWaterMark:2})},Hs=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Tl=Hs&&typeof ReadableStream=="function",rp=Hs&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Al=(e,...t)=>{try{return!!e(...t)}catch{return!1}},ip=Tl&&Al(()=>{let e=!1;const t=new Request(ye.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),xo=64*1024,Cr=Tl&&Al(()=>y.isReadableStream(new Response("").body)),ws={stream:Cr&&(e=>e.body)};Hs&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!ws[t]&&(ws[t]=y.isFunction(e[t])?n=>n[t]():(n,s)=>{throw new H(`Response type '${t}' is not supported`,H.ERR_NOT_SUPPORT,s)})})})(new Response);const op=async e=>{if(e==null)return 0;if(y.isBlob(e))return e.size;if(y.isSpecCompliantForm(e))return(await new Request(ye.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(y.isArrayBufferView(e)||y.isArrayBuffer(e))return e.byteLength;if(y.isURLSearchParams(e)&&(e=e+""),y.isString(e))return(await rp(e)).byteLength},cp=async(e,t)=>{const n=y.toFiniteNumber(e.getContentLength());return n??op(t)},lp=Hs&&(async e=>{let{url:t,method:n,data:s,signal:r,cancelToken:i,timeout:o,onDownloadProgress:c,onUploadProgress:l,responseType:u,headers:a,withCredentials:f="same-origin",fetchOptions:p}=Sl(e);u=u?(u+"").toLowerCase():"text";let m=ep([r,i&&i.toAbortSignal()],o),_;const E=m&&m.unsubscribe&&(()=>{m.unsubscribe()});let S;try{if(l&&ip&&n!=="get"&&n!=="head"&&(S=await cp(a,s))!==0){let $=new Request(t,{method:"POST",body:s,duplex:"half"}),te;if(y.isFormData(s)&&(te=$.headers.get("content-type"))&&a.setContentType(te),$.body){const[W,be]=Ro(S,bs(So(l)));s=Ao($.body,xo,W,be)}}y.isString(f)||(f=f?"include":"omit");const P="credentials"in Request.prototype;_=new Request(t,{...p,signal:m,method:n.toUpperCase(),headers:a.normalize().toJSON(),body:s,duplex:"half",credentials:P?f:void 0});let O=await fetch(_);const L=Cr&&(u==="stream"||u==="response");if(Cr&&(c||L&&E)){const $={};["status","statusText","headers"].forEach(Fe=>{$[Fe]=O[Fe]});const te=y.toFiniteNumber(O.headers.get("content-length")),[W,be]=c&&Ro(te,bs(So(c),!0))||[];O=new Response(Ao(O.body,xo,W,()=>{be&&be(),E&&E()}),$)}u=u||"text";let I=await ws[y.findKey(ws,u)||"text"](O,e);return!L&&E&&E(),await new Promise(($,te)=>{vl($,te,{data:I,headers:je.from(O.headers),status:O.status,statusText:O.statusText,config:e,request:_})})}catch(P){throw E&&E(),P&&P.name==="TypeError"&&/Load failed|fetch/i.test(P.message)?Object.assign(new H("Network Error",H.ERR_NETWORK,e,_),{cause:P.cause||P}):H.from(P,P&&P.code,e,_)}}),Pr={http:Ed,xhr:Qd,fetch:lp};y.forEach(Pr,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Oo=e=>`- ${e}`,ap=e=>y.isFunction(e)||e===null||e===!1,xl={getAdapter:e=>{e=y.isArray(e)?e:[e];const{length:t}=e;let n,s;const r={};for(let i=0;i<t;i++){n=e[i];let o;if(s=n,!ap(n)&&(s=Pr[(o=String(n)).toLowerCase()],s===void 0))throw new H(`Unknown adapter '${o}'`);if(s)break;r[o||"#"+i]=s}if(!s){const i=Object.entries(r).map(([c,l])=>`adapter ${c} `+(l===!1?"is not supported by the environment":"is not available in the build"));let o=t?i.length>1?`since :
`+i.map(Oo).join(`
`):" "+Oo(i[0]):"as no adapter specified";throw new H("There is no suitable adapter to dispatch the request "+o,"ERR_NOT_SUPPORT")}return s},adapters:Pr};function ar(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new sn(null,e)}function Co(e){return ar(e),e.headers=je.from(e.headers),e.data=lr.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),xl.getAdapter(e.adapter||ui.adapter)(e).then(function(s){return ar(e),s.data=lr.call(e,e.transformResponse,s),s.headers=je.from(s.headers),s},function(s){return El(s)||(ar(e),s&&s.response&&(s.response.data=lr.call(e,e.transformResponse,s.response),s.response.headers=je.from(s.response.headers))),Promise.reject(s)})}const Ol="1.9.0",js={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{js[e]=function(s){return typeof s===e||"a"+(t<1?"n ":" ")+e}});const Po={};js.transitional=function(t,n,s){function r(i,o){return"[Axios v"+Ol+"] Transitional option '"+i+"'"+o+(s?". "+s:"")}return(i,o,c)=>{if(t===!1)throw new H(r(o," has been removed"+(n?" in "+n:"")),H.ERR_DEPRECATED);return n&&!Po[o]&&(Po[o]=!0,console.warn(r(o," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(i,o,c):!0}};js.spelling=function(t){return(n,s)=>(console.warn(`${s} is likely a misspelling of ${t}`),!0)};function up(e,t,n){if(typeof e!="object")throw new H("options must be an object",H.ERR_BAD_OPTION_VALUE);const s=Object.keys(e);let r=s.length;for(;r-- >0;){const i=s[r],o=t[i];if(o){const c=e[i],l=c===void 0||o(c,i,e);if(l!==!0)throw new H("option "+i+" must be "+l,H.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new H("Unknown option "+i,H.ERR_BAD_OPTION)}}const ts={assertOptions:up,validators:js},Qe=ts.validators;class Es{constructor(t){this.defaults=t||{},this.interceptors={request:new Eo,response:new Eo}}async request(t,n){try{return await this._request(t,n)}catch(s){if(s instanceof Error){let r={};Error.captureStackTrace?Error.captureStackTrace(r):r=new Error;const i=r.stack?r.stack.replace(/^.+\n/,""):"";try{s.stack?i&&!String(s.stack).endsWith(i.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+i):s.stack=i}catch{}}throw s}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=Lt(this.defaults,n);const{transitional:s,paramsSerializer:r,headers:i}=n;s!==void 0&&ts.assertOptions(s,{silentJSONParsing:Qe.transitional(Qe.boolean),forcedJSONParsing:Qe.transitional(Qe.boolean),clarifyTimeoutError:Qe.transitional(Qe.boolean)},!1),r!=null&&(y.isFunction(r)?n.paramsSerializer={serialize:r}:ts.assertOptions(r,{encode:Qe.function,serialize:Qe.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),ts.assertOptions(n,{baseUrl:Qe.spelling("baseURL"),withXsrfToken:Qe.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let o=i&&y.merge(i.common,i[n.method]);i&&y.forEach(["delete","get","head","post","put","patch","common"],_=>{delete i[_]}),n.headers=je.concat(o,i);const c=[];let l=!0;this.interceptors.request.forEach(function(E){typeof E.runWhen=="function"&&E.runWhen(n)===!1||(l=l&&E.synchronous,c.unshift(E.fulfilled,E.rejected))});const u=[];this.interceptors.response.forEach(function(E){u.push(E.fulfilled,E.rejected)});let a,f=0,p;if(!l){const _=[Co.bind(this),void 0];for(_.unshift.apply(_,c),_.push.apply(_,u),p=_.length,a=Promise.resolve(n);f<p;)a=a.then(_[f++],_[f++]);return a}p=c.length;let m=n;for(f=0;f<p;){const _=c[f++],E=c[f++];try{m=_(m)}catch(S){E.call(this,S);break}}try{a=Co.call(this,m)}catch(_){return Promise.reject(_)}for(f=0,p=u.length;f<p;)a=a.then(u[f++],u[f++]);return a}getUri(t){t=Lt(this.defaults,t);const n=Rl(t.baseURL,t.url,t.allowAbsoluteUrls);return _l(n,t.params,t.paramsSerializer)}}y.forEach(["delete","get","head","options"],function(t){Es.prototype[t]=function(n,s){return this.request(Lt(s||{},{method:t,url:n,data:(s||{}).data}))}});y.forEach(["post","put","patch"],function(t){function n(s){return function(i,o,c){return this.request(Lt(c||{},{method:t,headers:s?{"Content-Type":"multipart/form-data"}:{},url:i,data:o}))}}Es.prototype[t]=n(),Es.prototype[t+"Form"]=n(!0)});const ns=Es;class fi{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(i){n=i});const s=this;this.promise.then(r=>{if(!s._listeners)return;let i=s._listeners.length;for(;i-- >0;)s._listeners[i](r);s._listeners=null}),this.promise.then=r=>{let i;const o=new Promise(c=>{s.subscribe(c),i=c}).then(r);return o.cancel=function(){s.unsubscribe(i)},o},t(function(i,o,c){s.reason||(s.reason=new sn(i,o,c),n(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=s=>{t.abort(s)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new fi(function(r){t=r}),cancel:t}}}const fp=fi;function hp(e){return function(n){return e.apply(null,n)}}function dp(e){return y.isObject(e)&&e.isAxiosError===!0}const kr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(kr).forEach(([e,t])=>{kr[t]=e});const pp=kr;function Cl(e){const t=new ns(e),n=ol(ns.prototype.request,t);return y.extend(n,ns.prototype,t,{allOwnKeys:!0}),y.extend(n,t,null,{allOwnKeys:!0}),n.create=function(r){return Cl(Lt(e,r))},n}const ae=Cl(ui);ae.Axios=ns;ae.CanceledError=sn;ae.CancelToken=fp;ae.isCancel=El;ae.VERSION=Ol;ae.toFormData=Vs;ae.AxiosError=H;ae.Cancel=ae.CanceledError;ae.all=function(t){return Promise.all(t)};ae.spread=hp;ae.isAxiosError=dp;ae.mergeConfig=Lt;ae.AxiosHeaders=je;ae.formToJSON=e=>wl(y.isHTMLForm(e)?new FormData(e):e);ae.getAdapter=xl.getAdapter;ae.HttpStatusCode=pp;ae.default=ae;const mp=ae,rt=Object.create(null);rt.open="0";rt.close="1";rt.ping="2";rt.pong="3";rt.message="4";rt.upgrade="5";rt.noop="6";const ss=Object.create(null);Object.keys(rt).forEach(e=>{ss[rt[e]]=e});const Nr={type:"error",data:"parser error"},Pl=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",kl=typeof ArrayBuffer=="function",Nl=e=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(e):e&&e.buffer instanceof ArrayBuffer,hi=({type:e,data:t},n,s)=>Pl&&t instanceof Blob?n?s(t):ko(t,s):kl&&(t instanceof ArrayBuffer||Nl(t))?n?s(t):ko(new Blob([t]),s):s(rt[e]+(t||"")),ko=(e,t)=>{const n=new FileReader;return n.onload=function(){const s=n.result.split(",")[1];t("b"+(s||""))},n.readAsDataURL(e)};function No(e){return e instanceof Uint8Array?e:e instanceof ArrayBuffer?new Uint8Array(e):new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}let ur;function gp(e,t){if(Pl&&e.data instanceof Blob)return e.data.arrayBuffer().then(No).then(t);if(kl&&(e.data instanceof ArrayBuffer||Nl(e.data)))return t(No(e.data));hi(e,!1,n=>{ur||(ur=new TextEncoder),t(ur.encode(n))})}const Lo="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",yn=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let e=0;e<Lo.length;e++)yn[Lo.charCodeAt(e)]=e;const yp=e=>{let t=e.length*.75,n=e.length,s,r=0,i,o,c,l;e[e.length-1]==="="&&(t--,e[e.length-2]==="="&&t--);const u=new ArrayBuffer(t),a=new Uint8Array(u);for(s=0;s<n;s+=4)i=yn[e.charCodeAt(s)],o=yn[e.charCodeAt(s+1)],c=yn[e.charCodeAt(s+2)],l=yn[e.charCodeAt(s+3)],a[r++]=i<<2|o>>4,a[r++]=(o&15)<<4|c>>2,a[r++]=(c&3)<<6|l&63;return u},_p=typeof ArrayBuffer=="function",di=(e,t)=>{if(typeof e!="string")return{type:"message",data:Ll(e,t)};const n=e.charAt(0);return n==="b"?{type:"message",data:bp(e.substring(1),t)}:ss[n]?e.length>1?{type:ss[n],data:e.substring(1)}:{type:ss[n]}:Nr},bp=(e,t)=>{if(_p){const n=yp(e);return Ll(n,t)}else return{base64:!0,data:e}},Ll=(e,t)=>{switch(t){case"blob":return e instanceof Blob?e:new Blob([e]);case"arraybuffer":default:return e instanceof ArrayBuffer?e:e.buffer}},Il=String.fromCharCode(30),wp=(e,t)=>{const n=e.length,s=new Array(n);let r=0;e.forEach((i,o)=>{hi(i,!1,c=>{s[o]=c,++r===n&&t(s.join(Il))})})},Ep=(e,t)=>{const n=e.split(Il),s=[];for(let r=0;r<n.length;r++){const i=di(n[r],t);if(s.push(i),i.type==="error")break}return s};function vp(){return new TransformStream({transform(e,t){gp(e,n=>{const s=n.length;let r;if(s<126)r=new Uint8Array(1),new DataView(r.buffer).setUint8(0,s);else if(s<65536){r=new Uint8Array(3);const i=new DataView(r.buffer);i.setUint8(0,126),i.setUint16(1,s)}else{r=new Uint8Array(9);const i=new DataView(r.buffer);i.setUint8(0,127),i.setBigUint64(1,BigInt(s))}e.data&&typeof e.data!="string"&&(r[0]|=128),t.enqueue(r),t.enqueue(n)})}})}let fr;function Kn(e){return e.reduce((t,n)=>t+n.length,0)}function Wn(e,t){if(e[0].length===t)return e.shift();const n=new Uint8Array(t);let s=0;for(let r=0;r<t;r++)n[r]=e[0][s++],s===e[0].length&&(e.shift(),s=0);return e.length&&s<e[0].length&&(e[0]=e[0].slice(s)),n}function Rp(e,t){fr||(fr=new TextDecoder);const n=[];let s=0,r=-1,i=!1;return new TransformStream({transform(o,c){for(n.push(o);;){if(s===0){if(Kn(n)<1)break;const l=Wn(n,1);i=(l[0]&128)===128,r=l[0]&127,r<126?s=3:r===126?s=1:s=2}else if(s===1){if(Kn(n)<2)break;const l=Wn(n,2);r=new DataView(l.buffer,l.byteOffset,l.length).getUint16(0),s=3}else if(s===2){if(Kn(n)<8)break;const l=Wn(n,8),u=new DataView(l.buffer,l.byteOffset,l.length),a=u.getUint32(0);if(a>Math.pow(2,53-32)-1){c.enqueue(Nr);break}r=a*Math.pow(2,32)+u.getUint32(4),s=3}else{if(Kn(n)<r)break;const l=Wn(n,r);c.enqueue(di(i?l:fr.decode(l),t)),s=0}if(r===0||r>e){c.enqueue(Nr);break}}}})}const Bl=4;function ce(e){if(e)return Sp(e)}function Sp(e){for(var t in ce.prototype)e[t]=ce.prototype[t];return e}ce.prototype.on=ce.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this};ce.prototype.once=function(e,t){function n(){this.off(e,n),t.apply(this,arguments)}return n.fn=t,this.on(e,n),this};ce.prototype.off=ce.prototype.removeListener=ce.prototype.removeAllListeners=ce.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var n=this._callbacks["$"+e];if(!n)return this;if(arguments.length==1)return delete this._callbacks["$"+e],this;for(var s,r=0;r<n.length;r++)if(s=n[r],s===t||s.fn===t){n.splice(r,1);break}return n.length===0&&delete this._callbacks["$"+e],this};ce.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),n=this._callbacks["$"+e],s=1;s<arguments.length;s++)t[s-1]=arguments[s];if(n){n=n.slice(0);for(var s=0,r=n.length;s<r;++s)n[s].apply(this,t)}return this};ce.prototype.emitReserved=ce.prototype.emit;ce.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]};ce.prototype.hasListeners=function(e){return!!this.listeners(e).length};const Ks=(()=>typeof Promise=="function"&&typeof Promise.resolve=="function"?t=>Promise.resolve().then(t):(t,n)=>n(t,0))(),Ie=(()=>typeof self<"u"?self:typeof window<"u"?window:Function("return this")())(),Tp="arraybuffer";function Dl(e,...t){return t.reduce((n,s)=>(e.hasOwnProperty(s)&&(n[s]=e[s]),n),{})}const Ap=Ie.setTimeout,xp=Ie.clearTimeout;function Ws(e,t){t.useNativeTimers?(e.setTimeoutFn=Ap.bind(Ie),e.clearTimeoutFn=xp.bind(Ie)):(e.setTimeoutFn=Ie.setTimeout.bind(Ie),e.clearTimeoutFn=Ie.clearTimeout.bind(Ie))}const Op=1.33;function Cp(e){return typeof e=="string"?Pp(e):Math.ceil((e.byteLength||e.size)*Op)}function Pp(e){let t=0,n=0;for(let s=0,r=e.length;s<r;s++)t=e.charCodeAt(s),t<128?n+=1:t<2048?n+=2:t<55296||t>=57344?n+=3:(s++,n+=4);return n}function Ul(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}function kp(e){let t="";for(let n in e)e.hasOwnProperty(n)&&(t.length&&(t+="&"),t+=encodeURIComponent(n)+"="+encodeURIComponent(e[n]));return t}function Np(e){let t={},n=e.split("&");for(let s=0,r=n.length;s<r;s++){let i=n[s].split("=");t[decodeURIComponent(i[0])]=decodeURIComponent(i[1])}return t}class Lp extends Error{constructor(t,n,s){super(t),this.description=n,this.context=s,this.type="TransportError"}}class pi extends ce{constructor(t){super(),this.writable=!1,Ws(this,t),this.opts=t,this.query=t.query,this.socket=t.socket,this.supportsBinary=!t.forceBase64}onError(t,n,s){return super.emitReserved("error",new Lp(t,n,s)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(t){this.readyState==="open"&&this.write(t)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(t){const n=di(t,this.socket.binaryType);this.onPacket(n)}onPacket(t){super.emitReserved("packet",t)}onClose(t){this.readyState="closed",super.emitReserved("close",t)}pause(t){}createUri(t,n={}){return t+"://"+this._hostname()+this._port()+this.opts.path+this._query(n)}_hostname(){const t=this.opts.hostname;return t.indexOf(":")===-1?t:"["+t+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(t){const n=kp(t);return n.length?"?"+n:""}}class Ip extends pi{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(t){this.readyState="pausing";const n=()=>{this.readyState="paused",t()};if(this._polling||!this.writable){let s=0;this._polling&&(s++,this.once("pollComplete",function(){--s||n()})),this.writable||(s++,this.once("drain",function(){--s||n()}))}else n()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(t){const n=s=>{if(this.readyState==="opening"&&s.type==="open"&&this.onOpen(),s.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(s)};Ep(t,this.socket.binaryType).forEach(n),this.readyState!=="closed"&&(this._polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this._poll())}doClose(){const t=()=>{this.write([{type:"close"}])};this.readyState==="open"?t():this.once("open",t)}write(t){this.writable=!1,wp(t,n=>{this.doWrite(n,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const t=this.opts.secure?"https":"http",n=this.query||{};return this.opts.timestampRequests!==!1&&(n[this.opts.timestampParam]=Ul()),!this.supportsBinary&&!n.sid&&(n.b64=1),this.createUri(t,n)}}let Fl=!1;try{Fl=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch{}const Bp=Fl;function Dp(){}class Up extends Ip{constructor(t){if(super(t),typeof location<"u"){const n=location.protocol==="https:";let s=location.port;s||(s=n?"443":"80"),this.xd=typeof location<"u"&&t.hostname!==location.hostname||s!==t.port}}doWrite(t,n){const s=this.request({method:"POST",data:t});s.on("success",n),s.on("error",(r,i)=>{this.onError("xhr post error",r,i)})}doPoll(){const t=this.request();t.on("data",this.onData.bind(this)),t.on("error",(n,s)=>{this.onError("xhr poll error",n,s)}),this.pollXhr=t}}let Xt=class rs extends ce{constructor(t,n,s){super(),this.createRequest=t,Ws(this,s),this._opts=s,this._method=s.method||"GET",this._uri=n,this._data=s.data!==void 0?s.data:null,this._create()}_create(){var t;const n=Dl(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");n.xdomain=!!this._opts.xd;const s=this._xhr=this.createRequest(n);try{s.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){s.setDisableHeaderCheck&&s.setDisableHeaderCheck(!0);for(let r in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(r)&&s.setRequestHeader(r,this._opts.extraHeaders[r])}}catch{}if(this._method==="POST")try{s.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch{}try{s.setRequestHeader("Accept","*/*")}catch{}(t=this._opts.cookieJar)===null||t===void 0||t.addCookies(s),"withCredentials"in s&&(s.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(s.timeout=this._opts.requestTimeout),s.onreadystatechange=()=>{var r;s.readyState===3&&((r=this._opts.cookieJar)===null||r===void 0||r.parseCookies(s.getResponseHeader("set-cookie"))),s.readyState===4&&(s.status===200||s.status===1223?this._onLoad():this.setTimeoutFn(()=>{this._onError(typeof s.status=="number"?s.status:0)},0))},s.send(this._data)}catch(r){this.setTimeoutFn(()=>{this._onError(r)},0);return}typeof document<"u"&&(this._index=rs.requestsCount++,rs.requests[this._index]=this)}_onError(t){this.emitReserved("error",t,this._xhr),this._cleanup(!0)}_cleanup(t){if(!(typeof this._xhr>"u"||this._xhr===null)){if(this._xhr.onreadystatechange=Dp,t)try{this._xhr.abort()}catch{}typeof document<"u"&&delete rs.requests[this._index],this._xhr=null}}_onLoad(){const t=this._xhr.responseText;t!==null&&(this.emitReserved("data",t),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}};Xt.requestsCount=0;Xt.requests={};if(typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",Io);else if(typeof addEventListener=="function"){const e="onpagehide"in Ie?"pagehide":"unload";addEventListener(e,Io,!1)}}function Io(){for(let e in Xt.requests)Xt.requests.hasOwnProperty(e)&&Xt.requests[e].abort()}const Fp=function(){const e=Ml({xdomain:!1});return e&&e.responseType!==null}();class Mp extends Up{constructor(t){super(t);const n=t&&t.forceBase64;this.supportsBinary=Fp&&!n}request(t={}){return Object.assign(t,{xd:this.xd},this.opts),new Xt(Ml,this.uri(),t)}}function Ml(e){const t=e.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!t||Bp))return new XMLHttpRequest}catch{}if(!t)try{return new Ie[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch{}}const ql=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative";class qp extends pi{get name(){return"websocket"}doOpen(){const t=this.uri(),n=this.opts.protocols,s=ql?{}:Dl(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(s.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(t,n,s)}catch(r){return this.emitReserved("error",r)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=t=>this.onClose({description:"websocket connection closed",context:t}),this.ws.onmessage=t=>this.onData(t.data),this.ws.onerror=t=>this.onError("websocket error",t)}write(t){this.writable=!1;for(let n=0;n<t.length;n++){const s=t[n],r=n===t.length-1;hi(s,this.supportsBinary,i=>{try{this.doWrite(s,i)}catch{}r&&Ks(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const t=this.opts.secure?"wss":"ws",n=this.query||{};return this.opts.timestampRequests&&(n[this.opts.timestampParam]=Ul()),this.supportsBinary||(n.b64=1),this.createUri(t,n)}}const hr=Ie.WebSocket||Ie.MozWebSocket;class Vp extends qp{createSocket(t,n,s){return ql?new hr(t,n,s):n?new hr(t,n):new hr(t)}doWrite(t,n){this.ws.send(n)}}class $p extends pi{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(t){return this.emitReserved("error",t)}this._transport.closed.then(()=>{this.onClose()}).catch(t=>{this.onError("webtransport error",t)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(t=>{const n=Rp(Number.MAX_SAFE_INTEGER,this.socket.binaryType),s=t.readable.pipeThrough(n).getReader(),r=vp();r.readable.pipeTo(t.writable),this._writer=r.writable.getWriter();const i=()=>{s.read().then(({done:c,value:l})=>{c||(this.onPacket(l),i())}).catch(c=>{})};i();const o={type:"open"};this.query.sid&&(o.data=`{"sid":"${this.query.sid}"}`),this._writer.write(o).then(()=>this.onOpen())})})}write(t){this.writable=!1;for(let n=0;n<t.length;n++){const s=t[n],r=n===t.length-1;this._writer.write(s).then(()=>{r&&Ks(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var t;(t=this._transport)===null||t===void 0||t.close()}}const Hp={websocket:Vp,webtransport:$p,polling:Mp},jp=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,Kp=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function Lr(e){if(e.length>8e3)throw"URI too long";const t=e,n=e.indexOf("["),s=e.indexOf("]");n!=-1&&s!=-1&&(e=e.substring(0,n)+e.substring(n,s).replace(/:/g,";")+e.substring(s,e.length));let r=jp.exec(e||""),i={},o=14;for(;o--;)i[Kp[o]]=r[o]||"";return n!=-1&&s!=-1&&(i.source=t,i.host=i.host.substring(1,i.host.length-1).replace(/;/g,":"),i.authority=i.authority.replace("[","").replace("]","").replace(/;/g,":"),i.ipv6uri=!0),i.pathNames=Wp(i,i.path),i.queryKey=zp(i,i.query),i}function Wp(e,t){const n=/\/{2,9}/g,s=t.replace(n,"/").split("/");return(t.slice(0,1)=="/"||t.length===0)&&s.splice(0,1),t.slice(-1)=="/"&&s.splice(s.length-1,1),s}function zp(e,t){const n={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(s,r,i){r&&(n[r]=i)}),n}const Ir=typeof addEventListener=="function"&&typeof removeEventListener=="function",is=[];Ir&&addEventListener("offline",()=>{is.forEach(e=>e())},!1);class vt extends ce{constructor(t,n){if(super(),this.binaryType=Tp,this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,t&&typeof t=="object"&&(n=t,t=null),t){const s=Lr(t);n.hostname=s.host,n.secure=s.protocol==="https"||s.protocol==="wss",n.port=s.port,s.query&&(n.query=s.query)}else n.host&&(n.hostname=Lr(n.host).host);Ws(this,n),this.secure=n.secure!=null?n.secure:typeof location<"u"&&location.protocol==="https:",n.hostname&&!n.port&&(n.port=this.secure?"443":"80"),this.hostname=n.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=n.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},n.transports.forEach(s=>{const r=s.prototype.name;this.transports.push(r),this._transportsByName[r]=s}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},n),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=Np(this.opts.query)),Ir&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},is.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(t){const n=Object.assign({},this.opts.query);n.EIO=Bl,n.transport=t,this.id&&(n.sid=this.id);const s=Object.assign({},this.opts,{query:n,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[t]);return new this._transportsByName[t](s)}_open(){if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}const t=this.opts.rememberUpgrade&&vt.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1?"websocket":this.transports[0];this.readyState="opening";const n=this.createTransport(t);n.open(),this.setTransport(n)}setTransport(t){this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",n=>this._onClose("transport close",n))}onOpen(){this.readyState="open",vt.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush()}_onPacket(t){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const n=new Error("server error");n.code=t.data,this._onError(n);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data);break}}onHandshake(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this._pingInterval=t.pingInterval,this._pingTimeout=t.pingTimeout,this._maxPayload=t.maxPayload,this.onOpen(),this.readyState!=="closed"&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const t=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+t,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},t),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const t=this._getWritablePackets();this.transport.send(t),this._prevBufferLen=t.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let n=1;for(let s=0;s<this.writeBuffer.length;s++){const r=this.writeBuffer[s].data;if(r&&(n+=Cp(r)),s>0&&n>this._maxPayload)return this.writeBuffer.slice(0,s);n+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const t=Date.now()>this._pingTimeoutTime;return t&&(this._pingTimeoutTime=0,Ks(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),t}write(t,n,s){return this._sendPacket("message",t,n,s),this}send(t,n,s){return this._sendPacket("message",t,n,s),this}_sendPacket(t,n,s,r){if(typeof n=="function"&&(r=n,n=void 0),typeof s=="function"&&(r=s,s=null),this.readyState==="closing"||this.readyState==="closed")return;s=s||{},s.compress=s.compress!==!1;const i={type:t,data:n,options:s};this.emitReserved("packetCreate",i),this.writeBuffer.push(i),r&&this.once("flush",r),this.flush()}close(){const t=()=>{this._onClose("forced close"),this.transport.close()},n=()=>{this.off("upgrade",n),this.off("upgradeError",n),t()},s=()=>{this.once("upgrade",n),this.once("upgradeError",n)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?s():t()}):this.upgrading?s():t()),this}_onError(t){if(vt.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&this.readyState==="opening")return this.transports.shift(),this._open();this.emitReserved("error",t),this._onClose("transport error",t)}_onClose(t,n){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing"){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),Ir&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const s=is.indexOf(this._offlineEventListener);s!==-1&&is.splice(s,1)}this.readyState="closed",this.id=null,this.emitReserved("close",t,n),this.writeBuffer=[],this._prevBufferLen=0}}}vt.protocol=Bl;class Jp extends vt{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),this.readyState==="open"&&this.opts.upgrade)for(let t=0;t<this._upgrades.length;t++)this._probe(this._upgrades[t])}_probe(t){let n=this.createTransport(t),s=!1;vt.priorWebsocketSuccess=!1;const r=()=>{s||(n.send([{type:"ping",data:"probe"}]),n.once("packet",f=>{if(!s)if(f.type==="pong"&&f.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",n),!n)return;vt.priorWebsocketSuccess=n.name==="websocket",this.transport.pause(()=>{s||this.readyState!=="closed"&&(a(),this.setTransport(n),n.send([{type:"upgrade"}]),this.emitReserved("upgrade",n),n=null,this.upgrading=!1,this.flush())})}else{const p=new Error("probe error");p.transport=n.name,this.emitReserved("upgradeError",p)}}))};function i(){s||(s=!0,a(),n.close(),n=null)}const o=f=>{const p=new Error("probe error: "+f);p.transport=n.name,i(),this.emitReserved("upgradeError",p)};function c(){o("transport closed")}function l(){o("socket closed")}function u(f){n&&f.name!==n.name&&i()}const a=()=>{n.removeListener("open",r),n.removeListener("error",o),n.removeListener("close",c),this.off("close",l),this.off("upgrading",u)};n.once("open",r),n.once("error",o),n.once("close",c),this.once("close",l),this.once("upgrading",u),this._upgrades.indexOf("webtransport")!==-1&&t!=="webtransport"?this.setTimeoutFn(()=>{s||n.open()},200):n.open()}onHandshake(t){this._upgrades=this._filterUpgrades(t.upgrades),super.onHandshake(t)}_filterUpgrades(t){const n=[];for(let s=0;s<t.length;s++)~this.transports.indexOf(t[s])&&n.push(t[s]);return n}}let Gp=class extends Jp{constructor(t,n={}){const s=typeof t=="object"?t:n;(!s.transports||s.transports&&typeof s.transports[0]=="string")&&(s.transports=(s.transports||["polling","websocket","webtransport"]).map(r=>Hp[r]).filter(r=>!!r)),super(t,s)}};function Xp(e,t="",n){let s=e;n=n||typeof location<"u"&&location,e==null&&(e=n.protocol+"//"+n.host),typeof e=="string"&&(e.charAt(0)==="/"&&(e.charAt(1)==="/"?e=n.protocol+e:e=n.host+e),/^(https?|wss?):\/\//.test(e)||(typeof n<"u"?e=n.protocol+"//"+e:e="https://"+e),s=Lr(e)),s.port||(/^(http|ws)$/.test(s.protocol)?s.port="80":/^(http|ws)s$/.test(s.protocol)&&(s.port="443")),s.path=s.path||"/";const i=s.host.indexOf(":")!==-1?"["+s.host+"]":s.host;return s.id=s.protocol+"://"+i+":"+s.port+t,s.href=s.protocol+"://"+i+(n&&n.port===s.port?"":":"+s.port),s}const Yp=typeof ArrayBuffer=="function",Qp=e=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer,Vl=Object.prototype.toString,Zp=typeof Blob=="function"||typeof Blob<"u"&&Vl.call(Blob)==="[object BlobConstructor]",em=typeof File=="function"||typeof File<"u"&&Vl.call(File)==="[object FileConstructor]";function mi(e){return Yp&&(e instanceof ArrayBuffer||Qp(e))||Zp&&e instanceof Blob||em&&e instanceof File}function os(e,t){if(!e||typeof e!="object")return!1;if(Array.isArray(e)){for(let n=0,s=e.length;n<s;n++)if(os(e[n]))return!0;return!1}if(mi(e))return!0;if(e.toJSON&&typeof e.toJSON=="function"&&arguments.length===1)return os(e.toJSON(),!0);for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&os(e[n]))return!0;return!1}function tm(e){const t=[],n=e.data,s=e;return s.data=Br(n,t),s.attachments=t.length,{packet:s,buffers:t}}function Br(e,t){if(!e)return e;if(mi(e)){const n={_placeholder:!0,num:t.length};return t.push(e),n}else if(Array.isArray(e)){const n=new Array(e.length);for(let s=0;s<e.length;s++)n[s]=Br(e[s],t);return n}else if(typeof e=="object"&&!(e instanceof Date)){const n={};for(const s in e)Object.prototype.hasOwnProperty.call(e,s)&&(n[s]=Br(e[s],t));return n}return e}function nm(e,t){return e.data=Dr(e.data,t),delete e.attachments,e}function Dr(e,t){if(!e)return e;if(e&&e._placeholder===!0){if(typeof e.num=="number"&&e.num>=0&&e.num<t.length)return t[e.num];throw new Error("illegal attachments")}else if(Array.isArray(e))for(let n=0;n<e.length;n++)e[n]=Dr(e[n],t);else if(typeof e=="object")for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(e[n]=Dr(e[n],t));return e}const sm=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],rm=5;var j;(function(e){e[e.CONNECT=0]="CONNECT",e[e.DISCONNECT=1]="DISCONNECT",e[e.EVENT=2]="EVENT",e[e.ACK=3]="ACK",e[e.CONNECT_ERROR=4]="CONNECT_ERROR",e[e.BINARY_EVENT=5]="BINARY_EVENT",e[e.BINARY_ACK=6]="BINARY_ACK"})(j||(j={}));class im{constructor(t){this.replacer=t}encode(t){return(t.type===j.EVENT||t.type===j.ACK)&&os(t)?this.encodeAsBinary({type:t.type===j.EVENT?j.BINARY_EVENT:j.BINARY_ACK,nsp:t.nsp,data:t.data,id:t.id}):[this.encodeAsString(t)]}encodeAsString(t){let n=""+t.type;return(t.type===j.BINARY_EVENT||t.type===j.BINARY_ACK)&&(n+=t.attachments+"-"),t.nsp&&t.nsp!=="/"&&(n+=t.nsp+","),t.id!=null&&(n+=t.id),t.data!=null&&(n+=JSON.stringify(t.data,this.replacer)),n}encodeAsBinary(t){const n=tm(t),s=this.encodeAsString(n.packet),r=n.buffers;return r.unshift(s),r}}function Bo(e){return Object.prototype.toString.call(e)==="[object Object]"}class gi extends ce{constructor(t){super(),this.reviver=t}add(t){let n;if(typeof t=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");n=this.decodeString(t);const s=n.type===j.BINARY_EVENT;s||n.type===j.BINARY_ACK?(n.type=s?j.EVENT:j.ACK,this.reconstructor=new om(n),n.attachments===0&&super.emitReserved("decoded",n)):super.emitReserved("decoded",n)}else if(mi(t)||t.base64)if(this.reconstructor)n=this.reconstructor.takeBinaryData(t),n&&(this.reconstructor=null,super.emitReserved("decoded",n));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+t)}decodeString(t){let n=0;const s={type:Number(t.charAt(0))};if(j[s.type]===void 0)throw new Error("unknown packet type "+s.type);if(s.type===j.BINARY_EVENT||s.type===j.BINARY_ACK){const i=n+1;for(;t.charAt(++n)!=="-"&&n!=t.length;);const o=t.substring(i,n);if(o!=Number(o)||t.charAt(n)!=="-")throw new Error("Illegal attachments");s.attachments=Number(o)}if(t.charAt(n+1)==="/"){const i=n+1;for(;++n&&!(t.charAt(n)===","||n===t.length););s.nsp=t.substring(i,n)}else s.nsp="/";const r=t.charAt(n+1);if(r!==""&&Number(r)==r){const i=n+1;for(;++n;){const o=t.charAt(n);if(o==null||Number(o)!=o){--n;break}if(n===t.length)break}s.id=Number(t.substring(i,n+1))}if(t.charAt(++n)){const i=this.tryParse(t.substr(n));if(gi.isPayloadValid(s.type,i))s.data=i;else throw new Error("invalid payload")}return s}tryParse(t){try{return JSON.parse(t,this.reviver)}catch{return!1}}static isPayloadValid(t,n){switch(t){case j.CONNECT:return Bo(n);case j.DISCONNECT:return n===void 0;case j.CONNECT_ERROR:return typeof n=="string"||Bo(n);case j.EVENT:case j.BINARY_EVENT:return Array.isArray(n)&&(typeof n[0]=="number"||typeof n[0]=="string"&&sm.indexOf(n[0])===-1);case j.ACK:case j.BINARY_ACK:return Array.isArray(n)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class om{constructor(t){this.packet=t,this.buffers=[],this.reconPack=t}takeBinaryData(t){if(this.buffers.push(t),this.buffers.length===this.reconPack.attachments){const n=nm(this.reconPack,this.buffers);return this.finishedReconstruction(),n}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}const cm=Object.freeze(Object.defineProperty({__proto__:null,Decoder:gi,Encoder:im,get PacketType(){return j},protocol:rm},Symbol.toStringTag,{value:"Module"}));function qe(e,t,n){return e.on(t,n),function(){e.off(t,n)}}const lm=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class $l extends ce{constructor(t,n,s){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=t,this.nsp=n,s&&s.auth&&(this.auth=s.auth),this._opts=Object.assign({},s),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const t=this.io;this.subs=[qe(t,"open",this.onopen.bind(this)),qe(t,"packet",this.onpacket.bind(this)),qe(t,"error",this.onerror.bind(this)),qe(t,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(...t){return t.unshift("message"),this.emit.apply(this,t),this}emit(t,...n){var s,r,i;if(lm.hasOwnProperty(t))throw new Error('"'+t.toString()+'" is a reserved event name');if(n.unshift(t),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(n),this;const o={type:j.EVENT,data:n};if(o.options={},o.options.compress=this.flags.compress!==!1,typeof n[n.length-1]=="function"){const a=this.ids++,f=n.pop();this._registerAckCallback(a,f),o.id=a}const c=(r=(s=this.io.engine)===null||s===void 0?void 0:s.transport)===null||r===void 0?void 0:r.writable,l=this.connected&&!(!((i=this.io.engine)===null||i===void 0)&&i._hasPingExpired());return this.flags.volatile&&!c||(l?(this.notifyOutgoingListeners(o),this.packet(o)):this.sendBuffer.push(o)),this.flags={},this}_registerAckCallback(t,n){var s;const r=(s=this.flags.timeout)!==null&&s!==void 0?s:this._opts.ackTimeout;if(r===void 0){this.acks[t]=n;return}const i=this.io.setTimeoutFn(()=>{delete this.acks[t];for(let c=0;c<this.sendBuffer.length;c++)this.sendBuffer[c].id===t&&this.sendBuffer.splice(c,1);n.call(this,new Error("operation has timed out"))},r),o=(...c)=>{this.io.clearTimeoutFn(i),n.apply(this,c)};o.withError=!0,this.acks[t]=o}emitWithAck(t,...n){return new Promise((s,r)=>{const i=(o,c)=>o?r(o):s(c);i.withError=!0,n.push(i),this.emit(t,...n)})}_addToQueue(t){let n;typeof t[t.length-1]=="function"&&(n=t.pop());const s={id:this._queueSeq++,tryCount:0,pending:!1,args:t,flags:Object.assign({fromQueue:!0},this.flags)};t.push((r,...i)=>s!==this._queue[0]?void 0:(r!==null?s.tryCount>this._opts.retries&&(this._queue.shift(),n&&n(r)):(this._queue.shift(),n&&n(null,...i)),s.pending=!1,this._drainQueue())),this._queue.push(s),this._drainQueue()}_drainQueue(t=!1){if(!this.connected||this._queue.length===0)return;const n=this._queue[0];n.pending&&!t||(n.pending=!0,n.tryCount++,this.flags=n.flags,this.emit.apply(this,n.args))}packet(t){t.nsp=this.nsp,this.io._packet(t)}onopen(){typeof this.auth=="function"?this.auth(t=>{this._sendConnectPacket(t)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(t){this.packet({type:j.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},t):t})}onerror(t){this.connected||this.emitReserved("connect_error",t)}onclose(t,n){this.connected=!1,delete this.id,this.emitReserved("disconnect",t,n),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(t=>{if(!this.sendBuffer.some(s=>String(s.id)===t)){const s=this.acks[t];delete this.acks[t],s.withError&&s.call(this,new Error("socket has been disconnected"))}})}onpacket(t){if(t.nsp===this.nsp)switch(t.type){case j.CONNECT:t.data&&t.data.sid?this.onconnect(t.data.sid,t.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case j.EVENT:case j.BINARY_EVENT:this.onevent(t);break;case j.ACK:case j.BINARY_ACK:this.onack(t);break;case j.DISCONNECT:this.ondisconnect();break;case j.CONNECT_ERROR:this.destroy();const s=new Error(t.data.message);s.data=t.data.data,this.emitReserved("connect_error",s);break}}onevent(t){const n=t.data||[];t.id!=null&&n.push(this.ack(t.id)),this.connected?this.emitEvent(n):this.receiveBuffer.push(Object.freeze(n))}emitEvent(t){if(this._anyListeners&&this._anyListeners.length){const n=this._anyListeners.slice();for(const s of n)s.apply(this,t)}super.emit.apply(this,t),this._pid&&t.length&&typeof t[t.length-1]=="string"&&(this._lastOffset=t[t.length-1])}ack(t){const n=this;let s=!1;return function(...r){s||(s=!0,n.packet({type:j.ACK,id:t,data:r}))}}onack(t){const n=this.acks[t.id];typeof n=="function"&&(delete this.acks[t.id],n.withError&&t.data.unshift(null),n.apply(this,t.data))}onconnect(t,n){this.id=t,this.recovered=n&&this._pid===n,this._pid=n,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(t=>this.emitEvent(t)),this.receiveBuffer=[],this.sendBuffer.forEach(t=>{this.notifyOutgoingListeners(t),this.packet(t)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(t=>t()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:j.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(t){return this.flags.compress=t,this}get volatile(){return this.flags.volatile=!0,this}timeout(t){return this.flags.timeout=t,this}onAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(t),this}prependAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(t),this}offAny(t){if(!this._anyListeners)return this;if(t){const n=this._anyListeners;for(let s=0;s<n.length;s++)if(t===n[s])return n.splice(s,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(t),this}prependAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(t),this}offAnyOutgoing(t){if(!this._anyOutgoingListeners)return this;if(t){const n=this._anyOutgoingListeners;for(let s=0;s<n.length;s++)if(t===n[s])return n.splice(s,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(t){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const n=this._anyOutgoingListeners.slice();for(const s of n)s.apply(this,t.data)}}}function rn(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}rn.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),n=Math.floor(t*this.jitter*e);e=Math.floor(t*10)&1?e+n:e-n}return Math.min(e,this.max)|0};rn.prototype.reset=function(){this.attempts=0};rn.prototype.setMin=function(e){this.ms=e};rn.prototype.setMax=function(e){this.max=e};rn.prototype.setJitter=function(e){this.jitter=e};class Ur extends ce{constructor(t,n){var s;super(),this.nsps={},this.subs=[],t&&typeof t=="object"&&(n=t,t=void 0),n=n||{},n.path=n.path||"/socket.io",this.opts=n,Ws(this,n),this.reconnection(n.reconnection!==!1),this.reconnectionAttempts(n.reconnectionAttempts||1/0),this.reconnectionDelay(n.reconnectionDelay||1e3),this.reconnectionDelayMax(n.reconnectionDelayMax||5e3),this.randomizationFactor((s=n.randomizationFactor)!==null&&s!==void 0?s:.5),this.backoff=new rn({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(n.timeout==null?2e4:n.timeout),this._readyState="closed",this.uri=t;const r=n.parser||cm;this.encoder=new r.Encoder,this.decoder=new r.Decoder,this._autoConnect=n.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(t){return arguments.length?(this._reconnection=!!t,t||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(t){return t===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=t,this)}reconnectionDelay(t){var n;return t===void 0?this._reconnectionDelay:(this._reconnectionDelay=t,(n=this.backoff)===null||n===void 0||n.setMin(t),this)}randomizationFactor(t){var n;return t===void 0?this._randomizationFactor:(this._randomizationFactor=t,(n=this.backoff)===null||n===void 0||n.setJitter(t),this)}reconnectionDelayMax(t){var n;return t===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=t,(n=this.backoff)===null||n===void 0||n.setMax(t),this)}timeout(t){return arguments.length?(this._timeout=t,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(t){if(~this._readyState.indexOf("open"))return this;this.engine=new Gp(this.uri,this.opts);const n=this.engine,s=this;this._readyState="opening",this.skipReconnect=!1;const r=qe(n,"open",function(){s.onopen(),t&&t()}),i=c=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",c),t?t(c):this.maybeReconnectOnOpen()},o=qe(n,"error",i);if(this._timeout!==!1){const c=this._timeout,l=this.setTimeoutFn(()=>{r(),i(new Error("timeout")),n.close()},c);this.opts.autoUnref&&l.unref(),this.subs.push(()=>{this.clearTimeoutFn(l)})}return this.subs.push(r),this.subs.push(o),this}connect(t){return this.open(t)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const t=this.engine;this.subs.push(qe(t,"ping",this.onping.bind(this)),qe(t,"data",this.ondata.bind(this)),qe(t,"error",this.onerror.bind(this)),qe(t,"close",this.onclose.bind(this)),qe(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(t){try{this.decoder.add(t)}catch(n){this.onclose("parse error",n)}}ondecoded(t){Ks(()=>{this.emitReserved("packet",t)},this.setTimeoutFn)}onerror(t){this.emitReserved("error",t)}socket(t,n){let s=this.nsps[t];return s?this._autoConnect&&!s.active&&s.connect():(s=new $l(this,t,n),this.nsps[t]=s),s}_destroy(t){const n=Object.keys(this.nsps);for(const s of n)if(this.nsps[s].active)return;this._close()}_packet(t){const n=this.encoder.encode(t);for(let s=0;s<n.length;s++)this.engine.write(n[s],t.options)}cleanup(){this.subs.forEach(t=>t()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(t,n){var s;this.cleanup(),(s=this.engine)===null||s===void 0||s.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",t,n),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const t=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const n=this.backoff.duration();this._reconnecting=!0;const s=this.setTimeoutFn(()=>{t.skipReconnect||(this.emitReserved("reconnect_attempt",t.backoff.attempts),!t.skipReconnect&&t.open(r=>{r?(t._reconnecting=!1,t.reconnect(),this.emitReserved("reconnect_error",r)):t.onreconnect()}))},n);this.opts.autoUnref&&s.unref(),this.subs.push(()=>{this.clearTimeoutFn(s)})}}onreconnect(){const t=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",t)}}const pn={};function cs(e,t){typeof e=="object"&&(t=e,e=void 0),t=t||{};const n=Xp(e,t.path||"/socket.io"),s=n.source,r=n.id,i=n.path,o=pn[r]&&i in pn[r].nsps,c=t.forceNew||t["force new connection"]||t.multiplex===!1||o;let l;return c?l=new Ur(s,t):(pn[r]||(pn[r]=new Ur(s,t)),l=pn[r]),n.query&&!t.query&&(t.query=n.queryKey),l.socket(n.path,t)}Object.assign(cs,{Manager:Ur,Socket:$l,io:cs,connect:cs});const vs=window.location.hostname,yi=vs==="localhost"||vs==="127.0.0.1";console.log("🌐 访问检测:",{hostname:vs,isLocalhost:yi,origin:window.location.origin,href:window.location.href});let It,Bt;yi?(It="http://localhost:3009/api",Bt="http://localhost:3009",console.log("🏠 本地模式 - 直连后端")):(It="/api",Bt=window.location.origin,console.log("🌐 公网模式 - 强制相对路径"),console.log("🔧 这将通过Vite代理转发到后端"));console.log("📍 最终配置:");console.log("  API_URL:",It);console.log("  WS_URL:",Bt);console.log("  当前域名:",vs);console.log("  是否本地:",yi);console.log("API URL:",It);console.log("WebSocket URL:",Bt);const zs=mp.create({baseURL:It,timeout:1e4,headers:{"Content-Type":"application/json"}});zs.interceptors.request.use(e=>{const t=localStorage.getItem("token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e));zs.interceptors.response.use(e=>e,e=>{var t;return((t=e.response)==null?void 0:t.status)===401&&(localStorage.removeItem("token"),localStorage.removeItem("user"),window.location.href="/login"),Promise.reject(e)});const am=cs(Bt,{reconnection:!0,reconnectionDelay:1e3,reconnectionDelayMax:5e3,reconnectionAttempts:1/0}),Do=[{symbol:"BTCUSDT",lastPrice:"108000",priceChangePercent:"-2.5",quoteVolume:"1000000000",marketCap:21e11,name:"Bitcoin"},{symbol:"ETHUSDT",lastPrice:"2550",priceChangePercent:"-4.3",quoteVolume:"500000000",marketCap:307e9,name:"Ethereum"},{symbol:"XRPUSDT",lastPrice:"2.34",priceChangePercent:"-4.2",quoteVolume:"200000000",marketCap:134e9,name:"XRP"},{symbol:"SOLUSDT",lastPrice:"174",priceChangePercent:"-6.4",quoteVolume:"300000000",marketCap:83e9,name:"Solana"},{symbol:"BNBUSDT",lastPrice:"720",priceChangePercent:"-3.1",quoteVolume:"150000000",marketCap:104e9,name:"BNB"},{symbol:"DOGEUSDT",lastPrice:"0.227",priceChangePercent:"-7.4",quoteVolume:"200000000",marketCap:33e9,name:"Dogecoin"},{symbol:"ADAUSDT",lastPrice:"0.89",priceChangePercent:"-5.2",quoteVolume:"100000000",marketCap:31e9,name:"Cardano"},{symbol:"TRXUSDT",lastPrice:"0.25",priceChangePercent:"1.8",quoteVolume:"80000000",marketCap:22e9,name:"TRON"},{symbol:"AVAXUSDT",lastPrice:"42",priceChangePercent:"-8.1",quoteVolume:"90000000",marketCap:17e9,name:"Avalanche"},{symbol:"LINKUSDT",lastPrice:"23",priceChangePercent:"-6.7",quoteVolume:"70000000",marketCap:14e9,name:"Chainlink"}].sort((e,t)=>t.marketCap-e.marketCap);class um{constructor(){this.priceData=[...Do],this.lastUpdated=null,this.isUpdating=!1,this.updateInterval=5e3,this.timerId=null,this.updateCallbacks=[],this.init()}init(){console.log("价格缓存服务初始化"),this.fetchPrices(),this.timerId=setInterval(()=>{this.fetchPrices()},this.updateInterval)}async fetchPrices(){if(!this.isUpdating){this.isUpdating=!0;try{console.log("获取最新价格数据...");const t=await zs.get("/prices");t.data&&Array.isArray(t.data)&&t.data.length>0?(console.log(`获取到${t.data.length}条价格数据`),this.priceData=t.data,this.lastUpdated=new Date,this.notifyUpdateCallbacks()):console.warn("获取到的价格数据为空")}catch(t){console.error("获取价格数据失败:",t),this.priceData.length===0&&(console.log("使用模拟数据"),this.priceData=[...Do],this.lastUpdated=new Date,this.notifyUpdateCallbacks())}finally{this.isUpdating=!1}}}getPrices(){return this.priceData}getLastUpdated(){return this.lastUpdated}addUpdateCallback(t){typeof t=="function"&&this.updateCallbacks.push(t)}removeUpdateCallback(t){const n=this.updateCallbacks.indexOf(t);n!==-1&&this.updateCallbacks.splice(n,1)}notifyUpdateCallbacks(){this.updateCallbacks.forEach(t=>{try{t(this.priceData)}catch(n){console.error("执行更新回调时出错:",n)}})}stop(){console.log("停止价格缓存服务"),this.timerId&&(clearInterval(this.timerId),this.timerId=null),this.updateCallbacks=[]}}const ym=new um;console.log("API URL:",It);console.log("WebSocket URL:",Bt);console.log("初始化价格缓存服务...");const Ut=mf(Nh);Ut.config.globalProperties.$api=zs;Ut.config.globalProperties.$socket=am;Ut.config.globalProperties.$apiUrl=It;Ut.config.globalProperties.$wsUrl=Bt;Ut.use(wf());Ut.use(il);Ut.mount("#app");export{lf as A,af as B,hm as C,Tu as D,dm as E,et as F,cs as G,mp as H,Ah as _,Th as a,Os as b,gn as c,Hn as d,gs as e,fm as f,Cu as g,pm as h,Ou as i,cf as j,zs as k,Pe as l,Ec as m,An as n,Fa as o,Ga as p,$r as q,ac as r,ym as s,Ho as t,Sh as u,Ji as v,mm as w,Wa as x,ve as y,Gn as z};
