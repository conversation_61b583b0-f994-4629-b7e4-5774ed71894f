import{_ as x,u as B,r as f,l as V,o as F,c as n,d as i,e as s,g as m,t as o,F as h,p as w,k as I,n as y}from"./index-a2cd3c28.js";const P={name:"MyEarningsView",setup(){const D=B(),t=f(!1),d=f(""),a=f("7d"),c=f({totalEarnings:0,todayEarnings:0,monthEarnings:0,activeStrategies:0,sources:[],history:[]}),k=f([{label:"7天",value:"7d"},{label:"30天",value:"30d"},{label:"90天",value:"90d"},{label:"全部",value:"all"}]),e=()=>{D.go(-1)},_=async()=>{var l,r,u,b;try{if(t.value=!0,d.value="",!localStorage.getItem("token")){d.value="请先登录";return}console.log("发送收益数据请求...");const g=await I.get("/earnings/summary");console.log("收益数据响应:",g.data),g.data.success&&(c.value=g.data.data)}catch(v){console.error("获取收益数据失败:",v),((l=v.response)==null?void 0:l.status)===401?d.value="认证失败，请重新登录":((r=v.response)==null?void 0:r.status)===404?d.value="收益功能暂不可用":d.value="获取收益数据失败: "+(((b=(u=v.response)==null?void 0:u.data)==null?void 0:b.error)||v.message)}finally{t.value=!1}},C=async()=>{await _()},E=l=>l===0?"0.00":Math.abs(l).toFixed(2),S=l=>new Date(l).getDate().toString().padStart(2,"0"),M=l=>{const r=new Date(l);return["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"][r.getMonth()]},p=V(()=>{if(a.value==="all")return c.value.history;const l=parseInt(a.value),r=new Date;return r.setDate(r.getDate()-l),c.value.history.filter(u=>new Date(u.date)>=r)});return F(()=>{_()}),{goBack:e,loading:t,error:d,selectedPeriod:a,earningsData:c,periods:k,refreshData:C,formatCurrency:E,formatDay:S,formatMonth:M,filteredHistory:p}}},T={class:"my-earnings-view"},U={class:"header"},H={class:"header-content"},N={class:"main-content"},z={class:"overview-section"},L={class:"overview-card"},R={key:0,class:"loading-state"},j={key:1,class:"error-state"},q={key:2,class:"overview-grid"},A={class:"overview-item total"},G={class:"overview-content"},J={class:"overview-value"},K={class:"overview-item today"},O={class:"overview-content"},Q={class:"overview-value"},W={class:"overview-item month"},X={class:"overview-content"},Y={class:"overview-value"},Z={class:"overview-item strategies"},$={class:"overview-content"},ss={class:"overview-value"},ts={class:"sources-section"},as={class:"sources-card"},es={key:0,class:"sources-list"},os={class:"source-info"},ns={class:"source-details"},is={class:"source-name"},ls={class:"source-description"},rs={key:1,class:"empty-sources"},ds={class:"history-section"},vs={class:"history-card"},cs={class:"history-filters"},us=["onClick"],fs={key:0,class:"history-list"},ys={class:"history-date"},_s={class:"date-day"},gs={class:"date-month"},ms={class:"history-details"},hs={class:"history-strategy"},ws={class:"history-pair"},Ds={key:1,class:"empty-history"};function ks(D,t,d,a,c,k){return n(),i("div",T,[s("header",U,[s("div",H,[s("button",{class:"back-button",onClick:t[0]||(t[0]=(...e)=>a.goBack&&a.goBack(...e))},t[3]||(t[3]=[s("i",{class:"fas fa-arrow-left"},null,-1)])),t[5]||(t[5]=s("h1",null,"我的收益",-1)),a.loading?m("",!0):(n(),i("button",{key:0,onClick:t[1]||(t[1]=(...e)=>a.refreshData&&a.refreshData(...e)),class:"refresh-button",title:"刷新数据"},t[4]||(t[4]=[s("i",{class:"fas fa-sync-alt"},null,-1)])))])]),s("main",N,[s("div",z,[s("div",L,[t[16]||(t[16]=s("h2",null,"💰 收益概览",-1)),a.loading?(n(),i("div",R,t[6]||(t[6]=[s("i",{class:"fas fa-spinner fa-spin"},null,-1),s("span",null,"加载中...",-1)]))):a.error?(n(),i("div",j,[t[7]||(t[7]=s("i",{class:"fas fa-exclamation-triangle"},null,-1)),s("span",null,o(a.error),1),s("button",{onClick:t[2]||(t[2]=(...e)=>a.refreshData&&a.refreshData(...e)),class:"retry-button"},"重试")])):(n(),i("div",q,[s("div",A,[t[9]||(t[9]=s("div",{class:"overview-icon"},[s("i",{class:"fas fa-coins"})],-1)),s("div",G,[s("div",J,o(a.formatCurrency(a.earningsData.totalEarnings)),1),t[8]||(t[8]=s("div",{class:"overview-label"},"总收益 (USDT)",-1))])]),s("div",K,[t[11]||(t[11]=s("div",{class:"overview-icon"},[s("i",{class:"fas fa-calendar-day"})],-1)),s("div",O,[s("div",Q,o(a.formatCurrency(a.earningsData.todayEarnings)),1),t[10]||(t[10]=s("div",{class:"overview-label"},"今日收益 (USDT)",-1))])]),s("div",W,[t[13]||(t[13]=s("div",{class:"overview-icon"},[s("i",{class:"fas fa-calendar-alt"})],-1)),s("div",X,[s("div",Y,o(a.formatCurrency(a.earningsData.monthEarnings)),1),t[12]||(t[12]=s("div",{class:"overview-label"},"本月收益 (USDT)",-1))])]),s("div",Z,[t[15]||(t[15]=s("div",{class:"overview-icon"},[s("i",{class:"fas fa-chart-line"})],-1)),s("div",$,[s("div",ss,o(a.earningsData.activeStrategies),1),t[14]||(t[14]=s("div",{class:"overview-label"},"运行策略",-1))])])]))])]),s("div",ts,[s("div",as,[t[18]||(t[18]=s("h3",null,"📊 收益来源",-1)),!a.loading&&a.earningsData.sources.length>0?(n(),i("div",es,[(n(!0),i(h,null,w(a.earningsData.sources,e=>(n(),i("div",{class:"source-item",key:e.type},[s("div",os,[s("div",{class:y(["source-icon",e.type])},[s("i",{class:y(e.icon)},null,2)],2),s("div",ns,[s("div",is,o(e.name),1),s("div",ls,o(e.description),1)])]),s("div",{class:y(["source-amount",{positive:e.amount>0,negative:e.amount<0}])},o(e.amount>0?"+":"")+o(a.formatCurrency(e.amount)),3)]))),128))])):a.loading?m("",!0):(n(),i("div",rs,t[17]||(t[17]=[s("i",{class:"fas fa-chart-pie"},null,-1),s("p",null,"暂无收益数据",-1),s("small",null,"开始交易策略后将显示收益来源",-1)])))])]),s("div",ds,[s("div",vs,[t[20]||(t[20]=s("h3",null,"📈 收益历史",-1)),s("div",cs,[(n(!0),i(h,null,w(a.periods,e=>(n(),i("button",{key:e.value,onClick:_=>a.selectedPeriod=e.value,class:y([{active:a.selectedPeriod===e.value},"filter-button"])},o(e.label),11,us))),128))]),!a.loading&&a.earningsData.history.length>0?(n(),i("div",fs,[(n(!0),i(h,null,w(a.filteredHistory,e=>(n(),i("div",{class:"history-item",key:e.id},[s("div",ys,[s("div",_s,o(a.formatDay(e.date)),1),s("div",gs,o(a.formatMonth(e.date)),1)]),s("div",ms,[s("div",hs,o(e.strategy),1),s("div",ws,o(e.pair),1)]),s("div",{class:y(["history-amount",{positive:e.amount>0,negative:e.amount<0}])},o(e.amount>0?"+":"")+o(a.formatCurrency(e.amount)),3)]))),128))])):a.loading?m("",!0):(n(),i("div",Ds,t[19]||(t[19]=[s("i",{class:"fas fa-history"},null,-1),s("p",null,"暂无收益历史",-1),s("small",null,"交易完成后将显示收益记录",-1)])))])])])])}const Cs=x(P,[["render",ks],["__scopeId","data-v-990fd7dd"]]);export{Cs as default};
