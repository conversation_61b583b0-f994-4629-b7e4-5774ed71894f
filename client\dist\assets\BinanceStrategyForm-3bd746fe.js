import{_ as Z,r as y,b as de,c as s,d as o,e as t,i as P,g as n,f as g,v as F,n as Q,t as v,w as ue,k as z,l as h,z as W,o as ve,A,B as Y,F as I,p as M,C as B,j as D}from"./index-a2cd3c28.js";import{s as V}from"./strategyPermissionService-52b945bf.js";const ce={name:"BinanceApiModal",props:{show:{type:Boolean,default:!1},isReconfiguring:{type:Boolean,default:!1}},emits:["close","api-saved"],setup(C,{emit:e}){const u=y(""),a=y(""),S=y(!1),b=y(""),l=de({apiKey:"",secretKey:""}),d=()=>{let T=!0;return l.apiKey="",l.secretKey="",b.value="",u.value.trim()||(l.apiKey="API Key不能为空",T=!1),a.value.trim()||(l.secretKey="Secret Key不能为空",T=!1),T},x=async()=>{var T;if(d()){S.value=!0,b.value="";try{console.log("保存币安 API密钥...");let p="default";const k=localStorage.getItem("user");if(k)try{const U=JSON.parse(k);U&&U.uid&&(p=U.uid)}catch(U){console.error("解析用户信息失败:",U)}console.log("币安API密钥信息:",{apiKey:u.value?`${u.value.substring(0,4)}...${u.value.substring(u.value.length-4)}`:"",secretKeyLength:a.value?a.value.length:0,userId:p});const f=await z.post("/binance/save-keys",{apiKey:u.value,secretKey:a.value,userId:p});console.log("币安API保存响应:",f.data),f.data.success?(console.log("币安API密钥保存成功"),e("api-saved",{apiKey:u.value,secretKey:a.value}),_()):(console.log("币安API密钥保存失败:",f.data.error),b.value=f.data.error||"币安API密钥保存失败")}catch(p){console.error("币安API密钥保存错误:",p),p.response?(console.error("错误响应:",p.response.status,p.response.data),b.value=((T=p.response.data)==null?void 0:T.error)||`保存失败 (${p.response.status})`):p.request?(console.error("无响应错误:",p.request),b.value="服务器未响应，请检查网络连接"):(console.error("请求配置错误:",p.message),b.value="请求配置错误: "+p.message)}finally{S.value=!1}}},_=()=>{u.value="",a.value="",b.value="",l.apiKey="",l.secretKey="",e("close")};return{apiKey:u,secretKey:a,loading:S,apiError:b,errors:l,saveApiKeys:x,closeModal:_,isReconfiguring:C.isReconfiguring}}},ge={class:"modal-content"},Te={class:"modal-header"},pe={class:"modal-body"},ye={key:0,class:"loading-spinner"},be={key:1},Se={class:"api-instructions"},fe={key:0,class:"update-note"},me={class:"form-group"},Ue={key:0,class:"error-message"},De={class:"form-group"},Pe={key:0,class:"error-message"},_e={key:0,class:"error-message"},ke={class:"form-actions"};function Ae(C,e,u,a,S,b){return u.show?(s(),o("div",{key:0,class:"modal-overlay",onClick:e[5]||(e[5]=ue((...l)=>a.closeModal&&a.closeModal(...l),["self"]))},[t("div",ge,[t("div",Te,[e[6]||(e[6]=t("h3",null,"币安 API设置",-1)),t("button",{class:"close-button",onClick:e[0]||(e[0]=(...l)=>a.closeModal&&a.closeModal(...l))},"×")]),t("div",pe,[a.loading?(s(),o("div",ye,e[7]||(e[7]=[t("div",{class:"spinner"},null,-1),t("div",{style:{"margin-left":"10px"}},"保存中...",-1)]))):(s(),o("div",be,[t("div",Se,[e[9]||(e[9]=t("p",null,"请输入您的币安API密钥信息，用于连接到币安交易平台。",-1)),e[10]||(e[10]=t("p",null,[P("您可以在 "),t("a",{href:"https://www.binance.com/cn/my/settings/api-management",target:"_blank"},"币安官网"),P(" 创建API密钥。")],-1)),e[11]||(e[11]=t("p",{class:"important-note"},"注意：请确保您的API密钥具有现货交易权限，并根据需要设置IP地址限制。",-1)),e[12]||(e[12]=t("p",null,"您的API密钥将被安全地保存，用于后续的交易操作。",-1)),a.isReconfiguring?(s(),o("p",fe,e[8]||(e[8]=[t("i",{class:"fas fa-info-circle"},null,-1),P(" 您正在更新API密钥，新的密钥将替换当前配置。 ")]))):n("",!0)]),t("div",me,[e[13]||(e[13]=t("label",{for:"apiKey"},"API Key",-1)),g(t("input",{type:"text",id:"apiKey","onUpdate:modelValue":e[1]||(e[1]=l=>a.apiKey=l),placeholder:"输入币安 API Key",class:Q({error:a.errors.apiKey})},null,2),[[F,a.apiKey]]),a.errors.apiKey?(s(),o("div",Ue,v(a.errors.apiKey),1)):n("",!0)]),t("div",De,[e[14]||(e[14]=t("label",{for:"secretKey"},"Secret Key",-1)),g(t("input",{type:"password",id:"secretKey","onUpdate:modelValue":e[2]||(e[2]=l=>a.secretKey=l),placeholder:"输入币安 Secret Key",class:Q({error:a.errors.secretKey})},null,2),[[F,a.secretKey]]),a.errors.secretKey?(s(),o("div",Pe,v(a.errors.secretKey),1)):n("",!0)]),a.apiError?(s(),o("div",_e,v(a.apiError),1)):n("",!0),t("div",ke,[t("button",{class:"cancel-button",onClick:e[3]||(e[3]=(...l)=>a.closeModal&&a.closeModal(...l))},"取消"),t("button",{class:"submit-button",onClick:e[4]||(e[4]=(...l)=>a.saveApiKeys&&a.saveApiKeys(...l))},"保存API密钥")])]))])])])):n("",!0)}const et=Z(ce,[["render",Ae],["__scopeId","data-v-1cd267c4"]]);const Ie={name:"BinanceStrategyForm",props:{apiConnected:{type:Boolean,default:!1},userId:{type:String,default:"default"}},emits:["strategy-created"],setup(C,{emit:e}){const u=y(""),a=y("BTCUSDT"),S=y(100),b=y(1),l=y("long"),d=y(""),x=y("single"),_=y(!1),T=y(""),p=y(!1),k=y([]),f=y([]),U=y(!1),m=y({tradingPairs:[],directions:["long"]}),N=[{value:"BTCUSDT",label:"BTCUSDT (比特币)"},{value:"ETHUSDT",label:"ETHUSDT (以太坊)"},{value:"BNBUSDT",label:"BNBUSDT (币安币)"},{value:"XRPUSDT",label:"XRPUSDT (瑞波币)"},{value:"LTCUSDT",label:"LTCUSDT (莱特币)"},{value:"ADAUSDT",label:"ADAUSDT (艾达币)"},{value:"DOTUSDT",label:"DOTUSDT (波卡)"},{value:"SOLUSDT",label:"SOLUSDT (索拉纳)"}],w=[{value:"UNIUSDT",label:"UNIUSDT (Uniswap)"},{value:"LINKUSDT",label:"LINKUSDT (Chainlink)"},{value:"AVAXUSDT",label:"AVAXUSDT (雪崩)"},{value:"MATICUSDT",label:"MATICUSDT (Polygon)"},{value:"ATOMUSDT",label:"ATOMUSDT (Cosmos)"},{value:"FILUSDT",label:"FILUSDT (Filecoin)"}],O=[{value:"DOGEUSDT",label:"DOGEUSDT (狗狗币)"},{value:"VETUSDT",label:"VETUSDT (唯链)"},{value:"TRXUSDT",label:"TRXUSDT (波场)"},{value:"ETCUSDT",label:"ETCUSDT (以太经典)"},{value:"XLMUSDT",label:"XLMUSDT (恒星币)"},{value:"THETAUSDT",label:"THETAUSDT (Theta)"}],E=[{value:"BTCUSDT",label:"BTCUSDT (比特币永续)"},{value:"ETHUSDT",label:"ETHUSDT (以太坊永续)"},{value:"BNBUSDT",label:"BNBUSDT (币安币永续)"},{value:"XRPUSDT",label:"XRPUSDT (瑞波币永续)"},{value:"LTCUSDT",label:"LTCUSDT (莱特币永续)"},{value:"ADAUSDT",label:"ADAUSDT (艾达币永续)"},{value:"DOTUSDT",label:"DOTUSDT (波卡永续)"},{value:"SOLUSDT",label:"SOLUSDT (索拉纳永续)"}],R=[{value:"UNIUSDT",label:"UNIUSDT (Uniswap永续)"},{value:"LINKUSDT",label:"LINKUSDT (Chainlink永续)"},{value:"AVAXUSDT",label:"AVAXUSDT (雪崩永续)"},{value:"MATICUSDT",label:"MATICUSDT (Polygon永续)"},{value:"ATOMUSDT",label:"ATOMUSDT (Cosmos永续)"},{value:"FILUSDT",label:"FILUSDT (Filecoin永续)"}],X=h(()=>u.value==="spot"?[...N,...w,...O].map(r=>r.value).every(r=>m.value.tradingPairs.includes(r)):[...E,...R].map(r=>r.value).every(r=>m.value.tradingPairs.includes(r))),$=[{value:"binance_spot1",label:"币安现货策略1（顺势抄底）"},{value:"binance_spot2",label:"币安现货策略2（顺势涨利）"},{value:"binance_spot3",label:"币安现货策略3（低买高卖）"},{value:"binance_spot4",label:"币安现货策略4（大单追踪）"},{value:"binance_spot5",label:"币安现货策略5（多多益善）"}],ee=[{value:"binance_futures1",label:"币安方案一：趋势动量追击"},{value:"binance_futures2",label:"币安方案二：震荡区间套利"},{value:"binance_futures3",label:"币安方案三：突破追击"},{value:"binance_futures4",label:"币安方案四：高频震荡双向狙击"},{value:"binance_futures5",label:"币安方案五：超级高胜率策略"},{value:"binance_futures6",label:"币安方案六：MACD超神策略"},{value:"binance_futures7",label:"币安方案七：RSI趋势跟随"},{value:"binance_futures8",label:"币安方案八：TD战法"},{value:"binance_futures9",label:"币安方案九：平滑趋势跟随"},{value:"binance_futures10",label:"币安方案十：超长期MA策略"}],ae=h(()=>{if(!f.value.length)return[];const i=f.value.map(r=>r.strategyId);return $.filter(r=>{const c=V.mapStrategyTemplateToId(r.value);return i.includes(c)})}),te=h(()=>{if(!f.value.length)return[];const i=f.value.map(r=>r.strategyId);return ee.filter(r=>{const c=V.mapStrategyTemplateToId(r.value);return i.includes(c)})}),j=h(()=>x.value==="single"?u.value&&a.value&&S.value>0&&l.value&&d.value:u.value&&m.value.tradingPairs.length>0&&S.value>0&&m.value.directions.length>0&&d.value),H=()=>{a.value="BTCUSDT",S.value=100,b.value=1,l.value="long",d.value="",T.value="",p.value=!1,k.value=[],m.value={tradingPairs:[],directions:["long"]}},le=()=>{if(u.value==="spot"){const i=[...N,...w,...O].map(r=>r.value);X.value?m.value.tradingPairs=[]:m.value.tradingPairs=[...i]}else{const i=[...E,...R].map(r=>r.value);X.value?m.value.tradingPairs=[]:m.value.tradingPairs=[...i]}},K=i=>({binance_spot1:"币安现货策略1（顺势抄底）",binance_spot2:"币安现货策略2（顺势涨利）",binance_spot3:"币安现货策略3（低买高卖）",binance_spot4:"币安现货策略4（大单追踪）",binance_spot5:"币安现货策略5（多多益善）",binance_futures1:"币安方案一：趋势动量追击",binance_futures2:"币安方案二：震荡区间套利",binance_futures3:"币安方案三：突破追击",binance_futures4:"币安方案四：高频震荡双向狙击",binance_futures5:"币安方案五：超级高胜率策略",binance_futures6:"币安方案六：MACD超神策略",binance_futures7:"币安方案七：RSI趋势跟随",binance_futures8:"币安方案八：TD战法",binance_futures9:"币安方案九：平滑趋势跟随",binance_futures10:"币安方案十：超长期MA策略"})[i]||i,se=i=>{switch(i){case"long":return u.value==="spot"?"买入":"做多";case"short":return"做空";case"both":return"自动判断";default:return i}},G=()=>{const i=d.value;return{binance_futures1:{min:3,max:20},binance_futures2:{min:5,max:30},binance_futures3:{min:10,max:50},binance_futures4:{min:50,max:125},binance_futures5:{min:5,max:25},binance_futures6:{min:8,max:35},binance_futures7:{min:10,max:40},binance_futures8:{min:3,max:15},binance_futures9:{min:5,max:20},binance_futures10:{min:2,max:10}}[i]||{min:1,max:125}},oe=()=>{const i=G();return`建议杠杆范围：${i.min}-${i.max}倍，请根据风险承受能力选择`},ne=()=>{const i=[],{directions:r,tradingPairs:c}=m.value;for(const L of c)for(const q of r)u.value==="futures"?i.push({type:u.value,symbol:L,amount:S.value,leverage:b.value,direction:q,strategyTemplate:d.value,strategyName:K(d.value)}):q!=="both"&&i.push({type:u.value,symbol:L,amount:S.value,leverage:1,direction:q,strategyTemplate:d.value,strategyName:K(d.value)});k.value=i,p.value=!0},J=async()=>{try{U.value=!0,f.value=await V.getUserStrategies(),console.log("币安用户可用策略:",f.value)}catch(i){console.error("加载用户策略权限失败:",i),f.value=[]}finally{U.value=!1}};W(u,()=>{d.value=""});const ie=async()=>{var i,r;if(j.value){try{await V.validateStrategyPermission(d.value)}catch(c){T.value=c.message;return}_.value=!0,T.value="";try{const c=await z.post("/binance/strategy",{userId:C.userId,type:u.value,symbol:a.value,amount:S.value,leverage:u.value==="futures"?b.value:1,direction:l.value,strategyTemplate:d.value,strategyName:K(d.value)});c.data.success?(e("strategy-created",c.data.strategy),H(),T.value="币安策略创建成功！"):T.value=c.data.error||"创建币安策略失败"}catch(c){console.error("创建币安策略失败:",c),T.value=((r=(i=c.response)==null?void 0:i.data)==null?void 0:r.error)||"创建币安策略失败"}finally{_.value=!1}}},re=async()=>{var i,r;if(k.value.length!==0){_.value=!0,T.value="";try{const c=await z.post("/binance/batch-strategies",{userId:C.userId,strategies:k.value});if(c.data.success){for(const L of c.data.strategies)e("strategy-created",L);H(),T.value=`成功创建 ${c.data.strategies.length} 个币安策略`}else T.value=c.data.error||"批量创建币安策略失败"}catch(c){console.error("批量创建币安策略失败:",c),T.value=((r=(i=c.response)==null?void 0:i.data)==null?void 0:r.error)||"批量创建币安策略失败"}finally{_.value=!1}}};return W(d,i=>{if(i&&u.value==="futures"){const r=G(),c=Math.round((r.min+r.max)/2);b.value=c}}),ve(()=>{J()}),{strategyType:u,symbol:a,amount:S,leverage:b,direction:l,strategyTemplate:d,createMode:x,loading:_,error:T,showPreview:p,batchStrategies:k,batchParams:m,binanceSpotMainPairs:N,binanceSpotDefiPairs:w,binanceSpotOtherPairs:O,binanceFuturesMainPairs:E,binanceFuturesDefiPairs:R,userStrategies:f,permissionLoading:U,availableSpotStrategies:ae,availableFuturesStrategies:te,loadUserPermissions:J,isAllTradingPairsSelected:X,isFormValid:j,resetForm:H,toggleAllTradingPairs:le,getStrategyTitle:K,getDirectionText:se,getBinanceLeverageRange:G,getBinanceLeverageHint:oe,generateBatchStrategies:ne,createStrategy:ie,createBatchStrategies:re}}},Me={class:"okx-strategy-form"},Ce={class:"strategy-settings"},Be={class:"form-section"},xe={class:"radio-group"},Ke={class:"radio-item"},Le={class:"radio-item"},he={class:"form-section"},Ve={class:"radio-group"},Fe={class:"radio-item"},Ne={class:"radio-item"},we={class:"form-section"},Oe={key:0,label:"币安现货策略"},Ee=["value"],Re={key:1,label:"币安合约策略"},Xe=["value"],He={key:2,label:"币安现货策略"},Ge={key:3,label:"币安合约策略"},qe={key:0},ze={class:"form-section"},je={key:0,label:"主流币种"},Je={key:1,label:"DeFi币种"},Qe={key:2,label:"其他币种"},We={key:3,label:"主流合约"},Ye={key:4,label:"DeFi合约"},Ze={key:1},$e={class:"form-section"},ea={class:"trading-pairs-selection"},aa={class:"trading-pairs-header"},ta={class:"trading-pairs-container"},la={key:0,class:"trading-pairs-group"},sa={class:"trading-pairs-list"},oa=["value"],na={key:1,class:"trading-pairs-group"},ia={class:"trading-pairs-list"},ra=["value"],da={key:2,class:"trading-pairs-group"},ua={class:"trading-pairs-list"},va=["value"],ca={key:3,class:"trading-pairs-group"},ga={class:"trading-pairs-list"},Ta=["value"],pa={key:4,class:"trading-pairs-group"},ya={class:"trading-pairs-list"},ba=["value"],Sa={class:"selected-pairs-count"},fa={class:"form-section"},ma={key:2,class:"form-section"},Ua={class:"slider-container"},Da=["min","max"],Pa={class:"slider-value"},_a={key:0,class:"leverage-hint"},ka={key:3,class:"form-section"},Aa={class:"direction-options"},Ia={key:0,class:"direction-option"},Ma={key:1,class:"direction-option"},Ca={key:2,class:"direction-option"},Ba={key:3,class:"direction-option"},xa={key:0,class:"auto-direction-info"},Ka={key:1,class:"auto-direction-info"},La={key:4,class:"form-section"},ha={class:"direction-options"},Va={key:0,class:"direction-option"},Fa={key:1,class:"direction-option"},Na={key:2,class:"direction-option"},wa={key:3,class:"direction-option"},Oa={key:0,class:"auto-direction-info"},Ea={key:5,class:"create-strategy-section"},Ra=["disabled"],Xa={key:0,class:"fas fa-spinner fa-spin"},Ha={key:6,class:"batch-strategy-section"},Ga=["disabled"],qa=["disabled"],za={key:0,class:"fas fa-spinner fa-spin"},ja={key:1,class:"batch-preview"},Ja={class:"preview-list"},Qa={key:0},Wa={key:7,class:"error-message"};function Ya(C,e,u,a,S,b){return s(),o("div",Me,[t("div",Ce,[e[66]||(e[66]=t("div",{class:"settings-title"},"策略设置",-1)),t("div",Be,[e[29]||(e[29]=t("div",{class:"section-label"},"策略类型",-1)),t("div",xe,[t("label",Ke,[g(t("input",{type:"radio","onUpdate:modelValue":e[0]||(e[0]=l=>a.strategyType=l),value:"spot",onChange:e[1]||(e[1]=(...l)=>a.resetForm&&a.resetForm(...l))},null,544),[[A,a.strategyType]]),e[27]||(e[27]=t("span",{class:"radio-text"},"现货策略",-1))]),t("label",Le,[g(t("input",{type:"radio","onUpdate:modelValue":e[2]||(e[2]=l=>a.strategyType=l),value:"futures",onChange:e[3]||(e[3]=(...l)=>a.resetForm&&a.resetForm(...l))},null,544),[[A,a.strategyType]]),e[28]||(e[28]=t("span",{class:"radio-text"},"合约策略",-1))])])]),t("div",he,[e[32]||(e[32]=t("div",{class:"section-label"},"创建模式",-1)),t("div",Ve,[t("label",Fe,[g(t("input",{type:"radio","onUpdate:modelValue":e[4]||(e[4]=l=>a.createMode=l),value:"single"},null,512),[[A,a.createMode]]),e[30]||(e[30]=t("span",{class:"radio-text"},"单个策略",-1))]),t("label",Ne,[g(t("input",{type:"radio","onUpdate:modelValue":e[5]||(e[5]=l=>a.createMode=l),value:"batch"},null,512),[[A,a.createMode]]),e[31]||(e[31]=t("span",{class:"radio-text"},"批量测量",-1))])])]),t("div",we,[e[36]||(e[36]=t("div",{class:"section-label"},"策略模板",-1)),g(t("select",{class:"form-select","onUpdate:modelValue":e[6]||(e[6]=l=>a.strategyTemplate=l)},[e[35]||(e[35]=t("option",{value:""},"-- 选择策略模板 --",-1)),a.strategyType==="spot"&&a.availableSpotStrategies.length>0?(s(),o("optgroup",Oe,[(s(!0),o(I,null,M(a.availableSpotStrategies,l=>(s(),o("option",{key:l.value,value:l.value},v(l.label),9,Ee))),128))])):n("",!0),a.strategyType==="futures"&&a.availableFuturesStrategies.length>0?(s(),o("optgroup",Re,[(s(!0),o(I,null,M(a.availableFuturesStrategies,l=>(s(),o("option",{key:l.value,value:l.value},v(l.label),9,Xe))),128))])):n("",!0),a.strategyType==="spot"&&a.availableSpotStrategies.length===0?(s(),o("optgroup",He,e[33]||(e[33]=[t("option",{disabled:""},"您当前的会员等级无可用的现货策略",-1)]))):n("",!0),a.strategyType==="futures"&&a.availableFuturesStrategies.length===0?(s(),o("optgroup",Ge,e[34]||(e[34]=[t("option",{disabled:""},"您当前的会员等级无可用的合约策略",-1)]))):n("",!0)],512),[[Y,a.strategyTemplate]])]),a.createMode==="single"?(s(),o("div",qe,[t("div",ze,[e[42]||(e[42]=t("div",{class:"section-label"},"交易对",-1)),g(t("select",{class:"form-select","onUpdate:modelValue":e[7]||(e[7]=l=>a.symbol=l)},[a.strategyType==="spot"?(s(),o("optgroup",je,e[37]||(e[37]=[B('<option value="BTCUSDT" data-v-09fa3301>BTCUSDT (比特币)</option><option value="ETHUSDT" data-v-09fa3301>ETHUSDT (以太坊)</option><option value="BNBUSDT" data-v-09fa3301>BNBUSDT (币安币)</option><option value="XRPUSDT" data-v-09fa3301>XRPUSDT (瑞波币)</option><option value="LTCUSDT" data-v-09fa3301>LTCUSDT (莱特币)</option><option value="ADAUSDT" data-v-09fa3301>ADAUSDT (艾达币)</option><option value="DOTUSDT" data-v-09fa3301>DOTUSDT (波卡)</option><option value="SOLUSDT" data-v-09fa3301>SOLUSDT (索拉纳)</option>',8)]))):n("",!0),a.strategyType==="spot"?(s(),o("optgroup",Je,e[38]||(e[38]=[B('<option value="UNIUSDT" data-v-09fa3301>UNIUSDT (Uniswap)</option><option value="LINKUSDT" data-v-09fa3301>LINKUSDT (Chainlink)</option><option value="AVAXUSDT" data-v-09fa3301>AVAXUSDT (雪崩)</option><option value="MATICUSDT" data-v-09fa3301>MATICUSDT (Polygon)</option><option value="ATOMUSDT" data-v-09fa3301>ATOMUSDT (Cosmos)</option><option value="FILUSDT" data-v-09fa3301>FILUSDT (Filecoin)</option>',6)]))):n("",!0),a.strategyType==="spot"?(s(),o("optgroup",Qe,e[39]||(e[39]=[B('<option value="DOGEUSDT" data-v-09fa3301>DOGEUSDT (狗狗币)</option><option value="VETUSDT" data-v-09fa3301>VETUSDT (唯链)</option><option value="TRXUSDT" data-v-09fa3301>TRXUSDT (波场)</option><option value="ETCUSDT" data-v-09fa3301>ETCUSDT (以太经典)</option><option value="XLMUSDT" data-v-09fa3301>XLMUSDT (恒星币)</option><option value="THETAUSDT" data-v-09fa3301>THETAUSDT (Theta)</option>',6)]))):n("",!0),a.strategyType==="futures"?(s(),o("optgroup",We,e[40]||(e[40]=[B('<option value="BTCUSDT" data-v-09fa3301>BTCUSDT (比特币永续)</option><option value="ETHUSDT" data-v-09fa3301>ETHUSDT (以太坊永续)</option><option value="BNBUSDT" data-v-09fa3301>BNBUSDT (币安币永续)</option><option value="XRPUSDT" data-v-09fa3301>XRPUSDT (瑞波币永续)</option><option value="LTCUSDT" data-v-09fa3301>LTCUSDT (莱特币永续)</option><option value="ADAUSDT" data-v-09fa3301>ADAUSDT (艾达币永续)</option><option value="DOTUSDT" data-v-09fa3301>DOTUSDT (波卡永续)</option><option value="SOLUSDT" data-v-09fa3301>SOLUSDT (索拉纳永续)</option>',8)]))):n("",!0),a.strategyType==="futures"?(s(),o("optgroup",Ye,e[41]||(e[41]=[B('<option value="UNIUSDT" data-v-09fa3301>UNIUSDT (Uniswap永续)</option><option value="LINKUSDT" data-v-09fa3301>LINKUSDT (Chainlink永续)</option><option value="AVAXUSDT" data-v-09fa3301>AVAXUSDT (雪崩永续)</option><option value="MATICUSDT" data-v-09fa3301>MATICUSDT (Polygon永续)</option><option value="ATOMUSDT" data-v-09fa3301>ATOMUSDT (Cosmos永续)</option><option value="FILUSDT" data-v-09fa3301>FILUSDT (Filecoin永续)</option>',6)]))):n("",!0)],512),[[Y,a.symbol]])])])):a.createMode==="batch"?(s(),o("div",Ze,[t("div",$e,[e[48]||(e[48]=t("div",{class:"section-label"},"选择交易对",-1)),t("div",ea,[t("div",aa,[t("button",{class:"select-all-button",onClick:e[8]||(e[8]=(...l)=>a.toggleAllTradingPairs&&a.toggleAllTradingPairs(...l))},v(a.isAllTradingPairsSelected?"取消全选":"全选"),1)]),t("div",ta,[a.strategyType==="spot"?(s(),o("div",la,[e[43]||(e[43]=t("div",{class:"trading-pairs-group-title"},"主流币种",-1)),t("div",sa,[(s(!0),o(I,null,M(a.binanceSpotMainPairs,l=>(s(),o("label",{key:l.value,class:"trading-pair-checkbox"},[g(t("input",{type:"checkbox","onUpdate:modelValue":e[9]||(e[9]=d=>a.batchParams.tradingPairs=d),value:l.value},null,8,oa),[[D,a.batchParams.tradingPairs]]),t("span",null,v(l.label),1)]))),128))])])):n("",!0),a.strategyType==="spot"?(s(),o("div",na,[e[44]||(e[44]=t("div",{class:"trading-pairs-group-title"},"DeFi币种",-1)),t("div",ia,[(s(!0),o(I,null,M(a.binanceSpotDefiPairs,l=>(s(),o("label",{key:l.value,class:"trading-pair-checkbox"},[g(t("input",{type:"checkbox","onUpdate:modelValue":e[10]||(e[10]=d=>a.batchParams.tradingPairs=d),value:l.value},null,8,ra),[[D,a.batchParams.tradingPairs]]),t("span",null,v(l.label),1)]))),128))])])):n("",!0),a.strategyType==="spot"?(s(),o("div",da,[e[45]||(e[45]=t("div",{class:"trading-pairs-group-title"},"其他币种",-1)),t("div",ua,[(s(!0),o(I,null,M(a.binanceSpotOtherPairs,l=>(s(),o("label",{key:l.value,class:"trading-pair-checkbox"},[g(t("input",{type:"checkbox","onUpdate:modelValue":e[11]||(e[11]=d=>a.batchParams.tradingPairs=d),value:l.value},null,8,va),[[D,a.batchParams.tradingPairs]]),t("span",null,v(l.label),1)]))),128))])])):n("",!0),a.strategyType==="futures"?(s(),o("div",ca,[e[46]||(e[46]=t("div",{class:"trading-pairs-group-title"},"主流合约",-1)),t("div",ga,[(s(!0),o(I,null,M(a.binanceFuturesMainPairs,l=>(s(),o("label",{key:l.value,class:"trading-pair-checkbox"},[g(t("input",{type:"checkbox","onUpdate:modelValue":e[12]||(e[12]=d=>a.batchParams.tradingPairs=d),value:l.value},null,8,Ta),[[D,a.batchParams.tradingPairs]]),t("span",null,v(l.label),1)]))),128))])])):n("",!0),a.strategyType==="futures"?(s(),o("div",pa,[e[47]||(e[47]=t("div",{class:"trading-pairs-group-title"},"DeFi合约",-1)),t("div",ya,[(s(!0),o(I,null,M(a.binanceFuturesDefiPairs,l=>(s(),o("label",{key:l.value,class:"trading-pair-checkbox"},[g(t("input",{type:"checkbox","onUpdate:modelValue":e[13]||(e[13]=d=>a.batchParams.tradingPairs=d),value:l.value},null,8,ba),[[D,a.batchParams.tradingPairs]]),t("span",null,v(l.label),1)]))),128))])])):n("",!0)]),t("div",Sa," 已选择 "+v(a.batchParams.tradingPairs.length)+" 个交易对 ",1)])])])):n("",!0),t("div",fa,[e[49]||(e[49]=t("div",{class:"section-label"},"开仓金额 (USDT)",-1)),g(t("input",{type:"number",class:"form-input","onUpdate:modelValue":e[14]||(e[14]=l=>a.amount=l),placeholder:"100",min:"10",step:"1"},null,512),[[F,a.amount,void 0,{number:!0}]]),e[50]||(e[50]=t("div",{class:"info-text"},[t("i",{class:"fas fa-info-circle"}),P(" 建议金额范围：1-100000 USDT，请根据资金情况设置 ")],-1))]),a.strategyType==="futures"?(s(),o("div",ma,[e[52]||(e[52]=t("div",{class:"section-label"},"杠杆倍数",-1)),t("div",Ua,[g(t("input",{type:"range",class:"leverage-slider","onUpdate:modelValue":e[15]||(e[15]=l=>a.leverage=l),min:a.getBinanceLeverageRange().min,max:a.getBinanceLeverageRange().max,step:"1"},null,8,Da),[[F,a.leverage]]),t("div",Pa,v(a.leverage)+"x",1)]),a.strategyTemplate?(s(),o("div",_a,[e[51]||(e[51]=t("i",{class:"fas fa-info-circle"},null,-1)),t("span",null,v(a.getBinanceLeverageHint()),1)])):n("",!0)])):n("",!0),a.createMode==="single"?(s(),o("div",ka,[e[59]||(e[59]=t("div",{class:"section-label"},"策略方向",-1)),t("div",Aa,[a.strategyType==="spot"?(s(),o("label",Ia,[g(t("input",{type:"radio","onUpdate:modelValue":e[16]||(e[16]=l=>a.direction=l),value:"long"},null,512),[[A,a.direction]]),e[53]||(e[53]=t("span",null,"买入",-1))])):n("",!0),a.strategyType==="futures"?(s(),o("label",Ma,[g(t("input",{type:"radio","onUpdate:modelValue":e[17]||(e[17]=l=>a.direction=l),value:"long"},null,512),[[A,a.direction]]),e[54]||(e[54]=t("span",null,"做多",-1))])):n("",!0),a.strategyType==="futures"?(s(),o("label",Ca,[g(t("input",{type:"radio","onUpdate:modelValue":e[18]||(e[18]=l=>a.direction=l),value:"short"},null,512),[[A,a.direction]]),e[55]||(e[55]=t("span",null,"做空",-1))])):n("",!0),a.strategyType==="futures"?(s(),o("label",Ba,[g(t("input",{type:"radio","onUpdate:modelValue":e[19]||(e[19]=l=>a.direction=l),value:"both"},null,512),[[A,a.direction]]),e[56]||(e[56]=t("span",null,"自动判断",-1))])):n("",!0)]),a.strategyType==="futures"&&a.direction==="both"?(s(),o("div",xa,e[57]||(e[57]=[t("i",{class:"fas fa-info-circle"},null,-1),P(" 自动判断模式：系统根据策略条件自动判断做多或做空方向， 而不是同时开启多空两个方向。 ")]))):n("",!0),a.strategyType==="spot"?(s(),o("div",Ka,e[58]||(e[58]=[t("i",{class:"fas fa-info-circle"},null,-1),P(" 现货交易只能买入持有，不支持做空操作。 ")]))):n("",!0)])):a.createMode==="batch"?(s(),o("div",La,[e[65]||(e[65]=t("div",{class:"section-label"},"策略方向",-1)),t("div",ha,[a.strategyType==="spot"?(s(),o("label",Va,[g(t("input",{type:"checkbox","onUpdate:modelValue":e[20]||(e[20]=l=>a.batchParams.directions=l),value:"long"},null,512),[[D,a.batchParams.directions]]),e[60]||(e[60]=t("span",null,"买入",-1))])):n("",!0),a.strategyType==="futures"?(s(),o("label",Fa,[g(t("input",{type:"checkbox","onUpdate:modelValue":e[21]||(e[21]=l=>a.batchParams.directions=l),value:"long"},null,512),[[D,a.batchParams.directions]]),e[61]||(e[61]=t("span",null,"做多",-1))])):n("",!0),a.strategyType==="futures"?(s(),o("label",Na,[g(t("input",{type:"checkbox","onUpdate:modelValue":e[22]||(e[22]=l=>a.batchParams.directions=l),value:"short"},null,512),[[D,a.batchParams.directions]]),e[62]||(e[62]=t("span",null,"做空",-1))])):n("",!0),a.strategyType==="futures"?(s(),o("label",wa,[g(t("input",{type:"checkbox","onUpdate:modelValue":e[23]||(e[23]=l=>a.batchParams.directions=l),value:"both"},null,512),[[D,a.batchParams.directions]]),e[63]||(e[63]=t("span",null,"自动判断",-1))])):n("",!0)]),a.strategyType==="spot"?(s(),o("div",Oa,e[64]||(e[64]=[t("i",{class:"fas fa-info-circle"},null,-1),P(" 现货交易只能买入持有，不支持做空操作。 ")]))):n("",!0)])):n("",!0),a.createMode==="single"?(s(),o("div",Ea,[t("button",{class:"create-strategy-btn",onClick:e[24]||(e[24]=(...l)=>a.createStrategy&&a.createStrategy(...l)),disabled:!a.isFormValid||a.loading},[a.loading?(s(),o("i",Xa)):n("",!0),P(" "+v(a.loading?"创建中...":"创建币安策略"),1)],8,Ra)])):n("",!0),a.createMode==="batch"?(s(),o("div",Ha,[t("button",{class:"batch-strategy-btn",onClick:e[25]||(e[25]=(...l)=>a.generateBatchStrategies&&a.generateBatchStrategies(...l)),disabled:!a.isFormValid}," 生成批量策略 ",8,Ga),a.showPreview?(s(),o("button",{key:0,class:"create-batch-btn",onClick:e[26]||(e[26]=(...l)=>a.createBatchStrategies&&a.createBatchStrategies(...l)),disabled:a.batchStrategies.length===0||a.loading},[a.loading?(s(),o("i",za)):n("",!0),P(" "+v(a.loading?"创建中...":`创建 ${a.batchStrategies.length} 个币安策略`),1)],8,qa)):n("",!0),a.showPreview?(s(),o("div",ja,[t("h4",null,"预览策略列表 ("+v(a.batchStrategies.length)+"个)",1),t("div",Ja,[(s(!0),o(I,null,M(a.batchStrategies,(l,d)=>(s(),o("div",{key:d,class:"preview-item"},[t("span",null,v(d+1)+". "+v(a.strategyType==="spot"?"币安现货":"币安合约")+" - "+v(a.getStrategyTitle(a.strategyTemplate)),1),t("span",null,"交易对: "+v(l.symbol),1),t("span",null,"金额: "+v(l.amount)+"USDT",1),a.strategyType==="futures"?(s(),o("span",Qa,"杠杆: "+v(l.leverage)+"x",1)):n("",!0),t("span",null,"方向: "+v(a.getDirectionText(l.direction)),1)]))),128))])])):n("",!0)])):n("",!0),a.error?(s(),o("div",Wa,v(a.error),1)):n("",!0)])])}const at=Z(Ie,[["render",Ya],["__scopeId","data-v-09fa3301"]]);export{et as B,at as a};
