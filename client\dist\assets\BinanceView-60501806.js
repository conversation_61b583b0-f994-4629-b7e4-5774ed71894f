import{_ as Z,r as m,o as le,c as i,d as r,e as t,f as N,B as Oe,C as X,n as p,i as P,t as l,g as v,F as E,p as O,k as T,l as A,m as Le,x as Y,y as j,v as Ne,E as ne,w as G,G as Ue,j as ae}from"./index-a2cd3c28.js";import{B as Me,a as Qe}from"./BinanceStrategyForm-3bd746fe.js";import"./strategyPermissionService-52b945bf.js";const He={name:"BinanceOrderHistory",props:{visible:{type:Boolean,default:!1},apiConnected:{type:Boolean,default:!1},userId:{type:String,default:"default"}},setup(b){const e=m([]),h=m(!1),s=m(""),S=m(""),I=m(!0),c=m(1),k=async(f=!1)=>{var u,L;if(!b.apiConnected){s.value="请先连接币安API";return}h.value=!0,s.value="";try{const B={symbol:S.value||void 0,limit:20};f?B.page=c.value+1:(c.value=1,e.value=[]),console.log("获取币安历史订单，参数:",B);const H=await T.get("/binance/orders",{params:B});if(H.data.success){const D=H.data.orders||[];f?(e.value=[...e.value,...D],c.value++):e.value=D,I.value=D.length===20,console.log(`获取到 ${D.length} 个币安历史订单`)}else s.value=H.data.error||"获取历史订单失败"}catch(B){console.error("获取币安历史订单失败:",B),s.value=((L=(u=B.response)==null?void 0:u.data)==null?void 0:L.error)||"获取历史订单失败"}finally{h.value=!1}},C=()=>{k(!0)},w=f=>f?new Date(f).toLocaleString():"-",o=f=>({MARKET:"市价单",LIMIT:"限价单",STOP_LOSS:"止损单",STOP_LOSS_LIMIT:"止损限价单",TAKE_PROFIT:"止盈单",TAKE_PROFIT_LIMIT:"止盈限价单"})[f]||f,R=f=>f==="BUY"?"买入":"卖出",_=f=>f==="BUY"?"side-buy":"side-sell",V=f=>({NEW:"新建",PARTIALLY_FILLED:"部分成交",FILLED:"完全成交",CANCELED:"已取消",PENDING_CANCEL:"取消中",REJECTED:"已拒绝",EXPIRED:"已过期"})[f]||f,U=f=>{switch(f){case"FILLED":return"status-filled";case"CANCELED":case"REJECTED":case"EXPIRED":return"status-canceled";case"PARTIALLY_FILLED":return"status-partial";default:return"status-pending"}},x=f=>{const u=parseFloat(f||0);return u===0?"0":u>=1e3?u.toFixed(2):u>=1?u.toFixed(4):u.toFixed(8).replace(/\.?0+$/,"")},q=f=>{const u=parseFloat(f||0);return u===0?"0":u>=1e3?u.toFixed(2):u>=1?u.toFixed(4):u.toFixed(8).replace(/\.?0+$/,"")},M=f=>{let u=0;if(f.cummulativeQuoteQty&&parseFloat(f.cummulativeQuoteQty)>0)u=parseFloat(f.cummulativeQuoteQty);else{const L=parseFloat(f.executedQty||f.origQty||0),B=parseFloat(f.price||0);u=L*B}return u===0?"0":u>=1e3?u.toFixed(2):u>=1?u.toFixed(4):u.toFixed(8).replace(/\.?0+$/,"")},Q=f=>M(f);return le(()=>{b.visible&&b.apiConnected&&k()}),{orders:e,loading:h,error:s,selectedSymbol:S,hasMore:I,fetchOrders:k,loadMore:C,formatDate:w,getOrderType:o,getSideText:R,getSideClass:_,getStatusText:V,getStatusClass:U,formatQuantity:x,formatPrice:q,formatAmount:M,calculateAmount:Q}}},Ve={key:0,class:"order-history"},qe={class:"history-header"},ze={class:"header-controls"},We=["disabled"],Ke={key:0,class:"error-message"},Ye={key:1,class:"loading"},je={key:2,class:"no-orders"},Xe={key:3,class:"orders-table"},Je={class:"table-body"},Ge={class:"table-cell"},Ze={class:"table-cell"},$e={class:"table-cell"},et={class:"table-cell"},tt={class:"table-cell"},st={class:"table-cell"},ot={class:"table-cell"},nt={key:4,class:"pagination"},at=["disabled"],lt={key:0,class:"fas fa-spinner fa-spin"};function it(b,e,h,s,S,I){return h.visible?(i(),r("div",Ve,[t("div",qe,[e[6]||(e[6]=t("h3",null,"币安历史订单",-1)),t("div",ze,[N(t("select",{"onUpdate:modelValue":e[0]||(e[0]=c=>s.selectedSymbol=c),onChange:e[1]||(e[1]=(...c)=>s.fetchOrders&&s.fetchOrders(...c)),class:"symbol-select"},e[4]||(e[4]=[X('<option value="" data-v-1b5ce221>所有交易对</option><option value="BTCUSDT" data-v-1b5ce221>BTC/USDT</option><option value="ETHUSDT" data-v-1b5ce221>ETH/USDT</option><option value="BNBUSDT" data-v-1b5ce221>BNB/USDT</option><option value="ADAUSDT" data-v-1b5ce221>ADA/USDT</option><option value="XRPUSDT" data-v-1b5ce221>XRP/USDT</option>',6)]),544),[[Oe,s.selectedSymbol]]),t("button",{class:"refresh-btn",onClick:e[2]||(e[2]=(...c)=>s.fetchOrders&&s.fetchOrders(...c)),disabled:s.loading},[t("i",{class:p(["fas fa-sync-alt",{"fa-spin":s.loading}])},null,2),e[5]||(e[5]=P(" 刷新 "))],8,We)])]),s.error?(i(),r("div",Ke,l(s.error),1)):v("",!0),s.loading?(i(),r("div",Ye,e[7]||(e[7]=[t("i",{class:"fas fa-spinner fa-spin"},null,-1),P(" 正在获取历史订单... ")]))):s.orders.length===0?(i(),r("div",je," 暂无历史订单 ")):(i(),r("div",Xe,[e[8]||(e[8]=X('<div class="table-header" data-v-1b5ce221><div class="header-cell" data-v-1b5ce221>时间</div><div class="header-cell" data-v-1b5ce221>交易对</div><div class="header-cell" data-v-1b5ce221>类型</div><div class="header-cell" data-v-1b5ce221>方向</div><div class="header-cell" data-v-1b5ce221>数量</div><div class="header-cell" data-v-1b5ce221>价格</div><div class="header-cell" data-v-1b5ce221>成交金额</div><div class="header-cell" data-v-1b5ce221>状态</div></div>',1)),t("div",Je,[(i(!0),r(E,null,O(s.orders,c=>(i(),r("div",{key:c.orderId,class:"table-row"},[t("div",Ge,l(s.formatDate(c.time)),1),t("div",Ze,l(c.symbol),1),t("div",$e,l(s.getOrderType(c.type)),1),t("div",{class:p(["table-cell",s.getSideClass(c.side)])},l(s.getSideText(c.side)),3),t("div",et,l(s.formatQuantity(c.origQty)),1),t("div",tt,l(s.formatPrice(c.price)),1),t("div",st,l(s.formatAmount(c)),1),t("div",ot,[t("span",{class:p(["status-badge",s.getStatusClass(c.status)])},l(s.getStatusText(c.status)),3)])]))),128))])])),s.orders.length>0?(i(),r("div",nt,[t("button",{class:"page-btn",onClick:e[3]||(e[3]=(...c)=>s.loadMore&&s.loadMore(...c)),disabled:s.loading||!s.hasMore},[s.loading?(i(),r("i",lt)):v("",!0),P(" "+l(s.loading?"加载中...":s.hasMore?"加载更多":"没有更多数据"),1)],8,at)])):v("",!0)])):v("",!0)}const rt=Z(He,[["render",it],["__scopeId","data-v-1b5ce221"]]);const ct={name:"BinanceAccountInfoPanel",setup(){const b=m(null),e=m(!1),h=m(""),s=m(!1),S=A(()=>{if(!b.value||!b.value.balances||!b.value.balances.spot)return{};const C={};for(const[w,o]of Object.entries(b.value.balances.spot))o.total>0&&(C[w]=o);return C}),I=A(()=>{if(!b.value||!b.value.balances||!b.value.balances.futures)return{};const C={};for(const[w,o]of Object.entries(b.value.balances.futures))(o.walletBalance>0||o.unrealizedProfit!==0)&&(C[w]=o);return C}),c=async()=>{var C,w;e.value=!0,h.value="";try{console.log("正在获取币安账户信息...");const o=await T.get("/binance/account");o.data.success?(b.value=o.data.account,console.log("币安账户信息获取成功:",b.value)):(h.value=o.data.error||"获取账户信息失败",console.error("获取币安账户信息失败:",h.value))}catch(o){console.error("获取币安账户信息失败:",o),h.value=((w=(C=o.response)==null?void 0:C.data)==null?void 0:w.error)||"获取账户信息失败"}finally{e.value=!1}};return{accountInfo:b,loading:e,error:h,showAccountInfo:s,filteredSpotBalances:S,filteredFuturesBalances:I,refreshAccountInfo:c,toggleAccountInfo:async()=>{s.value?s.value=!1:(s.value=!0,b.value||await c())}}}},dt={class:"account-info-section"},ut={class:"account-button-section"},vt=["disabled"],ft={key:1,class:"fas fa-spinner fa-spin"},gt={key:0,class:"error-message"},mt={key:1,class:"account-details"},bt={key:0,class:"balance-section"},pt={class:"balance-grid"},_t={class:"asset-name"},ht={class:"asset-balance"},St={class:"free-balance"},yt={class:"locked-balance"},kt={class:"total-balance"},wt={key:1,class:"balance-section"},Ct={class:"balance-grid"},At={class:"asset-name"},Pt={class:"asset-balance"},It={class:"wallet-balance"},xt={class:"margin-balance"},Dt={class:"available-balance"},Tt={key:2,class:"positions-section"},Bt={class:"positions-grid"},Ft={class:"position-header"},Rt={class:"position-symbol"},Et={class:"position-details"},Ot={class:"position-info"},Lt={class:"position-info"},Nt={class:"position-info"},Ut={class:"position-info"},Mt={key:0,class:"position-info"},Qt={class:"account-status"},Ht={class:"status-row"},Vt={class:"status-item-inline"},qt={class:"status-item-inline"},zt={class:"status-item-inline"};function Wt(b,e,h,s,S,I){return i(),r("div",dt,[t("div",ut,[t("button",{class:"account-button",onClick:e[0]||(e[0]=(...c)=>s.toggleAccountInfo&&s.toggleAccountInfo(...c)),disabled:s.loading},[e[1]||(e[1]=t("i",{class:"fas fa-wallet"},null,-1)),P(" "+l(s.loading?"正在获取...":s.showAccountInfo?"隐藏账户信息":"查询账户信息")+" ",1),s.loading?v("",!0):(i(),r("i",{key:0,class:p(["fas",s.showAccountInfo?"fa-chevron-up":"fa-chevron-down"])},null,2)),s.loading?(i(),r("i",ft)):v("",!0)],8,vt)]),s.error&&s.showAccountInfo?(i(),r("div",gt,l(s.error),1)):v("",!0),s.accountInfo&&s.showAccountInfo?(i(),r("div",mt,[s.filteredSpotBalances&&Object.keys(s.filteredSpotBalances).length>0?(i(),r("div",bt,[e[2]||(e[2]=t("div",{class:"section-title"},"现货账户",-1)),t("div",pt,[(i(!0),r(E,null,O(s.filteredSpotBalances,(c,k)=>(i(),r("div",{key:k,class:"balance-item"},[t("div",_t,l(k),1),t("div",ht,[t("div",St,"可用: "+l(c.free.toFixed(8)),1),t("div",yt,"冻结: "+l(c.locked.toFixed(8)),1),t("div",kt,"总计: "+l(c.total.toFixed(8)),1)])]))),128))])])):v("",!0),s.filteredFuturesBalances&&Object.keys(s.filteredFuturesBalances).length>0?(i(),r("div",wt,[e[3]||(e[3]=t("div",{class:"section-title"},"合约账户",-1)),t("div",Ct,[(i(!0),r(E,null,O(s.filteredFuturesBalances,(c,k)=>(i(),r("div",{key:k,class:"balance-item futures-item"},[t("div",At,l(k),1),t("div",Pt,[t("div",It,"钱包余额: "+l(c.walletBalance.toFixed(8)),1),t("div",xt,"保证金余额: "+l(c.marginBalance.toFixed(8)),1),t("div",Dt,"可用余额: "+l(c.availableBalance.toFixed(8)),1),t("div",{class:p(["unrealized-profit",{"profit-positive":c.unrealizedProfit>0,"profit-negative":c.unrealizedProfit<0}])}," 未实现盈亏: "+l(c.unrealizedProfit.toFixed(8)),3)])]))),128))])])):v("",!0),s.accountInfo.balances&&s.accountInfo.balances.positions&&s.accountInfo.balances.positions.length>0?(i(),r("div",Tt,[e[9]||(e[9]=t("div",{class:"section-title"},"合约持仓",-1)),t("div",Bt,[(i(!0),r(E,null,O(s.accountInfo.balances.positions,c=>(i(),r("div",{key:c.symbol,class:"position-item"},[t("div",Ft,[t("div",Rt,l(c.symbol),1),t("div",{class:p(["position-side",{"long-position":c.positionAmt>0,"short-position":c.positionAmt<0}])},l(c.positionAmt>0?"多头":"空头"),3)]),t("div",Et,[t("div",Ot,[e[4]||(e[4]=t("span",null,"持仓数量:",-1)),t("span",null,l(Math.abs(c.positionAmt).toFixed(8)),1)]),t("div",Lt,[e[5]||(e[5]=t("span",null,"开仓价格:",-1)),t("span",null,l(c.entryPrice.toFixed(4)),1)]),t("div",Nt,[e[6]||(e[6]=t("span",null,"标记价格:",-1)),t("span",null,l(c.markPrice.toFixed(4)),1)]),t("div",Ut,[e[7]||(e[7]=t("span",null,"未实现盈亏:",-1)),t("span",{class:p({"profit-positive":c.unRealizedProfit>0,"profit-negative":c.unRealizedProfit<0})},l(c.unRealizedProfit.toFixed(4)),3)]),c.percentage?(i(),r("div",Mt,[e[8]||(e[8]=t("span",null,"盈亏比例:",-1)),t("span",{class:p({"profit-positive":c.percentage>0,"profit-negative":c.percentage<0})},l(c.percentage.toFixed(2))+"% ",3)])):v("",!0)])]))),128))])])):v("",!0),t("div",Qt,[e[15]||(e[15]=t("div",{class:"section-title"},"账户状态",-1)),t("div",Ht,[t("span",Vt,[e[10]||(e[10]=t("span",{class:"status-label"},"交易权限:",-1)),t("span",{class:p(["status-value",{"status-enabled":s.accountInfo.canTrade}])},l(s.accountInfo.canTrade?"已启用":"已禁用"),3)]),e[13]||(e[13]=t("span",{class:"status-separator"},"|",-1)),t("span",qt,[e[11]||(e[11]=t("span",{class:"status-label"},"提现权限:",-1)),t("span",{class:p(["status-value",{"status-enabled":s.accountInfo.canWithdraw}])},l(s.accountInfo.canWithdraw?"已启用":"已禁用"),3)]),e[14]||(e[14]=t("span",{class:"status-separator"},"|",-1)),t("span",zt,[e[12]||(e[12]=t("span",{class:"status-label"},"充值权限:",-1)),t("span",{class:p(["status-value",{"status-enabled":s.accountInfo.canDeposit}])},l(s.accountInfo.canDeposit?"已启用":"已禁用"),3)])])])])):v("",!0)])}const Kt=Z(ct,[["render",Wt],["__scopeId","data-v-c5d7c40d"]]);const Yt={name:"BinanceView",components:{BinanceApiModal:Me,BinanceStrategyForm:Qe,BinanceOrderHistory:rt,BinanceAccountInfoPanel:Kt},setup(){const b=m(!1),e=m(""),h=m(!1),s=m(!1),S=m([]),I=m(!1),c=m(!1),k=m(!1),C=m("card"),w=m("all"),o=m(""),R=m(!1),_=m(!1),V=m(!1),U=m(!1),x=m([]),q=m(!1),M=m(!1),Q=m(!1),f=m(!1);let u=null;const L=A(()=>S.value.filter(n=>n.status==="waiting"||n.status==="active").length),B=A(()=>S.value.filter(n=>n.status==="completed").length),H=A(()=>[...S.value].sort((n,a)=>{const d=n.status==="waiting"||n.status==="active",g=a.status==="waiting"||a.status==="active";return d&&!g?-1:!d&&g?1:new Date(a.createdAt||0)-new Date(n.createdAt||0)})),D=A(()=>{let n=H.value;if(w.value==="running"?n=n.filter(a=>a.status==="waiting"||a.status==="active"):w.value==="completed"&&(n=n.filter(a=>a.status==="completed")),o.value.trim()){const a=o.value.toLowerCase().trim();n=n.filter(d=>d.strategyName&&typeof d.strategyName=="string"&&d.strategyName.toLowerCase().includes(a)||d.symbol&&typeof d.symbol=="string"&&d.symbol.toLowerCase().includes(a)||d.type&&typeof d.type=="string"&&d.type.toLowerCase().includes(a))}return n}),$=A(()=>D.value.length>0&&x.value.length===D.value.length),ie=A(()=>x.value.length>0&&x.value.length<D.value.length),z=A(()=>D.value.filter(n=>x.value.includes(n.id||n._id)&&(n.status==="waiting"||n.status==="active"))),W=A(()=>D.value.filter(n=>x.value.includes(n.id||n._id)&&n.status!=="waiting"&&n.status!=="active")),ee=A(()=>z.value.length),te=A(()=>W.value.length),re=A(()=>ee.value>0),ce=A(()=>te.value>0),de=n=>n?new Date(n).toLocaleString():"-",ue=n=>n.status==="waiting"||n.status==="active",ve=()=>o.value.trim()?"未找到匹配的策略":w.value==="running"?"暂无运行中的策略":w.value==="completed"?"暂无已完成的策略":"暂无策略",fe=()=>{window.scrollTo({top:0,behavior:"smooth"}),_.value=!1},ge=n=>{var d;let a=null;switch(n){case"account-info":a=document.querySelector(".api-status-section")||document.querySelector(".account-info-panel");break;case"strategy-form":a=document.querySelector(".strategy-form");break;case"strategies-list":a=document.querySelector(".active-strategies");break;case"running-strategies":a=((d=document.querySelector(".strategy-card .status-active, .strategy-card .status-waiting"))==null?void 0:d.closest(".strategy-card"))||document.querySelector(".compact-strategy-row.running-strategy");break;default:return}a&&a.scrollIntoView({behavior:"smooth",block:"start"}),_.value=!1},se=()=>{R.value=window.scrollY>300,_.value&&window.scrollY>0&&(_.value=!1)},me=()=>{s.value=!s.value,s.value&&setTimeout(()=>{const n=document.getElementById("order-history-section");n&&n.scrollIntoView({behavior:"smooth",block:"start"})},100)},be=()=>{localStorage.setItem("userId",e.value),localStorage.setItem(`binanceApiConnected_${e.value}`,b.value.toString()),console.log("币安用户状态已保存到本地存储，用户ID:",e.value)},pe=async()=>{const n=localStorage.getItem("user");if(n)try{const a=JSON.parse(n);a&&a.uid&&(console.log("从登录用户信息加载用户UID:",a.uid),e.value=a.uid)}catch(a){console.error("解析用户信息失败:",a)}if(!e.value){const a=localStorage.getItem("userId");a?(console.log("从本地存储加载用户ID:",a),e.value=a):(console.log("未找到用户ID，使用默认值"),e.value="default")}await _e()},_e=async()=>{try{console.log("检查币安API密钥状态...");const n=await T.get("/binance/api-keys");n.data.success&&n.data.hasApiKeys?(console.log("币安API密钥已配置:",n.data.apiKeyPreview),b.value=!0,localStorage.setItem(`binanceApiConnected_${e.value}`,"true"),J(),await F()):(console.log("币安API密钥未配置，需要用户输入"),b.value=!1,localStorage.setItem(`binanceApiConnected_${e.value}`,"false"),I.value=!1,h.value=!0)}catch(n){console.error("检查币安API密钥状态失败:",n),localStorage.getItem(`binanceApiConnected_${e.value}`)==="true"?(console.log("从本地存储加载币安API连接状态: 已连接"),b.value=!0,J()):(console.log("币安API未连接，需要用户输入API密钥"),b.value=!1,I.value=!1,h.value=!0)}},he=async n=>{console.log("币安API验证成功，更新状态"),b.value=!0,h.value=!1,I.value=!1,localStorage.setItem(`binanceApiConnected_${e.value}`,"true"),be(),J(),S.value=[],await F()},Se=n=>{S.value.unshift({...n,createdAt:Date.now()})},J=()=>{console.log("正在连接币安WebSocket...");const n="https://api.frp-end.com";console.log("使用WebSocket URL:",n),u=Ue(n,{transports:["websocket","polling"],reconnectionAttempts:5,reconnectionDelay:1e3,timeout:2e4}),u.on("connect",()=>{console.log("币安WebSocket connected"),u.emit("subscribeBinance",{userId:e.value})}),u.on("binanceUpdate",a=>{console.log("收到币安更新:",a)}),u.on("binanceAccountInit",a=>{console.log("收到币安账户初始化数据:",a)}),u.on("disconnect",()=>{console.log("币安WebSocket disconnected")})},F=async()=>{try{console.log("正在获取币安策略列表，用户ID:",e.value);const n=await T.get("/binance/strategies");console.log("币安API响应:",n.data),n.data&&n.data.success&&n.data.strategies?(console.log(`获取到 ${n.data.strategies.length} 个币安策略`),n.data.strategies.forEach((a,d)=>{console.log(`币安策略 ${d+1}:`,{id:a.id||a._id,name:a.strategyName,status:a.status,type:a.type,exchange:a.exchange})}),S.value=n.data.strategies):(console.error("获取币安策略列表失败: 返回数据格式不正确",n.data),n.data&&n.data.error&&console.error("错误信息:",n.data.error))}catch(n){console.error("获取币安策略列表失败:",n),n.response?(console.error("错误响应状态:",n.response.status),console.error("错误响应数据:",n.response.data),n.response.status===401&&console.error("认证失败，可能需要重新登录")):n.request?console.error("网络请求失败:",n.request):console.error("请求配置错误:",n.message)}},ye=async n=>{try{if(!n){console.error("策略ID不能为空");return}const a=await T.post(`/binance/strategies/${n}/stop`);if(a.data&&a.data.success){const d=S.value.findIndex(g=>g.id===n||g._id===n);d!==-1&&(S.value[d].status="completed"),await F()}}catch(a){console.error("停止币安策略失败:",a)}},ke=async n=>{try{if(!n){console.error("策略ID不能为空");return}if(!confirm("确定要删除此策略吗？此操作不可撤销。"))return;console.log("删除币安策略:",n);const a=await T.delete(`/binance/strategies/${n}`);a.data&&a.data.success?(console.log("删除币安策略成功"),await F()):console.error("删除币安策略失败:",a.data.error)}catch(a){console.error("删除币安策略失败:",a),a.response&&console.error("错误响应:",a.response.data)}},we=n=>{switch(n){case"waiting":return"等待中";case"active":return"运行中";case"completed":return"已完成";case"executing":return"执行中";case"error":return"错误";default:return n||"未知"}},Ce=n=>{switch(n){case"waiting":return"status-waiting";case"active":return"status-active";case"completed":return"status-completed";case"executing":return"status-executing";case"error":return"status-error";default:return""}},Ae=(n,a)=>{switch(n){case"long":return a==="spot"?"买入":"做多";case"short":return"做空";case"both":return"自动判断";default:return n||"未知"}},oe=n=>{if(n.realTimeProfit!==void 0&&n.realTimeProfit!==null)return n.realTimeProfit.toFixed(2);if(!n.entryPrice||!n.currentPrice)return"0.00";const a=parseFloat(n.entryPrice),d=parseFloat(n.currentPrice),g=parseFloat(n.amount);let y=0;return n.type==="spot"||n.direction==="long"?y=(d-a)/a*g:n.direction==="short"&&(y=(a-d)/a*g),y.toFixed(2)},Pe=n=>{if(n.realTimeProfitPercentage!==void 0&&n.realTimeProfitPercentage!==null){const K=n.realTimeProfitPercentage;return`${K>0?"+":""}${K.toFixed(2)}%`}if(!n.entryPrice||!n.currentPrice)return"0.00%";const a=parseFloat(n.entryPrice),d=parseFloat(n.currentPrice);let g=0;return n.type==="spot"||n.direction==="long"?g=(d-a)/a*100:n.direction==="short"&&(g=(a-d)/a*100),`${g>0?"+":""}${g.toFixed(2)}%`},Ie=n=>{const a=parseFloat(oe(n));return{"profit-positive":a>0,"profit-negative":a<0,"profit-neutral":a===0}},xe=async n=>{try{if(!n){console.error("策略ID不能为空");return}if(!confirm("确定要立即平仓吗？此操作将关闭当前持仓并停止策略。"))return;c.value=!0,console.log("执行币安策略一键平仓:",n);const a=await T.post(`/binance/strategies/${n}/close-position`);a.data&&a.data.success?(console.log("币安策略平仓成功"),await F(),alert("平仓成功！")):(console.error("币安策略平仓失败:",a.data.error),alert("平仓失败："+(a.data.error||"未知错误")))}catch(a){console.error("币安策略平仓失败:",a),a.response&&a.response.data&&a.response.data.error?alert("平仓失败："+a.response.data.error):alert("平仓失败：网络错误或服务器错误")}finally{c.value=!1}},De=async n=>{try{if(!n){console.error("策略ID不能为空");return}if(!confirm("确定要恢复此策略吗？策略将重新开始监控市场条件。"))return;k.value=!0,console.log("恢复币安策略:",n);const a=await T.post(`/binance/strategies/${n}/recover`);a.data&&a.data.success?(console.log("币安策略恢复成功"),await F(),alert("策略恢复成功！")):(console.error("币安策略恢复失败:",a.data.error),alert("策略恢复失败："+(a.data.error||"未知错误")))}catch(a){console.error("币安策略恢复失败:",a),a.response&&a.response.data&&a.response.data.error?alert("策略恢复失败："+a.response.data.error):alert("策略恢复失败：网络错误或服务器错误")}finally{k.value=!1}},Te=()=>{console.log("用户请求重新配置币安API"),I.value=!0,h.value=!0},Be=async()=>{var n,a;try{U.value=!0,console.log("开始删除币安API配置...");const d=S.value.filter(y=>y.status==="waiting"||y.status==="active");if(d.length>0){console.log(`正在停止 ${d.length} 个运行中的策略...`);for(const y of d)try{await T.post(`/binance/strategies/${y.id||y._id}/stop`)}catch(K){console.error(`停止策略 ${y.id||y._id} 失败:`,K)}}const g=await T.delete("/binance/api-keys");g.data.success?(console.log("币安API配置删除成功"),u&&(u.disconnect(),u=null),b.value=!1,localStorage.setItem(`binanceApiConnected_${e.value}`,"false"),S.value=[],V.value=!1,alert("币安API配置已成功删除！")):(console.error("删除API配置失败:",g.data.error),alert("删除API配置失败: "+(g.data.error||"未知错误")))}catch(d){console.error("删除API配置失败:",d),alert("删除API配置失败: "+(((a=(n=d.response)==null?void 0:n.data)==null?void 0:a.error)||d.message||"网络错误"))}finally{U.value=!1}},Fe=()=>{$.value?x.value=[]:x.value=D.value.map(n=>n.id||n._id)},Re=async()=>{var n,a;if(z.value.length===0){alert("没有选中的运行中策略");return}try{Q.value=!0;const d=z.value.map(y=>y.id||y._id);console.log("批量停止币安策略:",d);const g=await T.post("/api/binance/strategies/batch-stop",{strategyIds:d});g.data&&g.data.success?(console.log("批量停止币安策略成功"),alert(`成功停止 ${g.data.stoppedCount} 个策略`),x.value=[],q.value=!1,await F()):(console.error("批量停止币安策略失败:",g.data.error),alert("批量停止策略失败: "+(g.data.error||"未知错误")))}catch(d){console.error("批量停止币安策略失败:",d),alert("批量停止策略失败: "+(((a=(n=d.response)==null?void 0:n.data)==null?void 0:a.error)||d.message||"网络错误"))}finally{Q.value=!1}},Ee=async()=>{var n,a;if(W.value.length===0){alert("没有选中的可删除策略");return}try{f.value=!0;const d=W.value.map(y=>y.id||y._id);console.log("批量删除币安策略:",d);const g=await T.post("/api/binance/strategies/batch-delete",{strategyIds:d});g.data&&g.data.success?(console.log("批量删除币安策略成功"),alert(`成功删除 ${g.data.deletedCount} 个策略`),x.value=[],M.value=!1,await F()):(console.error("批量删除币安策略失败:",g.data.error),alert("批量删除策略失败: "+(g.data.error||"未知错误")))}catch(d){console.error("批量删除币安策略失败:",d),alert("批量删除策略失败: "+(((a=(n=d.response)==null?void 0:n.data)==null?void 0:a.error)||d.message||"网络错误"))}finally{f.value=!1}};return le(()=>{pe(),window.addEventListener("scroll",se)}),Le(()=>{u&&u.disconnect(),window.removeEventListener("scroll",se)}),{apiConnected:b,userId:e,showApiModal:h,showOrderHistory:s,strategies:S,isReconfiguring:I,isClosingPosition:c,isRecoveringStrategy:k,viewMode:C,statusFilter:w,searchQuery:o,showScrollTop:R,showQuickNav:_,showDeleteApiConfirm:V,isDeletingApi:U,selectedStrategies:x,showBatchStopConfirm:q,showBatchDeleteConfirm:M,isBatchStopping:Q,isBatchDeleting:f,runningStrategiesCount:L,completedStrategiesCount:B,filteredStrategies:D,isAllSelected:$,isPartiallySelected:ie,selectedRunningStrategies:z,selectedNonRunningStrategies:W,runningSelectedCount:ee,nonRunningSelectedCount:te,hasRunningSelectedStrategies:re,hasNonRunningSelectedStrategies:ce,formatDate:de,isRunningStrategy:ue,getNoStrategiesText:ve,scrollToTop:fe,scrollToSection:ge,toggleOrderHistory:me,onApiValidated:he,onStrategyCreated:Se,fetchStrategies:F,stopStrategy:ye,deleteStrategy:ke,getStatusText:we,getStatusClass:Ce,getDirectionText:Ae,calculateProfit:oe,calculateProfitPercentage:Pe,getProfitClass:Ie,closePosition:xe,recoverStrategy:De,reconfigureApi:Te,deleteApi:Be,toggleSelectAll:Fe,batchStopStrategies:Re,batchDeleteStrategies:Ee}}},jt={class:"okx-view"},Xt={class:"main-content"},Jt={key:0,class:"connect-api-section"},Gt={class:"card"},Zt={class:"card-content"},$t={key:1},es={class:"api-status-section"},ts={class:"api-status-info"},ss={class:"api-actions"},os={class:"active-strategies"},ns={class:"strategies-header"},as={class:"strategies-controls"},ls={class:"view-mode-toggle"},is={class:"status-filter"},rs={class:"header-actions"},cs={class:"btn-text"},ds={class:"search-section"},us={class:"search-box"},vs={key:0,class:"batch-operations"},fs={class:"batch-controls"},gs={class:"selection-controls"},ms={class:"checkbox-container"},bs=["checked","indeterminate"],ps={class:"checkbox-label"},_s={key:0,class:"batch-actions"},hs=["disabled"],Ss=["disabled"],ys={key:1,class:"no-strategies"},ks={key:2,class:"strategies-list"},ws={class:"strategy-header"},Cs={class:"strategy-header-left"},As={class:"strategy-checkbox"},Ps=["value"],Is={class:"strategy-type"},xs={class:"strategy-header-right"},Ds={key:0,class:"network-error-warning"},Ts={class:"strategy-details"},Bs={key:0,class:"strategy-item strategy-name"},Fs={class:"value"},Rs={class:"strategy-item"},Es={class:"value"},Os={class:"strategy-item"},Ls={class:"value"},Ns={class:"strategy-item"},Us={class:"value"},Ms={key:1,class:"strategy-item"},Qs={class:"value"},Hs={class:"strategy-item"},Vs={class:"value"},qs={key:2,class:"strategy-item"},zs={class:"value"},Ws={key:3,class:"strategy-item"},Ks={class:"value risk-value"},Ys={key:4,class:"strategy-item"},js={class:"value profit-value"},Xs={key:5,class:"strategy-item"},Js={class:"profit-percentage"},Gs={key:6,class:"strategy-item"},Zs={key:0},$s={class:"strategy-actions"},eo={class:"action-buttons"},to=["onClick"],so=["onClick","disabled"],oo=["onClick","disabled"],no={key:3,class:"strategy-status-info"},ao=["onClick"],lo={class:"delete-action"},io=["onClick"],ro={key:3,class:"strategies-compact-list"},co={class:"compact-col checkbox-col"},uo={class:"strategy-checkbox-compact"},vo=["value"],fo={class:"compact-col strategy-name-col"},go={class:"strategy-name-compact"},mo={class:"strategy-type-compact"},bo={class:"compact-col"},po={class:"symbol-text"},_o={class:"compact-col"},ho={class:"compact-col"},So={class:"amount-text"},yo={class:"compact-col"},ko={key:2,class:"profit-text"},wo={class:"compact-col"},Co={class:"compact-actions"},Ao=["onClick"],Po=["onClick"],Io=["onClick"],xo=["onClick"],Do={key:0,id:"order-history-section"},To={key:0,class:"floating-actions"},Bo={class:"floating-btn-group"},Fo={class:"quick-nav-menu"},Ro={class:"delete-confirm-modal"},Eo={class:"modal-header"},Oo={class:"modal-footer"},Lo=["disabled"],No={class:"batch-confirm-modal"},Uo={class:"modal-header"},Mo={class:"modal-body"},Qo={class:"warning-text"},Ho={class:"strategy-list-preview"},Vo={class:"strategy-name"},qo={class:"strategy-symbol"},zo={class:"strategy-status"},Wo={class:"modal-footer"},Ko=["disabled"],Yo={class:"batch-confirm-modal"},jo={class:"modal-header"},Xo={class:"modal-body"},Jo={class:"warning-text"},Go={class:"strategy-list-preview"},Zo={class:"strategy-name"},$o={class:"strategy-symbol"},en={class:"strategy-status"},tn={class:"modal-footer"},sn=["disabled"];function on(b,e,h,s,S,I){const c=Y("BinanceAccountInfoPanel"),k=Y("BinanceStrategyForm"),C=Y("BinanceOrderHistory"),w=Y("BinanceApiModal");return i(),r("div",jt,[e[86]||(e[86]=t("div",{class:"market-header"},"币安交易",-1)),t("div",Xt,[s.apiConnected?(i(),r("div",$t,[t("div",es,[t("div",ts,[e[40]||(e[40]=t("span",{class:"api-status-text"},"✅ API已连接",-1)),t("div",ss,[t("button",{onClick:e[1]||(e[1]=(...o)=>s.reconfigureApi&&s.reconfigureApi(...o)),class:"reconfigure-button"}," 重新配置API "),t("button",{onClick:e[2]||(e[2]=o=>s.showDeleteApiConfirm=!0),class:"delete-api-button"},e[39]||(e[39]=[t("i",{class:"fas fa-trash"},null,-1),P(" 删除API ")]))])])]),j(c),j(k,{apiConnected:s.apiConnected,userId:s.userId,onStrategyCreated:s.onStrategyCreated},null,8,["apiConnected","userId","onStrategyCreated"]),t("div",os,[t("div",ns,[e[44]||(e[44]=t("div",{class:"section-title"},"策略管理",-1)),t("div",as,[t("div",ls,[t("button",{class:p(["view-mode-btn",{active:s.viewMode==="card"}]),onClick:e[3]||(e[3]=o=>s.viewMode="card"),title:"卡片视图"},e[41]||(e[41]=[t("i",{class:"fas fa-th-large"},null,-1)]),2),t("button",{class:p(["view-mode-btn",{active:s.viewMode==="compact"}]),onClick:e[4]||(e[4]=o=>s.viewMode="compact"),title:"紧凑视图"},e[42]||(e[42]=[t("i",{class:"fas fa-list"},null,-1)]),2)]),t("div",is,[t("button",{class:p(["filter-btn",{active:s.statusFilter==="all"}]),onClick:e[5]||(e[5]=o=>s.statusFilter="all")}," 全部 ("+l(s.strategies.length)+") ",3),t("button",{class:p(["filter-btn",{active:s.statusFilter==="running"}]),onClick:e[6]||(e[6]=o=>s.statusFilter="running")}," 运行中 ("+l(s.runningStrategiesCount)+") ",3),t("button",{class:p(["filter-btn",{active:s.statusFilter==="completed"}]),onClick:e[7]||(e[7]=o=>s.statusFilter="completed")}," 已完成 ("+l(s.completedStrategiesCount)+") ",3)]),t("div",rs,[t("button",{class:"header-action-btn history-btn",onClick:e[8]||(e[8]=(...o)=>s.toggleOrderHistory&&s.toggleOrderHistory(...o)),title:"查询历史订单"},[e[43]||(e[43]=t("i",{class:"fas fa-history"},null,-1)),t("span",cs,l(s.showOrderHistory?"隐藏订单":"历史订单"),1)])])])]),t("div",ds,[t("div",us,[e[46]||(e[46]=t("i",{class:"fas fa-search search-icon"},null,-1)),N(t("input",{type:"text","onUpdate:modelValue":e[9]||(e[9]=o=>s.searchQuery=o),placeholder:"搜索策略名称、交易对...",class:"search-input"},null,512),[[Ne,s.searchQuery]]),s.searchQuery?(i(),r("button",{key:0,onClick:e[10]||(e[10]=o=>s.searchQuery=""),class:"clear-search"},e[45]||(e[45]=[t("i",{class:"fas fa-times"},null,-1)]))):v("",!0)])]),s.filteredStrategies.length>0?(i(),r("div",vs,[t("div",fs,[t("div",gs,[t("label",ms,[t("input",{type:"checkbox",checked:s.isAllSelected,indeterminate:s.isPartiallySelected,onChange:e[11]||(e[11]=(...o)=>s.toggleSelectAll&&s.toggleSelectAll(...o))},null,40,bs),e[47]||(e[47]=t("span",{class:"checkmark"},null,-1)),t("span",ps,l(s.selectedStrategies.length>0?`已选择 ${s.selectedStrategies.length} 个策略`:"全选"),1)])]),s.selectedStrategies.length>0?(i(),r("div",_s,[t("button",{class:"batch-btn stop-batch-btn",onClick:e[12]||(e[12]=o=>s.showBatchStopConfirm=!0),disabled:!s.hasRunningSelectedStrategies,title:"批量停止选中的运行中策略"},[e[48]||(e[48]=t("i",{class:"fas fa-stop"},null,-1)),P(" 停止选中策略 ("+l(s.runningSelectedCount)+") ",1)],8,hs),t("button",{class:"batch-btn delete-batch-btn",onClick:e[13]||(e[13]=o=>s.showBatchDeleteConfirm=!0),disabled:!s.hasNonRunningSelectedStrategies,title:"批量删除选中的非运行中策略"},[e[49]||(e[49]=t("i",{class:"fas fa-trash"},null,-1)),P(" 删除选中策略 ("+l(s.nonRunningSelectedCount)+") ",1)],8,Ss)])):v("",!0)])])):v("",!0),s.filteredStrategies.length===0?(i(),r("div",ys,l(s.getNoStrategiesText()),1)):s.viewMode==="card"?(i(),r("div",ks,[(i(!0),r(E,null,O(s.filteredStrategies,(o,R)=>(i(),r("div",{key:R,class:"strategy-card"},[t("div",ws,[t("div",Cs,[t("label",As,[N(t("input",{type:"checkbox",value:o.id||o._id,"onUpdate:modelValue":e[14]||(e[14]=_=>s.selectedStrategies=_)},null,8,Ps),[[ae,s.selectedStrategies]]),e[50]||(e[50]=t("span",{class:"checkmark-small"},null,-1))]),t("div",Is,l(o.type==="spot"?"现货策略":"合约策略"),1)]),t("div",xs,[t("div",{class:p(["strategy-status",s.getStatusClass(o.status)])},l(s.getStatusText(o.status)),3),o.networkStatus==="network_error"?(i(),r("div",Ds,e[51]||(e[51]=[t("i",{class:"fas fa-exclamation-triangle"},null,-1),P(" 网络连接失败 ")]))):v("",!0)])]),t("div",Ts,[o.strategyName?(i(),r("div",Bs,[t("div",Fs,l(o.strategyName),1)])):v("",!0),t("div",Rs,[e[52]||(e[52]=t("div",{class:"label"},"交易对",-1)),t("div",Es,l(o.symbol),1)]),t("div",Os,[e[53]||(e[53]=t("div",{class:"label"},"方向",-1)),t("div",Ls,l(s.getDirectionText(o.direction,o.type)),1)]),t("div",Ns,[e[54]||(e[54]=t("div",{class:"label"},"金额",-1)),t("div",Us,l(o.amount)+" USDT",1)]),o.type==="futures"?(i(),r("div",Ms,[e[55]||(e[55]=t("div",{class:"label"},"杠杆",-1)),t("div",Qs,l(o.leverage)+"x",1)])):v("",!0),t("div",Hs,[e[56]||(e[56]=t("div",{class:"label"},"创建时间",-1)),t("div",Vs,l(s.formatDate(o.createdAt)),1)]),o.entryPrice&&o.entryPrice>0?(i(),r("div",qs,[e[57]||(e[57]=t("div",{class:"label"},"入场价格",-1)),t("div",zs,l(o.entryPrice.toFixed(2)),1)])):v("",!0),o.stopLoss&&o.stopLoss>0?(i(),r("div",Ws,[e[58]||(e[58]=t("div",{class:"label"},"止损价格",-1)),t("div",Ks,l(o.stopLoss.toFixed(2)),1)])):v("",!0),o.takeProfit&&o.takeProfit>0?(i(),r("div",Ys,[e[59]||(e[59]=t("div",{class:"label"},"止盈价格",-1)),t("div",js,l(o.takeProfit.toFixed(2)),1)])):v("",!0),o.status==="active"&&o.entryPrice&&o.currentPrice?(i(),r("div",Xs,[e[60]||(e[60]=t("div",{class:"label"},"实时盈利",-1)),t("div",{class:p(["value",s.getProfitClass(o)])},[P(l(s.calculateProfit(o))+" USDT ",1),t("span",Js," ("+l(s.calculateProfitPercentage(o))+") ",1)],2)])):v("",!0),o.profit!==void 0&&o.profit!==0&&o.status==="completed"?(i(),r("div",Gs,[e[61]||(e[61]=t("div",{class:"label"},"最终收益",-1)),t("div",{class:p(["value",{"profit-positive":o.profit>0,"profit-negative":o.profit<0}])},[P(l(o.profit.toFixed(2))+" USDT ",1),o.profitPercentage!==void 0?(i(),r("span",Zs," ("+l(o.profitPercentage>0?"+":"")+l(o.profitPercentage.toFixed(2))+"%) ",1)):v("",!0)],2)])):v("",!0),t("div",$s,[t("div",eo,[o.status==="waiting"||o.status==="active"?(i(),r("button",{key:0,class:"stop-button",onClick:_=>s.stopStrategy(o.id||o._id)}," 停止策略 ",8,to)):v("",!0),o.status==="active"?(i(),r("button",{key:1,class:"close-position-button",onClick:_=>s.closePosition(o.id||o._id),disabled:s.isClosingPosition},l(s.isClosingPosition?"平仓中...":"一键平仓"),9,so)):v("",!0),o.status==="error"?(i(),r("button",{key:2,class:"recover-button",onClick:_=>s.recoverStrategy(o.id||o._id),disabled:s.isRecoveringStrategy},l(s.isRecoveringStrategy?"恢复中...":"恢复策略"),9,oo)):o.status!=="error"&&o.status!=="waiting"&&o.status!=="active"?(i(),r("div",no,l(s.getStatusText(o.status)),1)):v("",!0),o.status==="error"?(i(),r("button",{key:4,class:"delete-button",onClick:_=>s.deleteStrategy(o.id||o._id)}," 删除 ",8,ao)):v("",!0)]),t("div",lo,[o.status!=="waiting"&&o.status!=="active"?(i(),r("button",{key:0,class:"delete-button-small",title:"删除策略",onClick:_=>s.deleteStrategy(o.id||o._id)},e[62]||(e[62]=[t("span",{class:"delete-icon"},"×",-1)]),8,io)):v("",!0)])])])]))),128))])):s.viewMode==="compact"?(i(),r("div",ro,[e[68]||(e[68]=X('<div class="compact-header" data-v-49181585><div class="compact-col checkbox-col" data-v-49181585>选择</div><div class="compact-col strategy-name-col" data-v-49181585>策略名称</div><div class="compact-col" data-v-49181585>交易对</div><div class="compact-col" data-v-49181585>状态</div><div class="compact-col" data-v-49181585>金额</div><div class="compact-col" data-v-49181585>盈利</div><div class="compact-col" data-v-49181585>操作</div></div>',1)),(i(!0),r(E,null,O(s.filteredStrategies,(o,R)=>(i(),r("div",{key:R,class:p(["compact-strategy-row",{"running-strategy":s.isRunningStrategy(o)}])},[t("div",co,[t("label",uo,[N(t("input",{type:"checkbox",value:o.id||o._id,"onUpdate:modelValue":e[15]||(e[15]=_=>s.selectedStrategies=_)},null,8,vo),[[ae,s.selectedStrategies]]),e[63]||(e[63]=t("span",{class:"checkmark-small"},null,-1))])]),t("div",fo,[t("div",go,l(o.strategyName||"未命名策略"),1),t("div",mo,l(o.type==="spot"?"现货":"合约"),1)]),t("div",bo,[t("span",po,l(o.symbol),1)]),t("div",_o,[t("span",{class:p(["strategy-status",s.getStatusClass(o.status)])},l(s.getStatusText(o.status)),3)]),t("div",ho,[t("span",So,l(o.amount)+" USDT",1)]),t("div",yo,[o.status==="active"&&o.entryPrice&&o.currentPrice?(i(),r("span",{key:0,class:p([s.getProfitClass(o),"profit-text"])},l(s.calculateProfit(o))+" USDT ",3)):o.profit!==void 0&&o.profit!==0&&o.status==="completed"?(i(),r("span",{key:1,class:p([{"profit-positive":o.profit>0,"profit-negative":o.profit<0},"profit-text"])},l(o.profit.toFixed(2))+" USDT ",3)):(i(),r("span",ko,"-"))]),t("div",wo,[t("div",Co,[o.status==="waiting"||o.status==="active"?(i(),r("button",{key:0,class:"compact-btn stop-btn",onClick:_=>s.stopStrategy(o.id||o._id),title:"停止策略"},e[64]||(e[64]=[t("i",{class:"fas fa-stop"},null,-1)]),8,Ao)):v("",!0),o.status==="active"?(i(),r("button",{key:1,class:"compact-btn close-btn",onClick:_=>s.closePosition(o.id||o._id),title:"一键平仓"},e[65]||(e[65]=[t("i",{class:"fas fa-hand-paper"},null,-1)]),8,Po)):v("",!0),o.status==="error"?(i(),r("button",{key:2,class:"compact-btn recover-btn",onClick:_=>s.recoverStrategy(o.id||o._id),title:"恢复策略"},e[66]||(e[66]=[t("i",{class:"fas fa-redo"},null,-1)]),8,Io)):v("",!0),o.status!=="waiting"&&o.status!=="active"?(i(),r("button",{key:3,class:"compact-btn delete-btn",onClick:_=>s.deleteStrategy(o.id||o._id),title:"删除策略"},e[67]||(e[67]=[t("i",{class:"fas fa-trash"},null,-1)]),8,xo)):v("",!0)])])],2))),128))])):v("",!0)]),s.showOrderHistory?(i(),r("div",Do,[j(C,{apiConnected:s.apiConnected,userId:s.userId,visible:s.showOrderHistory},null,8,["apiConnected","userId","visible"])])):v("",!0)])):(i(),r("div",Jt,[t("div",Gt,[t("div",Zt,[e[37]||(e[37]=t("h3",null,"连接币安API",-1)),e[38]||(e[38]=t("p",null,"请先连接您的币安API以使用交易功能",-1)),t("button",{class:"connect-button",onClick:e[0]||(e[0]=o=>s.showApiModal=!0)},e[36]||(e[36]=[t("i",{class:"fas fa-plug"},null,-1),P(" 连接API ")]))])])]))]),s.apiConnected?(i(),r("div",To,[t("div",Bo,[N(t("button",{class:"floating-btn scroll-top-btn",onClick:e[16]||(e[16]=(...o)=>s.scrollToTop&&s.scrollToTop(...o)),title:"回到顶部"},e[69]||(e[69]=[t("i",{class:"fas fa-arrow-up"},null,-1)]),512),[[ne,s.showScrollTop]]),t("button",{class:"floating-btn nav-btn",onClick:e[17]||(e[17]=o=>s.showQuickNav=!s.showQuickNav),title:"快速导航"},e[70]||(e[70]=[t("i",{class:"fas fa-compass"},null,-1)]))]),N(t("div",Fo,[t("button",{class:"nav-item",onClick:e[18]||(e[18]=o=>s.scrollToSection("account-info"))},e[71]||(e[71]=[t("i",{class:"fas fa-wallet"},null,-1),t("span",null,"账户信息",-1)])),t("button",{class:"nav-item",onClick:e[19]||(e[19]=o=>s.scrollToSection("strategy-form"))},e[72]||(e[72]=[t("i",{class:"fas fa-plus"},null,-1),t("span",null,"创建策略",-1)])),t("button",{class:"nav-item",onClick:e[20]||(e[20]=o=>s.scrollToSection("strategies-list"))},e[73]||(e[73]=[t("i",{class:"fas fa-list"},null,-1),t("span",null,"策略列表",-1)])),t("button",{class:"nav-item",onClick:e[21]||(e[21]=(...o)=>s.toggleOrderHistory&&s.toggleOrderHistory(...o))},[e[74]||(e[74]=t("i",{class:"fas fa-history"},null,-1)),t("span",null,l(s.showOrderHistory?"隐藏订单":"历史订单"),1)]),s.runningStrategiesCount>0?(i(),r("button",{key:0,class:"nav-item",onClick:e[22]||(e[22]=o=>s.scrollToSection("running-strategies"))},[e[75]||(e[75]=t("i",{class:"fas fa-play"},null,-1)),t("span",null,"运行中策略 ("+l(s.runningStrategiesCount)+")",1)])):v("",!0)],512),[[ne,s.showQuickNav]])])):v("",!0),j(w,{show:s.showApiModal,isReconfiguring:s.isReconfiguring,onClose:e[23]||(e[23]=o=>s.showApiModal=!1),onApiValidated:s.onApiValidated},null,8,["show","isReconfiguring","onApiValidated"]),s.showDeleteApiConfirm?(i(),r("div",{key:1,class:"modal-overlay",onClick:e[27]||(e[27]=G(o=>s.showDeleteApiConfirm=!1,["self"]))},[t("div",Ro,[t("div",Eo,[e[76]||(e[76]=t("h3",null,"确认删除API",-1)),t("button",{class:"close-button",onClick:e[24]||(e[24]=o=>s.showDeleteApiConfirm=!1)},"×")]),e[78]||(e[78]=X('<div class="modal-body" data-v-49181585><div class="warning-icon" data-v-49181585><i class="fas fa-exclamation-triangle" data-v-49181585></i></div><p class="warning-text" data-v-49181585>您确定要删除币安API配置吗？</p><p class="warning-details" data-v-49181585>删除后将：</p><ul class="warning-list" data-v-49181585><li data-v-49181585>断开与币安的连接</li><li data-v-49181585>停止所有运行中的策略</li><li data-v-49181585>清除已保存的API密钥</li><li data-v-49181585>需要重新配置才能使用交易功能</li></ul></div>',1)),t("div",Oo,[t("button",{class:"cancel-button",onClick:e[25]||(e[25]=o=>s.showDeleteApiConfirm=!1)}," 取消 "),t("button",{class:"confirm-delete-button",onClick:e[26]||(e[26]=(...o)=>s.deleteApi&&s.deleteApi(...o)),disabled:s.isDeletingApi},[e[77]||(e[77]=t("i",{class:"fas fa-trash"},null,-1)),P(" "+l(s.isDeletingApi?"删除中...":"确认删除"),1)],8,Lo)])])])):v("",!0),s.showBatchStopConfirm?(i(),r("div",{key:2,class:"modal-overlay",onClick:e[31]||(e[31]=G(o=>s.showBatchStopConfirm=!1,["self"]))},[t("div",No,[t("div",Uo,[e[79]||(e[79]=t("h3",null,"确认批量停止策略",-1)),t("button",{class:"close-button",onClick:e[28]||(e[28]=o=>s.showBatchStopConfirm=!1)},"×")]),t("div",Mo,[e[80]||(e[80]=t("div",{class:"warning-icon"},[t("i",{class:"fas fa-stop-circle"})],-1)),t("p",Qo,"您确定要停止选中的 "+l(s.runningSelectedCount)+" 个运行中的策略吗？",1),t("div",Ho,[(i(!0),r(E,null,O(s.selectedRunningStrategies,o=>(i(),r("div",{key:o.id||o._id,class:"strategy-preview-item"},[t("span",Vo,l(o.strategyName||"未命名策略"),1),t("span",qo,l(o.symbol),1),t("span",zo,l(s.getStatusText(o.status)),1)]))),128))])]),t("div",Wo,[t("button",{class:"cancel-button",onClick:e[29]||(e[29]=o=>s.showBatchStopConfirm=!1)}," 取消 "),t("button",{class:"confirm-stop-button",onClick:e[30]||(e[30]=(...o)=>s.batchStopStrategies&&s.batchStopStrategies(...o)),disabled:s.isBatchStopping},[e[81]||(e[81]=t("i",{class:"fas fa-stop"},null,-1)),P(" "+l(s.isBatchStopping?"停止中...":"确认停止"),1)],8,Ko)])])])):v("",!0),s.showBatchDeleteConfirm?(i(),r("div",{key:3,class:"modal-overlay",onClick:e[35]||(e[35]=G(o=>s.showBatchDeleteConfirm=!1,["self"]))},[t("div",Yo,[t("div",jo,[e[82]||(e[82]=t("h3",null,"确认批量删除策略",-1)),t("button",{class:"close-button",onClick:e[32]||(e[32]=o=>s.showBatchDeleteConfirm=!1)},"×")]),t("div",Xo,[e[83]||(e[83]=t("div",{class:"warning-icon"},[t("i",{class:"fas fa-exclamation-triangle"})],-1)),t("p",Jo,"您确定要删除选中的 "+l(s.nonRunningSelectedCount)+" 个策略吗？",1),e[84]||(e[84]=t("p",{class:"warning-details"},"此操作不可撤销！",-1)),t("div",Go,[(i(!0),r(E,null,O(s.selectedNonRunningStrategies,o=>(i(),r("div",{key:o.id||o._id,class:"strategy-preview-item"},[t("span",Zo,l(o.strategyName||"未命名策略"),1),t("span",$o,l(o.symbol),1),t("span",en,l(s.getStatusText(o.status)),1)]))),128))])]),t("div",tn,[t("button",{class:"cancel-button",onClick:e[33]||(e[33]=o=>s.showBatchDeleteConfirm=!1)}," 取消 "),t("button",{class:"confirm-delete-button",onClick:e[34]||(e[34]=(...o)=>s.batchDeleteStrategies&&s.batchDeleteStrategies(...o)),disabled:s.isBatchDeleting},[e[85]||(e[85]=t("i",{class:"fas fa-trash"},null,-1)),P(" "+l(s.isBatchDeleting?"删除中...":"确认删除"),1)],8,sn)])])])):v("",!0)])}const cn=Z(Yt,[["render",on],["__scopeId","data-v-49181585"]]);export{cn as default};
