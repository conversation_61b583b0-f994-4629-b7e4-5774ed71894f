import{_ as L,u as N,a as B,r as n,b as I,o as R,c as i,d as l,e as o,n as c,w as p,f as u,v as w,g as d,h as P,i as b,j as x,t as h,k as D}from"./index-a2cd3c28.js";const j={name:"LoginView",setup(){const y=N(),s=B(),f=n("login"),e=n(!1),C=n(!1),M=n(!1),a=n(!1),k=n(!1),m=n(""),v=n(""),r=I({email:"",username:"",password:"",confirmPassword:"",inviteCode:""}),T=()=>{r.email="",r.username="",r.password="",r.confirmPassword="",r.inviteCode="",m.value="",v.value=""},V=()=>{if(f.value==="register"){if(r.password!==r.confirmPassword)return m.value="两次输入的密码不一致",!1;if(r.password.length<6)return m.value="密码长度至少6位",!1;if(!k.value)return m.value="请同意用户协议",!1}return!0},S=async()=>{var t,g;if(V()){e.value=!0,m.value="",v.value="";try{f.value==="login"?await U():await q()}catch(_){console.error("认证错误:",_),m.value=((g=(t=_.response)==null?void 0:t.data)==null?void 0:g.error)||"操作失败，请重试"}finally{e.value=!1}}},U=async()=>{const t=await D.post("/auth/login",{email:r.email,password:r.password,rememberMe:a.value});t.data.success&&(localStorage.setItem("user",JSON.stringify(t.data.user)),localStorage.setItem("token",t.data.token),v.value="登录成功！",setTimeout(()=>{y.push({name:"home"})},1e3))},q=async()=>{const t={email:r.email,username:r.username,password:r.password};r.inviteCode.trim()&&(t.inviteCode=r.inviteCode.trim()),(await D.post("/auth/register",t)).data.success&&(r.inviteCode.trim()?v.value="注册成功！邀请码已生效，请登录":v.value="注册成功！请登录",setTimeout(()=>{f.value="login",r.password="",r.confirmPassword="",r.inviteCode=""},1500))},A=()=>{alert("忘记密码功能暂未开放，请联系客服")},F=()=>{alert("用户协议内容")};return R(()=>{const t=s.query.invite;t&&(r.inviteCode=t,f.value="register")}),{authMode:f,loading:e,showPassword:C,showConfirmPassword:M,rememberMe:a,agreeToTerms:k,errorMessage:m,successMessage:v,formData:r,handleSubmit:S,handleForgotPassword:A,showTerms:F,resetForm:T}}},z={class:"login-view"},E={class:"login-container"},J={class:"auth-tabs"},O={class:"form-group"},G={class:"input-wrapper"},H={key:0,class:"form-group"},K={class:"input-wrapper"},Q={class:"form-group"},W={class:"input-wrapper"},X=["type"],Y={key:1,class:"form-group"},Z={class:"input-wrapper"},$=["type"],ss={key:2,class:"form-group"},es={class:"input-wrapper"},os={key:3,class:"form-options"},as={class:"remember-me"},rs={class:"forgot-password"},ts={key:4,class:"form-options"},is={class:"agreement"},ls={for:"agreement"},ns=["disabled"],ds={key:0,class:"fas fa-spinner fa-spin"},ms={key:0,class:"error-message"},us={key:1,class:"success-message"};function fs(y,s,f,e,C,M){return i(),l("div",z,[s[24]||(s[24]=o("div",{class:"background-decoration"},[o("div",{class:"decoration-shape shape-1"}),o("div",{class:"decoration-shape shape-2"}),o("div",{class:"decoration-shape shape-3"})],-1)),o("div",E,[s[23]||(s[23]=o("div",{class:"logo-section"},[o("div",{class:"logo"},[o("span",{class:"logo-text"},"AACoin")]),o("p",{class:"welcome-text"},"欢迎使用AACoin")],-1)),o("div",J,[o("div",{class:c(["auth-tab",{active:e.authMode==="login"}]),onClick:s[0]||(s[0]=a=>e.authMode="login")}," 登录 ",2),o("div",{class:c(["auth-tab",{active:e.authMode==="register"}]),onClick:s[1]||(s[1]=a=>e.authMode="register")}," 注册 ",2)]),o("form",{onSubmit:s[13]||(s[13]=p((...a)=>e.handleSubmit&&e.handleSubmit(...a),["prevent"])),class:"auth-form"},[o("div",O,[s[14]||(s[14]=o("label",{class:"form-label"},"邮箱",-1)),o("div",G,[u(o("input",{"onUpdate:modelValue":s[2]||(s[2]=a=>e.formData.email=a),type:"email",class:"form-input",placeholder:"请输入邮箱地址",required:""},null,512),[[w,e.formData.email]])])]),e.authMode==="register"?(i(),l("div",H,[s[15]||(s[15]=o("label",{class:"form-label"},"用户名",-1)),o("div",K,[u(o("input",{"onUpdate:modelValue":s[3]||(s[3]=a=>e.formData.username=a),type:"text",class:"form-input",placeholder:"请输入用户名",required:""},null,512),[[w,e.formData.username]])])])):d("",!0),o("div",Q,[s[16]||(s[16]=o("label",{class:"form-label"},"密码",-1)),o("div",W,[u(o("input",{"onUpdate:modelValue":s[4]||(s[4]=a=>e.formData.password=a),type:e.showPassword?"text":"password",class:"form-input",placeholder:"请输入密码",required:""},null,8,X),[[P,e.formData.password]]),o("i",{class:c([e.showPassword?"fas fa-eye-slash":"fas fa-eye","password-toggle"]),onClick:s[5]||(s[5]=a=>e.showPassword=!e.showPassword)},null,2)])]),e.authMode==="register"?(i(),l("div",Y,[s[17]||(s[17]=o("label",{class:"form-label"},"确认密码",-1)),o("div",Z,[u(o("input",{"onUpdate:modelValue":s[6]||(s[6]=a=>e.formData.confirmPassword=a),type:e.showConfirmPassword?"text":"password",class:"form-input",placeholder:"请再次输入密码",required:""},null,8,$),[[P,e.formData.confirmPassword]]),o("i",{class:c([e.showConfirmPassword?"fas fa-eye-slash":"fas fa-eye","password-toggle"]),onClick:s[7]||(s[7]=a=>e.showConfirmPassword=!e.showConfirmPassword)},null,2)])])):d("",!0),e.authMode==="register"?(i(),l("div",ss,[s[19]||(s[19]=o("label",{class:"form-label"},[b("邀请码 "),o("span",{class:"optional-label"},"(可选)")],-1)),o("div",es,[u(o("input",{"onUpdate:modelValue":s[8]||(s[8]=a=>e.formData.inviteCode=a),type:"text",class:"form-input",placeholder:"请输入邀请码（如：AC000123）"},null,512),[[w,e.formData.inviteCode]]),s[18]||(s[18]=o("i",{class:"fas fa-gift invite-icon"},null,-1))]),s[20]||(s[20]=o("div",{class:"invite-hint"},[o("i",{class:"fas fa-info-circle"}),o("span",null,"输入邀请码可获得注册奖励")],-1))])):d("",!0),e.authMode==="login"?(i(),l("div",os,[o("div",as,[u(o("input",{type:"checkbox",id:"remember","onUpdate:modelValue":s[9]||(s[9]=a=>e.rememberMe=a)},null,512),[[x,e.rememberMe]]),s[21]||(s[21]=o("label",{for:"remember"},"记住密码",-1))]),o("div",rs,[o("a",{href:"#",onClick:s[10]||(s[10]=p((...a)=>e.handleForgotPassword&&e.handleForgotPassword(...a),["prevent"]))},"忘记密码？")])])):d("",!0),e.authMode==="register"?(i(),l("div",ts,[o("div",is,[u(o("input",{type:"checkbox",id:"agreement","onUpdate:modelValue":s[11]||(s[11]=a=>e.agreeToTerms=a),required:""},null,512),[[x,e.agreeToTerms]]),o("label",ls,[s[22]||(s[22]=b(" 我同意 ")),o("a",{href:"#",onClick:s[12]||(s[12]=p((...a)=>e.showTerms&&e.showTerms(...a),["prevent"]))},"《用户协议》")])])])):d("",!0),o("button",{type:"submit",class:"submit-button",disabled:e.loading},[e.loading?(i(),l("i",ds)):d("",!0),b(" "+h(e.authMode==="login"?"登录":"注册"),1)],8,ns)],32),e.errorMessage?(i(),l("div",ms,h(e.errorMessage),1)):d("",!0),e.successMessage?(i(),l("div",us,h(e.successMessage),1)):d("",!0)])])}const cs=L(j,[["render",fs],["__scopeId","data-v-ecb0eb00"]]);export{cs as default};
