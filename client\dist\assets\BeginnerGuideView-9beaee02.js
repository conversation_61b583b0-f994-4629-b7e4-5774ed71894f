import{_ as x,u as C,r as c,c as n,d as l,e as s,C as B,n as v,i as f,F as d,p as r,g,t as e}from"./index-a2cd3c28.js";const w={name:"BeginnerGuideView",setup(){const p=C(),i=c("basics"),m=()=>{p.go(-1)},a=()=>{p.push({name:"exchange-register"})},_=()=>{p.push({name:"api-intro"})},b=()=>{p.push({name:"binance"})},t=c([{id:1,icon:"₿",title:"比特币 (Bitcoin)",description:'第一个也是最知名的加密货币，被称为"数字黄金"',details:["2009年由中本聪创造","总量限制为2100万枚","去中心化数字货币","价值储存和支付工具"]},{id:2,icon:"🏦",title:"区块链技术",description:"支撑加密货币的底层技术，是一个分布式账本系统",details:["去中心化记账系统","数据不可篡改","透明且可验证","无需中介机构"]},{id:3,icon:"💼",title:"数字钱包",description:"存储和管理加密货币的工具，分为热钱包和冷钱包",details:["私钥控制资产","热钱包便于交易","冷钱包更加安全","助记词是恢复关键"]}]),o=c([{term:"HODL",category:"投资策略",definition:"长期持有加密货币的投资策略，不进行频繁交易",example:"无论市场如何波动，坚持持有比特币不卖出"},{term:"市值",category:"市场指标",definition:"加密货币的总价值，等于流通供应量乘以当前价格",example:"比特币市值 = 流通的比特币数量 × 当前比特币价格"},{term:"波动性",category:"风险指标",definition:"价格变化的幅度和频率，加密货币通常具有高波动性",example:"比特币一天内价格可能上涨或下跌10%以上"},{term:"DeFi",category:"金融概念",definition:"去中心化金融，基于区块链的金融服务和产品",example:"通过智能合约进行借贷、交易和理财"}]),u=c([{id:1,title:"选择交易所",description:"选择一个可靠、安全的加密货币交易平台",tips:["选择有良好声誉的大型交易所","检查交易所的安全措施和保险","了解交易费用和支持的币种"]},{id:2,title:"完成身份验证",description:"根据监管要求完成KYC（了解你的客户）认证",tips:["准备身份证件和地址证明","确保信息真实准确","保护个人隐私信息安全"]},{id:3,title:"资金入金",description:"通过银行转账或其他方式向交易所充值",tips:["从小额开始，熟悉流程","确认到账时间和手续费","保留所有交易记录"]},{id:4,title:"开始交易",description:"选择交易对，下单买入或卖出加密货币",tips:["先学习基本的订单类型","设置合理的止损和止盈","不要投入超过承受能力的资金"]}]),k=c([{name:"现货交易",icon:"💰",risk:"低风险",riskLevel:"low",description:"直接买卖加密货币，拥有实际资产",pros:"风险相对较低，操作简单",cons:"收益有限，需要较多资金",suitable:"新手投资者，长期投资者"},{name:"合约交易",icon:"⚡",risk:"高风险",riskLevel:"high",description:"使用杠杆进行交易，可以放大收益和损失",pros:"资金利用率高，可做空获利",cons:"风险极高，可能爆仓",suitable:"有经验的交易者"}]),y=c([{id:1,title:"双重验证 (2FA)",icon:"fas fa-mobile-alt",importance:"必须",level:"critical",description:"为账户添加额外的安全层，防止未授权访问",steps:["下载Google Authenticator或类似应用","在交易所设置中启用2FA","备份恢复代码到安全地方","每次登录时输入动态验证码"]},{id:2,title:"强密码策略",icon:"fas fa-key",importance:"重要",level:"important",description:"使用复杂且唯一的密码保护账户安全",steps:["使用至少12位字符的密码","包含大小写字母、数字和符号","每个平台使用不同密码","定期更换密码"]}]),h=c([{id:1,title:"钓鱼网站诈骗",description:"虚假网站模仿正规交易所，窃取用户登录信息和资金",prevention:["始终通过官方渠道访问交易所","检查网站URL是否正确","注意SSL证书和安全标识","不点击可疑邮件中的链接"]},{id:2,title:"私钥泄露风险",description:"私钥一旦泄露，资产将面临被盗风险",prevention:["永远不要在网上分享私钥","使用硬件钱包存储大额资产","定期备份钱包文件","使用多重签名钱包"]}]),T=c([{id:1,icon:"📖",title:"区块链基础知识",description:"了解区块链技术原理和加密货币基础概念",difficulty:"入门级",level:"beginner"},{id:2,icon:"📊",title:"技术分析入门",description:"学习K线图、技术指标和图表分析方法",difficulty:"进阶级",level:"intermediate"},{id:3,icon:"🔒",title:"安全防护指南",description:"掌握钱包安全、私钥管理和风险防范",difficulty:"必修课",level:"essential"}]);return{activeTab:i,goBack:m,navigateToRegister:a,navigateToApiGuide:_,navigateToStrategies:b,basicConcepts:t,commonTerms:o,tradingSteps:u,tradingTypes:k,securityMeasures:y,riskWarnings:h,learningResources:T}}},S={class:"beginner-guide-view"},A={class:"header"},G={class:"header-content"},L={class:"main-content"},R={class:"guide-tabs"},V={key:0,class:"guide-content"},F={class:"guide-section"},N={class:"concept-cards"},D={class:"concept-icon"},I={class:"concept-info"},K={class:"concept-details"},M={class:"guide-section"},P={class:"terms-grid"},W={class:"term-header"},z={class:"term-category"},E={key:0,class:"term-example"},H={key:1,class:"guide-content"},O={class:"guide-section"},U={class:"trading-steps"},Y={class:"step-header"},j={class:"step-number"},q={class:"step-tips"},J={class:"guide-section"},Q={class:"trading-types"},X={class:"type-header"},Z={class:"type-icon"},$={class:"type-features"},ss={class:"feature-row"},is={class:"feature-value pros"},ts={class:"feature-row"},es={class:"feature-value cons"},ns={class:"feature-row"},ls={class:"feature-value"},as={key:2,class:"guide-content"},os={class:"guide-section"},ds={class:"security-measures"},rs={class:"measure-header"},cs={class:"measure-steps"},us={class:"step-num"},vs={class:"guide-section"},ps={class:"risk-warnings"},fs={class:"warning-header"},gs={class:"prevention-tips"},ms={class:"learning-resources"},_s={class:"resource-grid"},bs={class:"resource-icon"},ks={class:"next-steps"},ys={class:"next-step-cards"};function hs(p,i,m,a,_,b){return n(),l("div",S,[s("header",A,[s("div",G,[s("button",{class:"back-button",onClick:i[0]||(i[0]=(...t)=>a.goBack&&a.goBack(...t))},i[7]||(i[7]=[s("i",{class:"fas fa-arrow-left"},null,-1)])),i[8]||(i[8]=s("h1",null,"新手指南",-1))])]),s("main",L,[i[31]||(i[31]=B('<div class="intro-section" data-v-0d9809ef><div class="intro-card" data-v-0d9809ef><h2 data-v-0d9809ef>🧭 加密货币交易新手指南</h2><p data-v-0d9809ef>欢迎进入加密货币世界！本指南将帮助您了解基础概念、交易术语和风险管理，让您安全地开始数字资产投资之旅。</p><div class="key-features" data-v-0d9809ef><div class="feature-item" data-v-0d9809ef><i class="fas fa-graduation-cap" data-v-0d9809ef></i><span data-v-0d9809ef>基础知识</span></div><div class="feature-item" data-v-0d9809ef><i class="fas fa-shield-alt" data-v-0d9809ef></i><span data-v-0d9809ef>安全防护</span></div><div class="feature-item" data-v-0d9809ef><i class="fas fa-chart-line" data-v-0d9809ef></i><span data-v-0d9809ef>交易技巧</span></div></div></div></div>',1)),s("div",R,[s("button",{class:v(["tab-button",{active:a.activeTab==="basics"}]),onClick:i[1]||(i[1]=t=>a.activeTab="basics")},i[9]||(i[9]=[s("i",{class:"fas fa-book"},null,-1),f(" 基础概念 ")]),2),s("button",{class:v(["tab-button",{active:a.activeTab==="trading"}]),onClick:i[2]||(i[2]=t=>a.activeTab="trading")},i[10]||(i[10]=[s("i",{class:"fas fa-exchange-alt"},null,-1),f(" 交易入门 ")]),2),s("button",{class:v(["tab-button",{active:a.activeTab==="security"}]),onClick:i[3]||(i[3]=t=>a.activeTab="security")},i[11]||(i[11]=[s("i",{class:"fas fa-lock"},null,-1),f(" 安全指南 ")]),2)]),a.activeTab==="basics"?(n(),l("div",V,[s("div",F,[i[13]||(i[13]=s("h3",null,"💰 什么是加密货币？",-1)),s("div",N,[(n(!0),l(d,null,r(a.basicConcepts,t=>(n(),l("div",{class:"concept-card",key:t.id},[s("div",D,e(t.icon),1),s("div",I,[s("h4",null,e(t.title),1),s("p",null,e(t.description),1),s("div",K,[(n(!0),l(d,null,r(t.details,o=>(n(),l("div",{class:"detail-item",key:o},[i[12]||(i[12]=s("i",{class:"fas fa-check-circle"},null,-1)),s("span",null,e(o),1)]))),128))])])]))),128))])]),s("div",M,[i[15]||(i[15]=s("h3",null,"📊 常见术语解释",-1)),s("div",P,[(n(!0),l(d,null,r(a.commonTerms,t=>(n(),l("div",{class:"term-card",key:t.term},[s("div",W,[s("h4",null,e(t.term),1),s("span",z,e(t.category),1)]),s("p",null,e(t.definition),1),t.example?(n(),l("div",E,[i[14]||(i[14]=s("strong",null,"示例：",-1)),f(e(t.example),1)])):g("",!0)]))),128))])])])):g("",!0),a.activeTab==="trading"?(n(),l("div",H,[s("div",O,[i[17]||(i[17]=s("h3",null,"🚀 如何开始交易？",-1)),s("div",U,[(n(!0),l(d,null,r(a.tradingSteps,(t,o)=>(n(),l("div",{class:"step-card",key:t.id},[s("div",Y,[s("div",j,e(o+1),1),s("h4",null,e(t.title),1)]),s("p",null,e(t.description),1),s("div",q,[(n(!0),l(d,null,r(t.tips,u=>(n(),l("div",{class:"tip-item",key:u},[i[16]||(i[16]=s("i",{class:"fas fa-lightbulb"},null,-1)),s("span",null,e(u),1)]))),128))])]))),128))])]),s("div",J,[i[21]||(i[21]=s("h3",null,"📈 交易类型对比",-1)),s("div",Q,[(n(!0),l(d,null,r(a.tradingTypes,t=>(n(),l("div",{class:"type-card",key:t.name},[s("div",X,[s("div",Z,e(t.icon),1),s("h4",null,e(t.name),1),s("div",{class:v(["type-risk",t.riskLevel])},e(t.risk),3)]),s("p",null,e(t.description),1),s("div",$,[s("div",ss,[i[18]||(i[18]=s("span",{class:"feature-label"},"优点：",-1)),s("span",is,e(t.pros),1)]),s("div",ts,[i[19]||(i[19]=s("span",{class:"feature-label"},"缺点：",-1)),s("span",es,e(t.cons),1)]),s("div",ns,[i[20]||(i[20]=s("span",{class:"feature-label"},"适合人群：",-1)),s("span",ls,e(t.suitable),1)])])]))),128))])])])):g("",!0),a.activeTab==="security"?(n(),l("div",as,[s("div",os,[i[22]||(i[22]=s("h3",null,"🔒 安全防护措施",-1)),s("div",ds,[(n(!0),l(d,null,r(a.securityMeasures,t=>(n(),l("div",{class:"measure-card",key:t.id},[s("div",rs,[s("i",{class:v([t.icon,"measure-icon"])},null,2),s("h4",null,e(t.title),1),s("div",{class:v(["importance",t.level])},e(t.importance),3)]),s("p",null,e(t.description),1),s("div",cs,[(n(!0),l(d,null,r(t.steps,(o,u)=>(n(),l("div",{class:"measure-step",key:u},[s("span",us,e(u+1)+".",1),s("span",null,e(o),1)]))),128))])]))),128))])]),s("div",vs,[i[25]||(i[25]=s("h3",null,"⚠️ 常见风险警示",-1)),s("div",ps,[(n(!0),l(d,null,r(a.riskWarnings,t=>(n(),l("div",{class:"warning-card",key:t.id},[s("div",fs,[i[23]||(i[23]=s("i",{class:"fas fa-exclamation-triangle"},null,-1)),s("h4",null,e(t.title),1)]),s("p",null,e(t.description),1),s("div",gs,[i[24]||(i[24]=s("h5",null,"预防措施：",-1)),s("ul",null,[(n(!0),l(d,null,r(t.prevention,o=>(n(),l("li",{key:o},e(o),1))),128))])])]))),128))])])])):g("",!0),s("div",ms,[i[26]||(i[26]=s("h3",null,"📚 推荐学习资源",-1)),s("div",_s,[(n(!0),l(d,null,r(a.learningResources,t=>(n(),l("div",{class:"resource-card",key:t.id},[s("div",bs,e(t.icon),1),s("h4",null,e(t.title),1),s("p",null,e(t.description),1),s("div",{class:v(["resource-level",t.level])},e(t.difficulty),3)]))),128))])]),s("div",ks,[i[30]||(i[30]=s("h3",null,"🎯 开始您的交易之旅",-1)),s("div",ys,[s("div",{class:"next-step-card",onClick:i[4]||(i[4]=(...t)=>a.navigateToRegister&&a.navigateToRegister(...t))},i[27]||(i[27]=[s("i",{class:"fas fa-user-plus"},null,-1),s("h4",null,"注册交易所",-1),s("p",null,"选择可靠的交易平台开始",-1)])),s("div",{class:"next-step-card",onClick:i[5]||(i[5]=(...t)=>a.navigateToApiGuide&&a.navigateToApiGuide(...t))},i[28]||(i[28]=[s("i",{class:"fas fa-key"},null,-1),s("h4",null,"学习API配置",-1),s("p",null,"了解如何安全配置API",-1)])),s("div",{class:"next-step-card",onClick:i[6]||(i[6]=(...t)=>a.navigateToStrategies&&a.navigateToStrategies(...t))},i[29]||(i[29]=[s("i",{class:"fas fa-robot"},null,-1),s("h4",null,"体验自动交易",-1),s("p",null,"从简单策略开始尝试",-1)]))])])])])}const xs=x(w,[["render",hs],["__scopeId","data-v-0d9809ef"]]);export{xs as default};
