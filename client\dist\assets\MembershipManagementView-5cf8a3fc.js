import{_ as w,u as x,r as a,o as U,c as d,d as u,e as s,F as q,p as A,g,t as l,w as C,f as r,v as n,B as E,H as _,i as B,n as S}from"./index-a2cd3c28.js";const P={name:"MembershipManagementView",setup(){const y=x(),e=a([]),p=a(!1),t=a(""),b=a(!1),f=a(null),m=a(!1),o=a({name:"",price:0,originalPrice:0,features:{strategies:{contracts:0,spot:0},service:{support:"",response:""},benefits:{discount:"",trial:"",referral:""}},order:0,isActive:!0}),F=()=>{y.go(-1)},c=async()=>{try{p.value=!0,t.value="";const i=localStorage.getItem("token"),v=await _.get("/api/membership/admin/config",{headers:{Authorization:`Bearer ${i}`}});v.data.success?e.value=v.data.data:t.value=v.data.error||"获取配置失败"}catch(i){console.error("获取配置错误:",i),t.value="网络错误，请检查连接"}finally{p.value=!1}},M=i=>{f.value=i,o.value={name:i.name,price:i.price,originalPrice:i.originalPrice,features:JSON.parse(JSON.stringify(i.features)),order:i.order,isActive:i.isActive},b.value=!0},k=()=>{b.value=!1,f.value=null},V=async()=>{try{m.value=!0;const i=localStorage.getItem("token"),v=await _.put(`/api/membership/admin/config/${f.value.level}`,o.value,{headers:{Authorization:`Bearer ${i}`}});v.data.success?(alert("配置更新成功！"),await c(),k()):t.value=v.data.error||"保存失败"}catch(i){console.error("保存配置错误:",i),t.value="保存失败，请重试"}finally{m.value=!1}};return U(()=>{c()}),{membershipConfigs:e,loading:p,error:t,showEditModal:b,editingConfig:f,editForm:o,saving:m,goBack:F,loadConfigs:c,editConfig:M,closeEditModal:k,saveConfig:V}}},N={class:"membership-management-view"},z={class:"header"},I={class:"header-content"},D={class:"main-content"},J={key:0,class:"config-list"},O={class:"config-header"},T={class:"config-actions"},H=["onClick"],L={class:"config-details"},R={class:"price-info"},j={class:"current-price"},G={class:"original-price"},K={class:"features-info"},Q={class:"feature-item"},W={class:"value"},X={class:"feature-item"},Y={class:"value"},Z={class:"feature-item"},h={class:"value"},$={class:"status-info"},ee={class:"order"},se={key:1,class:"loading-section"},te={key:2,class:"error-section"},oe={class:"modal-header"},ie={class:"modal-body"},le={class:"form-group"},re={class:"form-row"},ne={class:"form-group"},ae={class:"form-group"},de={class:"form-row"},ue={class:"form-group"},me={class:"form-group"},ve={class:"form-group"},fe={class:"form-group"},pe={class:"form-group"},be={class:"form-group"},ge={class:"form-group"},ce={class:"form-row"},ye={class:"form-group"},Fe={class:"form-group"},ke={class:"form-actions"},Ce=["disabled"];function _e(y,e,p,t,b,f){var m;return d(),u("div",N,[s("header",z,[s("div",I,[s("button",{class:"back-button",onClick:e[0]||(e[0]=(...o)=>t.goBack&&t.goBack(...o))},e[19]||(e[19]=[s("i",{class:"fas fa-arrow-left"},null,-1)])),e[20]||(e[20]=s("h1",null,"会员配置管理",-1))])]),s("main",D,[t.membershipConfigs.length>0?(d(),u("div",J,[(d(!0),u(q,null,A(t.membershipConfigs,o=>(d(),u("div",{class:"config-card",key:o.level},[s("div",O,[s("h3",null,l(o.name),1),s("div",T,[s("button",{onClick:F=>t.editConfig(o),class:"edit-button"},e[21]||(e[21]=[s("i",{class:"fas fa-edit"},null,-1),B(" 编辑 ")]),8,H)])]),s("div",L,[s("div",R,[s("div",j,"当前价格: ¥"+l(o.price),1),s("div",G,"原价: ¥"+l(o.originalPrice),1)]),s("div",K,[s("div",Q,[e[22]||(e[22]=s("span",{class:"label"},"策略权限:",-1)),s("span",W,l(o.features.strategies.contracts)+"合约 + "+l(o.features.strategies.spot)+"现货",1)]),s("div",X,[e[23]||(e[23]=s("span",{class:"label"},"服务支持:",-1)),s("span",Y,l(o.features.service.support),1)]),s("div",Z,[e[24]||(e[24]=s("span",{class:"label"},"专属权益:",-1)),s("span",h,l(o.features.benefits.discount),1)])]),s("div",$,[s("span",{class:S(["status",{active:o.isActive,inactive:!o.isActive}])},l(o.isActive?"启用":"禁用"),3),s("span",ee,"排序: "+l(o.order),1)])])]))),128))])):g("",!0),t.loading?(d(),u("div",se,e[25]||(e[25]=[s("i",{class:"fas fa-spinner fa-spin"},null,-1),s("p",null,"正在加载配置...",-1)]))):g("",!0),t.error?(d(),u("div",te,[e[26]||(e[26]=s("i",{class:"fas fa-exclamation-triangle"},null,-1)),s("p",null,l(t.error),1),s("button",{onClick:e[1]||(e[1]=(...o)=>t.loadConfigs&&t.loadConfigs(...o)),class:"retry-button"}," 重试 ")])):g("",!0)]),t.showEditModal?(d(),u("div",{key:0,class:"modal-overlay",onClick:e[18]||(e[18]=(...o)=>t.closeEditModal&&t.closeEditModal(...o))},[s("div",{class:"modal-content",onClick:e[17]||(e[17]=C(()=>{},["stop"]))},[s("div",oe,[s("h3",null,"编辑 "+l((m=t.editingConfig)==null?void 0:m.name),1),s("button",{onClick:e[2]||(e[2]=(...o)=>t.closeEditModal&&t.closeEditModal(...o)),class:"close-button"},e[27]||(e[27]=[s("i",{class:"fas fa-times"},null,-1)]))]),s("div",ie,[s("form",{onSubmit:e[16]||(e[16]=C((...o)=>t.saveConfig&&t.saveConfig(...o),["prevent"]))},[s("div",le,[e[28]||(e[28]=s("label",null,"会员名称",-1)),r(s("input",{"onUpdate:modelValue":e[3]||(e[3]=o=>t.editForm.name=o),type:"text",required:""},null,512),[[n,t.editForm.name]])]),s("div",re,[s("div",ne,[e[29]||(e[29]=s("label",null,"当前价格",-1)),r(s("input",{"onUpdate:modelValue":e[4]||(e[4]=o=>t.editForm.price=o),type:"number",step:"0.01",required:""},null,512),[[n,t.editForm.price,void 0,{number:!0}]])]),s("div",ae,[e[30]||(e[30]=s("label",null,"原价",-1)),r(s("input",{"onUpdate:modelValue":e[5]||(e[5]=o=>t.editForm.originalPrice=o),type:"number",step:"0.01",required:""},null,512),[[n,t.editForm.originalPrice,void 0,{number:!0}]])])]),s("div",de,[s("div",ue,[e[31]||(e[31]=s("label",null,"合约策略数",-1)),r(s("input",{"onUpdate:modelValue":e[6]||(e[6]=o=>t.editForm.features.strategies.contracts=o),type:"number",required:""},null,512),[[n,t.editForm.features.strategies.contracts,void 0,{number:!0}]])]),s("div",me,[e[32]||(e[32]=s("label",null,"现货策略数",-1)),r(s("input",{"onUpdate:modelValue":e[7]||(e[7]=o=>t.editForm.features.strategies.spot=o),type:"number",required:""},null,512),[[n,t.editForm.features.strategies.spot,void 0,{number:!0}]])])]),s("div",ve,[e[33]||(e[33]=s("label",null,"服务支持",-1)),r(s("input",{"onUpdate:modelValue":e[8]||(e[8]=o=>t.editForm.features.service.support=o),type:"text",required:""},null,512),[[n,t.editForm.features.service.support]])]),s("div",fe,[e[34]||(e[34]=s("label",null,"响应时间",-1)),r(s("input",{"onUpdate:modelValue":e[9]||(e[9]=o=>t.editForm.features.service.response=o),type:"text",required:""},null,512),[[n,t.editForm.features.service.response]])]),s("div",pe,[e[35]||(e[35]=s("label",null,"专属权益",-1)),r(s("input",{"onUpdate:modelValue":e[10]||(e[10]=o=>t.editForm.features.benefits.discount=o),type:"text",required:""},null,512),[[n,t.editForm.features.benefits.discount]])]),s("div",be,[e[36]||(e[36]=s("label",null,"体验权益",-1)),r(s("input",{"onUpdate:modelValue":e[11]||(e[11]=o=>t.editForm.features.benefits.trial=o),type:"text",required:""},null,512),[[n,t.editForm.features.benefits.trial]])]),s("div",ge,[e[37]||(e[37]=s("label",null,"推荐奖励",-1)),r(s("input",{"onUpdate:modelValue":e[12]||(e[12]=o=>t.editForm.features.benefits.referral=o),type:"text"},null,512),[[n,t.editForm.features.benefits.referral]])]),s("div",ce,[s("div",ye,[e[38]||(e[38]=s("label",null,"排序",-1)),r(s("input",{"onUpdate:modelValue":e[13]||(e[13]=o=>t.editForm.order=o),type:"number",required:""},null,512),[[n,t.editForm.order,void 0,{number:!0}]])]),s("div",Fe,[e[40]||(e[40]=s("label",null,"状态",-1)),r(s("select",{"onUpdate:modelValue":e[14]||(e[14]=o=>t.editForm.isActive=o)},e[39]||(e[39]=[s("option",{value:!0},"启用",-1),s("option",{value:!1},"禁用",-1)]),512),[[E,t.editForm.isActive]])])]),s("div",ke,[s("button",{type:"button",onClick:e[15]||(e[15]=(...o)=>t.closeEditModal&&t.closeEditModal(...o)),class:"cancel-button"}," 取消 "),s("button",{type:"submit",class:"save-button",disabled:t.saving},l(t.saving?"保存中...":"保存"),9,Ce)])],32)])])])):g("",!0)])}const Ve=w(P,[["render",_e],["__scopeId","data-v-2bd816a7"]]);export{Ve as default};
