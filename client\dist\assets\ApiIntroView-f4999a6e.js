import{_ as r,u as f,r as g,c as v,d as e,e as s,C as n,n as b,i as c,g as l}from"./index-a2cd3c28.js";const m={name:"ApiIntroView",setup(){const i=f(),a=g("binance");return{activeTab:a,goBack:()=>{i.go(-1)},navigateToApiSettings:()=>{i.push({name:"user"})},navigateToStrategies:()=>{a.value==="binance"?i.push({name:"binance"}):i.push({name:"okx"})}}}},h={class:"api-intro-view"},u={class:"header"},A={class:"header-content"},I={class:"main-content"},P={class:"exchange-tabs"},k={key:0,class:"tutorial-content"},y={key:1,class:"tutorial-content"},x={class:"next-steps"},T={class:"next-step-cards"};function q(i,a,o,t,p,w){return v(),e("div",h,[s("header",u,[s("div",A,[s("button",{class:"back-button",onClick:a[0]||(a[0]=(...d)=>t.goBack&&t.goBack(...d))},a[5]||(a[5]=[s("i",{class:"fas fa-arrow-left"},null,-1)])),a[6]||(a[6]=s("h1",null,"API介绍",-1))])]),s("main",I,[a[14]||(a[14]=n('<div class="intro-section" data-v-a5141a3b><div class="intro-card" data-v-a5141a3b><h2 data-v-a5141a3b>🔑 什么是API密钥？</h2><p data-v-a5141a3b>API密钥是您与交易所进行程序化交易的身份凭证。通过API密钥，AACoin可以代表您在交易所执行买卖操作，实现自动化交易策略。</p><div class="key-features" data-v-a5141a3b><div class="feature-item" data-v-a5141a3b><i class="fas fa-shield-alt" data-v-a5141a3b></i><span data-v-a5141a3b>安全可控</span></div><div class="feature-item" data-v-a5141a3b><i class="fas fa-robot" data-v-a5141a3b></i><span data-v-a5141a3b>自动化交易</span></div><div class="feature-item" data-v-a5141a3b><i class="fas fa-clock" data-v-a5141a3b></i><span data-v-a5141a3b>24/7运行</span></div></div></div></div>',1)),s("div",P,[s("button",{class:b(["tab-button",{active:t.activeTab==="binance"}]),onClick:a[1]||(a[1]=d=>t.activeTab="binance")},a[7]||(a[7]=[s("i",{class:"fas fa-coins"},null,-1),c(" 币安 API ")]),2),s("button",{class:b(["tab-button",{active:t.activeTab==="okx"}]),onClick:a[2]||(a[2]=d=>t.activeTab="okx")},a[8]||(a[8]=[s("i",{class:"fas fa-exchange-alt"},null,-1),c(" OKX API ")]),2)]),t.activeTab==="binance"?(v(),e("div",k,a[9]||(a[9]=[n('<div class="api-guide-card" data-v-a5141a3b><div class="guide-header" data-v-a5141a3b><img src="https://bin.bnbstatic.com/static/images/common/favicon.ico" alt="Binance" class="exchange-logo" data-v-a5141a3b><div data-v-a5141a3b><h3 data-v-a5141a3b>币安 API 密钥生成</h3><p data-v-a5141a3b>获取币安交易所的API访问权限</p></div></div><div class="step-by-step" data-v-a5141a3b><h4 data-v-a5141a3b>📋 详细步骤指南</h4><div class="step-item" data-v-a5141a3b><div class="step-number" data-v-a5141a3b>1</div><div class="step-content" data-v-a5141a3b><h5 data-v-a5141a3b>登录币安账户</h5><p data-v-a5141a3b>访问 <a href="https://www.binance.com" target="_blank" data-v-a5141a3b>币安官网</a> 并登录您的账户</p><div class="tip-box" data-v-a5141a3b><i class="fas fa-lightbulb" data-v-a5141a3b></i><span data-v-a5141a3b>确保您已完成身份认证(KYC)和双重验证设置</span></div></div></div><div class="step-item" data-v-a5141a3b><div class="step-number" data-v-a5141a3b>2</div><div class="step-content" data-v-a5141a3b><h5 data-v-a5141a3b>进入API管理页面</h5><p data-v-a5141a3b>点击右上角头像 → 选择&quot;API管理&quot; → 点击&quot;创建API&quot;</p><div class="path-indicator" data-v-a5141a3b><span data-v-a5141a3b>用户中心</span><i class="fas fa-chevron-right" data-v-a5141a3b></i><span data-v-a5141a3b>API管理</span><i class="fas fa-chevron-right" data-v-a5141a3b></i><span data-v-a5141a3b>创建API</span></div></div></div><div class="step-item" data-v-a5141a3b><div class="step-number" data-v-a5141a3b>3</div><div class="step-content" data-v-a5141a3b><h5 data-v-a5141a3b>创建API密钥</h5><p data-v-a5141a3b>填写API标签名称（如：AACoin交易）并完成安全验证</p><div class="code-block" data-v-a5141a3b><div class="code-header" data-v-a5141a3b>建议的API标签名称</div><code data-v-a5141a3b>AACoin-Trading-Bot</code></div></div></div><div class="step-item" data-v-a5141a3b><div class="step-number" data-v-a5141a3b>4</div><div class="step-content" data-v-a5141a3b><h5 data-v-a5141a3b>配置API权限</h5><p data-v-a5141a3b>选择以下权限以确保策略正常运行：</p><div class="permission-list" data-v-a5141a3b><div class="permission-item required" data-v-a5141a3b><i class="fas fa-check-circle" data-v-a5141a3b></i><span data-v-a5141a3b>现货和杠杆交易</span><span class="required-tag" data-v-a5141a3b>必需</span></div><div class="permission-item required" data-v-a5141a3b><i class="fas fa-check-circle" data-v-a5141a3b></i><span data-v-a5141a3b>合约交易</span><span class="required-tag" data-v-a5141a3b>必需</span></div><div class="permission-item optional" data-v-a5141a3b><i class="fas fa-info-circle" data-v-a5141a3b></i><span data-v-a5141a3b>读取</span><span class="optional-tag" data-v-a5141a3b>默认</span></div></div></div></div><div class="step-item" data-v-a5141a3b><div class="step-number" data-v-a5141a3b>5</div><div class="step-content" data-v-a5141a3b><h5 data-v-a5141a3b>获取密钥信息</h5><p data-v-a5141a3b>创建成功后，您将获得以下信息：</p><div class="key-info" data-v-a5141a3b><div class="key-item" data-v-a5141a3b><strong data-v-a5141a3b>API Key：</strong><span class="key-example" data-v-a5141a3b>以字母数字组合开头的64位字符串</span></div><div class="key-item" data-v-a5141a3b><strong data-v-a5141a3b>Secret Key：</strong><span class="key-example" data-v-a5141a3b>用于签名验证的私钥（仅显示一次）</span></div></div><div class="warning-box" data-v-a5141a3b><i class="fas fa-exclamation-triangle" data-v-a5141a3b></i><p data-v-a5141a3b><strong data-v-a5141a3b>重要：</strong>Secret Key只显示一次，请立即复制并安全保存！</p></div></div></div></div><div class="security-settings" data-v-a5141a3b><h4 data-v-a5141a3b>🔒 安全设置建议</h4><div class="security-grid" data-v-a5141a3b><div class="security-item" data-v-a5141a3b><i class="fas fa-globe" data-v-a5141a3b></i><div data-v-a5141a3b><h5 data-v-a5141a3b>IP白名单</h5><p data-v-a5141a3b>限制API只能从特定IP地址访问</p></div></div><div class="security-item" data-v-a5141a3b><i class="fas fa-clock" data-v-a5141a3b></i><div data-v-a5141a3b><h5 data-v-a5141a3b>定期更换</h5><p data-v-a5141a3b>建议每3-6个月更换一次API密钥</p></div></div><div class="security-item" data-v-a5141a3b><i class="fas fa-eye-slash" data-v-a5141a3b></i><div data-v-a5141a3b><h5 data-v-a5141a3b>保密存储</h5><p data-v-a5141a3b>不要在公共场所或聊天中分享密钥</p></div></div></div></div></div>',1)]))):l("",!0),t.activeTab==="okx"?(v(),e("div",y,a[10]||(a[10]=[n('<div class="api-guide-card" data-v-a5141a3b><div class="guide-header" data-v-a5141a3b><img src="https://static.okx.com/cdn/assets/imgs/MjAyMTA0/6B7B1F8FD9B77B6F3D3F8F8F8F8F8F8F.png" alt="OKX" class="exchange-logo" data-v-a5141a3b><div data-v-a5141a3b><h3 data-v-a5141a3b>OKX API 密钥生成</h3><p data-v-a5141a3b>获取OKX交易所的API访问权限</p></div></div><div class="step-by-step" data-v-a5141a3b><h4 data-v-a5141a3b>📋 详细步骤指南</h4><div class="step-item" data-v-a5141a3b><div class="step-number" data-v-a5141a3b>1</div><div class="step-content" data-v-a5141a3b><h5 data-v-a5141a3b>登录OKX账户</h5><p data-v-a5141a3b>访问 <a href="https://www.okx.com" target="_blank" data-v-a5141a3b>OKX官网</a> 并登录您的账户</p><div class="tip-box" data-v-a5141a3b><i class="fas fa-lightbulb" data-v-a5141a3b></i><span data-v-a5141a3b>确保您已完成身份认证和安全设置</span></div></div></div><div class="step-item" data-v-a5141a3b><div class="step-number" data-v-a5141a3b>2</div><div class="step-content" data-v-a5141a3b><h5 data-v-a5141a3b>进入API设置页面</h5><p data-v-a5141a3b>点击右上角头像 → 选择&quot;API&quot; → 点击&quot;创建V5 API Key&quot;</p><div class="path-indicator" data-v-a5141a3b><span data-v-a5141a3b>个人中心</span><i class="fas fa-chevron-right" data-v-a5141a3b></i><span data-v-a5141a3b>API</span><i class="fas fa-chevron-right" data-v-a5141a3b></i><span data-v-a5141a3b>创建V5 API Key</span></div></div></div><div class="step-item" data-v-a5141a3b><div class="step-number" data-v-a5141a3b>3</div><div class="step-content" data-v-a5141a3b><h5 data-v-a5141a3b>设置API基本信息</h5><p data-v-a5141a3b>填写API名称并设置Passphrase（API密码）</p><div class="code-block" data-v-a5141a3b><div class="code-header" data-v-a5141a3b>建议的API名称</div><code data-v-a5141a3b>AACoin-Auto-Trading</code></div><div class="tip-box" data-v-a5141a3b><i class="fas fa-key" data-v-a5141a3b></i><span data-v-a5141a3b>Passphrase请设置为8-32位字符，包含字母和数字</span></div></div></div><div class="step-item" data-v-a5141a3b><div class="step-number" data-v-a5141a3b>4</div><div class="step-content" data-v-a5141a3b><h5 data-v-a5141a3b>配置API权限</h5><p data-v-a5141a3b>选择以下权限以确保策略正常运行：</p><div class="permission-list" data-v-a5141a3b><div class="permission-item required" data-v-a5141a3b><i class="fas fa-check-circle" data-v-a5141a3b></i><span data-v-a5141a3b>读取</span><span class="required-tag" data-v-a5141a3b>必需</span></div><div class="permission-item required" data-v-a5141a3b><i class="fas fa-check-circle" data-v-a5141a3b></i><span data-v-a5141a3b>交易</span><span class="required-tag" data-v-a5141a3b>必需</span></div><div class="permission-item optional" data-v-a5141a3b><i class="fas fa-info-circle" data-v-a5141a3b></i><span data-v-a5141a3b>提币</span><span class="optional-tag" data-v-a5141a3b>可选</span></div></div></div></div><div class="step-item" data-v-a5141a3b><div class="step-number" data-v-a5141a3b>5</div><div class="step-content" data-v-a5141a3b><h5 data-v-a5141a3b>获取密钥信息</h5><p data-v-a5141a3b>创建成功后，您将获得以下三个重要信息：</p><div class="key-info" data-v-a5141a3b><div class="key-item" data-v-a5141a3b><strong data-v-a5141a3b>API Key：</strong><span class="key-example" data-v-a5141a3b>公开的API访问密钥</span></div><div class="key-item" data-v-a5141a3b><strong data-v-a5141a3b>Secret Key：</strong><span class="key-example" data-v-a5141a3b>私密的签名密钥（仅显示一次）</span></div><div class="key-item" data-v-a5141a3b><strong data-v-a5141a3b>Passphrase：</strong><span class="key-example" data-v-a5141a3b>您设置的API密码</span></div></div><div class="warning-box" data-v-a5141a3b><i class="fas fa-exclamation-triangle" data-v-a5141a3b></i><p data-v-a5141a3b><strong data-v-a5141a3b>重要：</strong>Secret Key只显示一次，请立即复制保存。Passphrase需要您记住！</p></div></div></div></div><div class="security-settings" data-v-a5141a3b><h4 data-v-a5141a3b>🔒 安全设置建议</h4><div class="security-grid" data-v-a5141a3b><div class="security-item" data-v-a5141a3b><i class="fas fa-network-wired" data-v-a5141a3b></i><div data-v-a5141a3b><h5 data-v-a5141a3b>IP绑定</h5><p data-v-a5141a3b>绑定固定IP地址提高安全性</p></div></div><div class="security-item" data-v-a5141a3b><i class="fas fa-shield-alt" data-v-a5141a3b></i><div data-v-a5141a3b><h5 data-v-a5141a3b>权限最小化</h5><p data-v-a5141a3b>只开启必要的API权限</p></div></div><div class="security-item" data-v-a5141a3b><i class="fas fa-history" data-v-a5141a3b></i><div data-v-a5141a3b><h5 data-v-a5141a3b>定期检查</h5><p data-v-a5141a3b>定期查看API使用记录</p></div></div></div></div></div>',1)]))):l("",!0),s("div",x,[a[13]||(a[13]=s("h3",null,"🚀 获取API密钥后的下一步",-1)),s("div",T,[s("div",{class:"next-step-card",onClick:a[3]||(a[3]=(...d)=>t.navigateToApiSettings&&t.navigateToApiSettings(...d))},a[11]||(a[11]=[s("i",{class:"fas fa-cog"},null,-1),s("h4",null,"配置API密钥",-1),s("p",null,"在AACoin中添加您刚获取的API密钥",-1)])),s("div",{class:"next-step-card",onClick:a[4]||(a[4]=(...d)=>t.navigateToStrategies&&t.navigateToStrategies(...d))},a[12]||(a[12]=[s("i",{class:"fas fa-play-circle"},null,-1),s("h4",null,"启动交易策略",-1),s("p",null,"选择并启动适合您的自动化交易策略",-1)]))])])])])}const C=r(m,[["render",q],["__scopeId","data-v-a5141a3b"]]);export{C as default};
