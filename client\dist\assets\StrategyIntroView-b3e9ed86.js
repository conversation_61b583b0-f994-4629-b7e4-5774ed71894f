import{_ as h,u as D,r as u,c as e,d as l,e as s,C as k,n as v,i as y,F as d,p as c,g as p,t as a}from"./index-a2cd3c28.js";const A={name:"StrategyIntroView",setup(){const f=D(),t=u("basic"),m=()=>{f.go(-1)},o=()=>{f.push({name:"user"})},g=()=>{f.push({name:"binance"})},_=u([{id:1,name:"K线图 (Candlestick)",icon:"📊",description:"显示开盘价、收盘价、最高价、最低价的图表形式",difficulty:"入门",difficultyLevel:"low",formula:"每根K线包含：开盘价(O)、最高价(H)、最低价(L)、收盘价(C)",formulaDesc:"K线实体表示开盘价和收盘价之间的价格区间，影线表示最高价和最低价",keyPoints:["阳线(绿色)：收盘价高于开盘价，表示上涨","阴线(红色)：收盘价低于开盘价，表示下跌","上影线：最高价与实体上端的距离，表示上方压力","下影线：最低价与实体下端的距离，表示下方支撑","K线组合形态可以预测价格走势"],applications:["单根K线分析：大阳线表示强势上涨，大阴线表示强势下跌","多根K线组合：如早晨之星、黄昏之星等反转形态","结合成交量分析：放量上涨更可靠，缩量下跌可能反弹","不同时间周期：日K线看趋势，小时K线看入场点"]},{id:2,name:"成交量 (Volume)",icon:"📈",description:"某一时间段内的交易数量，反映市场活跃度和资金流向",difficulty:"入门",difficultyLevel:"low",formula:"Volume = 买入量 + 卖出量",formulaDesc:"成交量是买卖双方达成交易的总数量，通常用柱状图显示",keyPoints:["放量：成交量明显增加，表示市场关注度高","缩量：成交量减少，表示市场观望情绪浓厚","量价配合：价涨量增为健康上涨，价跌量增需谨慎","异常放量：可能预示重要转折点的到来","成交量领先于价格变化"],applications:["确认趋势：上涨趋势中放量上涨，下跌趋势中放量下跌","识别反转：价格新高但成交量萎缩，可能见顶","突破确认：重要阻力位突破需要成交量配合","底部确认：恐慌性抛售后的放量反弹"]},{id:3,name:"支撑位与阻力位",icon:"📏",description:"价格难以跌破的水平线(支撑)和难以突破的水平线(阻力)",difficulty:"入门",difficultyLevel:"low",formula:"通过历史价格的高点和低点连线确定",formulaDesc:"支撑位是价格下跌时的买盘集中区域，阻力位是价格上涨时的卖盘集中区域",keyPoints:["心理价位：整数价位往往成为重要的支撑或阻力","历史高低点：前期重要高点和低点具有参考意义","成交密集区：大量交易发生的价格区间","角色转换：突破阻力位后可能变成支撑位","多次测试：测试次数越多，支撑阻力越强"],applications:["买入时机：在支撑位附近买入，风险相对较小","卖出时机：在阻力位附近卖出，获利了结","止损设置：跌破支撑位或突破阻力位失败时止损","目标价位：以下一个阻力位作为目标价位"]}]),i=u([{id:1,name:"移动平均线 (MA)",icon:"📈",description:"一定时期内价格的平均值，用于识别趋势方向",difficulty:"入门",difficultyLevel:"low",formula:"MA(n) = (P1 + P2 + ... + Pn) / n",formulaDesc:"n为周期数，P为各期价格。常用周期有5、10、20、60、120、250日",keyPoints:["金叉：短期MA上穿长期MA，看涨信号","死叉：短期MA下穿长期MA，看跌信号","价格在MA上方为多头市场，下方为空头市场","MA斜率反映趋势强度，越陡峭趋势越强","多条MA形成多头排列或空头排列"],applications:["趋势判断：MA20上穿MA60形成金叉，确认上涨趋势","支撑阻力：重要MA线常成为动态支撑或阻力","入场时机：价格回调至MA线附近是较好的入场点","止损设置：跌破重要MA线可作为止损信号"]},{id:2,name:"MACD指标",icon:"🌊",description:"移动平均收敛发散指标，用于判断趋势变化和买卖时机",difficulty:"进阶",difficultyLevel:"medium",formula:"MACD = EMA12 - EMA26, Signal = EMA9(MACD), Histogram = MACD - Signal",formulaDesc:"DIF线(快线)、DEA线(慢线)和MACD柱状图组成，常用参数为(12,26,9)",keyPoints:["MACD金叉：DIF上穿DEA，买入信号","MACD死叉：DIF下穿DEA，卖出信号","零轴上方为多头市场，下方为空头市场","柱状图反映多空力量强弱变化","顶背离和底背离预示趋势反转"],applications:["趋势确认：MACD在零轴上方金叉，确认上涨趋势","背离分析：价格创新高但MACD不创新高，可能见顶","入场时机：MACD金叉且柱状图由负转正","出场时机：MACD死叉或出现顶背离"]},{id:3,name:"布林带 (Bollinger Bands)",icon:"📊",description:"由移动平均线和标准差构成的通道，用于判断价格波动范围",difficulty:"进阶",difficultyLevel:"medium",formula:"中轨=MA20, 上轨=MA20+2×标准差, 下轨=MA20-2×标准差",formulaDesc:"通常使用20日移动平均线和2倍标准差，形成价格波动的动态通道",keyPoints:["价格在上下轨之间波动，超出轨道为异常","布林带收窄预示波动率降低，可能有大行情","布林带扩张表示波动率增加，趋势可能延续","价格触及上轨为超买，触及下轨为超卖","中轨作为重要的支撑阻力线"],applications:["震荡交易：价格在上下轨之间来回波动时的区间交易","突破交易：价格突破上轨或下轨后的趋势跟踪","超买超卖：价格触及轨道边缘时的反向交易机会","波动率分析：布林带宽度变化预测市场波动"]}]),n=u([{id:1,name:"RSI相对强弱指标",icon:"⚡",description:"衡量价格变动速度和幅度的动量震荡指标",difficulty:"入门",difficultyLevel:"low",formula:"RSI = 100 - (100 / (1 + RS)), RS = 平均上涨幅度 / 平均下跌幅度",formulaDesc:"通常使用14日周期，RSI值在0-100之间波动，50为中性线",keyPoints:["RSI > 70为超买区域，可能面临回调压力","RSI < 30为超卖区域，可能出现反弹机会","RSI背离：价格与RSI走势相反，预示反转","RSI在50上方表示多头强势，下方表示空头强势","极端值(>80或<20)出现频率较低但信号更强"],applications:["超买超卖：RSI>70时减仓，RSI<30时加仓","背离交易：价格新高但RSI不创新高，考虑做空","趋势确认：RSI突破50线确认趋势方向","入场时机：RSI从超卖区域回升时买入"]},{id:2,name:"KDJ随机指标",icon:"🎯",description:"结合价格位置和动量的随机震荡指标",difficulty:"进阶",difficultyLevel:"medium",formula:"K = (C-L9)/(H9-L9)×100, D = MA3(K), J = 3K - 2D",formulaDesc:"C为收盘价，L9为9日最低价，H9为9日最高价，常用参数(9,3,3)",keyPoints:["K线反映价格在近期区间中的相对位置","D线是K线的平滑移动平均线","J线是K、D线的加权平均，反应更敏感","KDJ>80为超买，KDJ<20为超卖","K线上穿D线为金叉买入信号"],applications:["金叉死叉：K线上穿D线买入，下穿卖出","超买超卖：KDJ在20以下买入，80以上卖出","背离分析：价格与KDJ走势背离预示反转","钝化现象：强势行情中KDJ可能长期超买或超卖"]},{id:3,name:"Williams %R威廉指标",icon:"📉",description:"衡量价格在一定时期内相对位置的震荡指标",difficulty:"进阶",difficultyLevel:"medium",formula:"%R = (Hn - C) / (Hn - Ln) × (-100)",formulaDesc:"Hn为n日最高价，Ln为n日最低价，C为收盘价，常用周期为14日",keyPoints:["%R值在-100到0之间波动，数值越小表示越接近高点","%R > -20为超买区域，可能面临下跌","%R < -80为超卖区域，可能出现反弹","与RSI相似但更敏感，适合短线交易","背离现象同样适用于威廉指标"],applications:["短线交易：%R从超卖区域(-80以下)回升时买入","超买警示：%R进入-20以上区域时谨慎追高","背离确认：价格新低但%R不创新低，可能见底","结合其他指标：与MACD、RSI配合使用效果更佳"]}]);return{activeTab:t,goBack:m,navigateToApiSettings:o,navigateToStrategies:g,basicIndicators:_,trendIndicators:i,oscillatorIndicators:n}}},b={class:"strategy-intro-view"},M={class:"header"},I={class:"header-content"},S={class:"main-content"},C={class:"strategy-tabs"},R={key:0,class:"strategy-content"},K={class:"strategy-category"},L={class:"strategy-list"},w={class:"strategy-header"},P={class:"strategy-icon"},T={class:"strategy-info"},x={class:"strategy-desc"},V={class:"strategy-details"},B={class:"parameters-section"},J={class:"calculation-box"},E={class:"formula"},H={class:"formula-desc"},F={class:"logic-section"},N={class:"logic-steps"},q={class:"step-number"},z={class:"step-content"},O={class:"tips-section"},W={class:"tips-list"},j={key:1,class:"strategy-content"},G={class:"strategy-category"},Q={class:"strategy-list"},U={class:"strategy-header"},X={class:"strategy-icon"},Y={class:"strategy-info"},Z={class:"strategy-desc"},$={class:"strategy-details"},ss={class:"parameters-section"},ts={class:"calculation-box"},is={class:"formula"},as={class:"formula-desc"},es={class:"logic-section"},ls={class:"logic-steps"},os={class:"step-number"},ns={class:"step-content"},ds={class:"tips-section"},cs={class:"tips-list"},rs={key:2,class:"strategy-content"},vs={class:"strategy-category"},fs={class:"strategy-list"},us={class:"strategy-header"},ys={class:"strategy-icon"},ps={class:"strategy-info"},ms={class:"strategy-desc"},gs={class:"strategy-details"},_s={class:"parameters-section"},ks={class:"calculation-box"},hs={class:"formula"},Ds={class:"formula-desc"},As={class:"logic-section"},bs={class:"logic-steps"},Ms={class:"step-number"},Is={class:"step-content"},Ss={class:"tips-section"},Cs={class:"tips-list"},Rs={class:"next-steps"},Ks={class:"next-step-cards"};function Ls(f,t,m,o,g,_){return e(),l("div",b,[s("header",M,[s("div",I,[s("button",{class:"back-button",onClick:t[0]||(t[0]=(...i)=>o.goBack&&o.goBack(...i))},t[6]||(t[6]=[s("i",{class:"fas fa-arrow-left"},null,-1)])),t[7]||(t[7]=s("h1",null,"策略学堂",-1))])]),s("main",S,[t[29]||(t[29]=k('<div class="intro-section" data-v-ef775130><div class="intro-card" data-v-ef775130><h2 data-v-ef775130>📚 技术指标与参数详解</h2><p data-v-ef775130>深入了解各种技术分析指标的含义、计算方法和应用场景，掌握交易策略中的核心参数知识。</p><div class="key-features" data-v-ef775130><div class="feature-item" data-v-ef775130><i class="fas fa-chart-line" data-v-ef775130></i><span data-v-ef775130>技术指标</span></div><div class="feature-item" data-v-ef775130><i class="fas fa-calculator" data-v-ef775130></i><span data-v-ef775130>参数计算</span></div><div class="feature-item" data-v-ef775130><i class="fas fa-lightbulb" data-v-ef775130></i><span data-v-ef775130>实战应用</span></div></div></div></div>',1)),s("div",C,[s("button",{class:v(["tab-button",{active:o.activeTab==="basic"}]),onClick:t[1]||(t[1]=i=>o.activeTab="basic")},t[8]||(t[8]=[s("i",{class:"fas fa-chart-bar"},null,-1),y(" 基础指标 ")]),2),s("button",{class:v(["tab-button",{active:o.activeTab==="trend"}]),onClick:t[2]||(t[2]=i=>o.activeTab="trend")},t[9]||(t[9]=[s("i",{class:"fas fa-trending-up"},null,-1),y(" 趋势指标 ")]),2),s("button",{class:v(["tab-button",{active:o.activeTab==="oscillator"}]),onClick:t[3]||(t[3]=i=>o.activeTab="oscillator")},t[10]||(t[10]=[s("i",{class:"fas fa-wave-square"},null,-1),y(" 震荡指标 ")]),2)]),o.activeTab==="basic"?(e(),l("div",R,[s("div",K,[t[14]||(t[14]=s("h3",null,"📊 基础技术指标",-1)),t[15]||(t[15]=s("p",null,"基础技术指标是技术分析的基石，包括价格、成交量和基本图表形态。",-1)),s("div",L,[(e(!0),l(d,null,c(o.basicIndicators,i=>(e(),l("div",{class:"strategy-card",key:i.id},[s("div",w,[s("div",P,a(i.icon),1),s("div",T,[s("h4",null,a(i.name),1),s("p",x,a(i.description),1)]),s("div",{class:v(["strategy-risk",i.difficultyLevel])},a(i.difficulty),3)]),s("div",V,[s("div",B,[t[11]||(t[11]=s("h5",null,"📈 计算方法",-1)),s("div",J,[s("div",E,a(i.formula),1),s("div",H,a(i.formulaDesc),1)])]),s("div",F,[t[12]||(t[12]=s("h5",null,"🔍 关键要点",-1)),s("div",N,[(e(!0),l(d,null,c(i.keyPoints,(n,r)=>(e(),l("div",{class:"logic-step",key:r},[s("div",q,a(r+1),1),s("div",z,a(n),1)]))),128))])]),s("div",O,[t[13]||(t[13]=s("h5",null,"💡 实战应用",-1)),s("ul",W,[(e(!0),l(d,null,c(i.applications,n=>(e(),l("li",{key:n},a(n),1))),128))])])])]))),128))])])])):p("",!0),o.activeTab==="trend"?(e(),l("div",j,[s("div",G,[t[19]||(t[19]=s("h3",null,"📈 趋势跟踪指标",-1)),t[20]||(t[20]=s("p",null,"趋势指标帮助识别市场方向和趋势强度，是制定交易策略的重要工具。",-1)),s("div",Q,[(e(!0),l(d,null,c(o.trendIndicators,i=>(e(),l("div",{class:"strategy-card",key:i.id},[s("div",U,[s("div",X,a(i.icon),1),s("div",Y,[s("h4",null,a(i.name),1),s("p",Z,a(i.description),1)]),s("div",{class:v(["strategy-risk",i.difficultyLevel])},a(i.difficulty),3)]),s("div",$,[s("div",ss,[t[16]||(t[16]=s("h5",null,"📈 计算方法",-1)),s("div",ts,[s("div",is,a(i.formula),1),s("div",as,a(i.formulaDesc),1)])]),s("div",es,[t[17]||(t[17]=s("h5",null,"🔍 关键要点",-1)),s("div",ls,[(e(!0),l(d,null,c(i.keyPoints,(n,r)=>(e(),l("div",{class:"logic-step",key:r},[s("div",os,a(r+1),1),s("div",ns,a(n),1)]))),128))])]),s("div",ds,[t[18]||(t[18]=s("h5",null,"💡 实战应用",-1)),s("ul",cs,[(e(!0),l(d,null,c(i.applications,n=>(e(),l("li",{key:n},a(n),1))),128))])])])]))),128))])])])):p("",!0),o.activeTab==="oscillator"?(e(),l("div",rs,[s("div",vs,[t[24]||(t[24]=s("h3",null,"🌊 震荡指标",-1)),t[25]||(t[25]=s("p",null,"震荡指标在特定范围内波动，帮助识别超买超卖状态和市场转折点。",-1)),s("div",fs,[(e(!0),l(d,null,c(o.oscillatorIndicators,i=>(e(),l("div",{class:"strategy-card",key:i.id},[s("div",us,[s("div",ys,a(i.icon),1),s("div",ps,[s("h4",null,a(i.name),1),s("p",ms,a(i.description),1)]),s("div",{class:v(["strategy-risk",i.difficultyLevel])},a(i.difficulty),3)]),s("div",gs,[s("div",_s,[t[21]||(t[21]=s("h5",null,"📈 计算方法",-1)),s("div",ks,[s("div",hs,a(i.formula),1),s("div",Ds,a(i.formulaDesc),1)])]),s("div",As,[t[22]||(t[22]=s("h5",null,"🔍 关键要点",-1)),s("div",bs,[(e(!0),l(d,null,c(i.keyPoints,(n,r)=>(e(),l("div",{class:"logic-step",key:r},[s("div",Ms,a(r+1),1),s("div",Is,a(n),1)]))),128))])]),s("div",Ss,[t[23]||(t[23]=s("h5",null,"💡 实战应用",-1)),s("ul",Cs,[(e(!0),l(d,null,c(i.applications,n=>(e(),l("li",{key:n},a(n),1))),128))])])])]))),128))])])])):p("",!0),t[30]||(t[30]=k('<div class="risk-warning" data-v-ef775130><h3 data-v-ef775130>⚠️ 重要风险提醒</h3><div class="warning-grid" data-v-ef775130><div class="warning-item" data-v-ef775130><i class="fas fa-exclamation-triangle" data-v-ef775130></i><div data-v-ef775130><h4 data-v-ef775130>市场风险</h4><p data-v-ef775130>加密货币市场波动剧烈，任何策略都无法保证盈利</p></div></div><div class="warning-item" data-v-ef775130><i class="fas fa-chart-line" data-v-ef775130></i><div data-v-ef775130><h4 data-v-ef775130>策略风险</h4><p data-v-ef775130>历史表现不代表未来收益，请根据市场情况调整参数</p></div></div><div class="warning-item" data-v-ef775130><i class="fas fa-money-bill-wave" data-v-ef775130></i><div data-v-ef775130><h4 data-v-ef775130>资金管理</h4><p data-v-ef775130>建议只投入您能承受损失的资金，合理分配仓位</p></div></div><div class="warning-item" data-v-ef775130><i class="fas fa-clock" data-v-ef775130></i><div data-v-ef775130><h4 data-v-ef775130>持续监控</h4><p data-v-ef775130>定期检查策略表现，及时调整或停止不适合的策略</p></div></div></div></div>',1)),s("div",Rs,[t[28]||(t[28]=s("h3",null,"🚀 开始使用策略",-1)),s("div",Ks,[s("div",{class:"next-step-card",onClick:t[4]||(t[4]=(...i)=>o.navigateToApiSettings&&o.navigateToApiSettings(...i))},t[26]||(t[26]=[s("i",{class:"fas fa-key"},null,-1),s("h4",null,"配置API",-1),s("p",null,"首先配置交易所API密钥",-1)])),s("div",{class:"next-step-card",onClick:t[5]||(t[5]=(...i)=>o.navigateToStrategies&&o.navigateToStrategies(...i))},t[27]||(t[27]=[s("i",{class:"fas fa-play"},null,-1),s("h4",null,"启动策略",-1),s("p",null,"选择合适的策略开始交易",-1)]))])])])])}const Ps=h(A,[["render",Ls],["__scopeId","data-v-ef775130"]]);export{Ps as default};
