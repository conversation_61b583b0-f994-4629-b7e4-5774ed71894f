import{_ as x,u as h,r as u,l as B,o as M,c,d as f,e as a,F,p as L,t as i,f as d,v as m,w as A,g as E,k as b,n as z,q as I}from"./index-a2cd3c28.js";const N={name:"RankingManagementView",setup(){const D=h(),o=u([]),p=u(!1),n=u(!1),v=u(!1),y=u(null),l=u({name:"",initial:"",dailyEarnings:0,monthlyEarnings:0,totalEarnings:0,level:"LV.1",color:"#4a90e2",order:0}),k=B(()=>l.value.name.trim()&&l.value.initial.trim()),g=async()=>{var e,r,t;try{p.value=!0;const s=await b.get("/ranking/admin");s.data.success&&(o.value=s.data.data)}catch(s){console.error("获取排行榜数据失败:",s),console.error("错误详情:",((e=s.response)==null?void 0:e.data)||s.message),alert("获取排行榜数据失败: "+(((t=(r=s.response)==null?void 0:r.data)==null?void 0:t.error)||s.message))}finally{p.value=!1}},V=async()=>{var e,r;try{v.value?(await b.put(`/ranking/admin/${y.value}`,l.value),alert("更新成功")):(await b.post("/ranking/admin",l.value),alert("添加成功")),_(),g()}catch(t){console.error("保存失败:",t),alert("保存失败: "+(((r=(e=t.response)==null?void 0:e.data)==null?void 0:r.error)||t.message))}},C=async e=>{var r,t;if(confirm("确定要删除这个排行榜条目吗？"))try{await b.delete(`/ranking/admin/${e}`),alert("删除成功"),g()}catch(s){console.error("删除失败:",s),alert("删除失败: "+(((t=(r=s.response)==null?void 0:r.data)==null?void 0:t.error)||s.message))}},U=e=>{v.value=!0,y.value=e._id,l.value={...e},n.value=!0},w=()=>{v.value=!1,y.value=null,l.value={name:"",initial:"",dailyEarnings:0,monthlyEarnings:0,totalEarnings:0,level:"LV.1",color:"#4a90e2",order:0},n.value=!0},_=()=>{n.value=!1},R=()=>{D.go(-1)};return M(()=>{g()}),{rankings:o,loading:p,showDialog:n,isEditing:v,formData:l,isFormValid:k,fetchRankings:g,saveRanking:V,deleteRanking:C,editRanking:U,openAddDialog:w,closeDialog:_,goBack:R}}},S={class:"ranking-management"},q={class:"header"},T={class:"content"},j={class:"ranking-list"},G={class:"ranking-info"},H={class:"rank-number"},J={class:"user-details"},K={class:"username"},O={class:"level"},P={class:"earnings-info"},Q={class:"daily"},W={class:"monthly"},X={class:"total"},Y={class:"actions"},Z=["onClick"],$=["onClick"],oo={class:"dialog-header"},ao={class:"dialog-content"},lo={class:"form-group"},no={class:"form-group"},so={class:"form-group"},eo={class:"form-group"},to={class:"form-group"},io={class:"form-group"},ro={class:"form-group"},mo={class:"form-group"},go={class:"dialog-actions"},uo=["disabled"],vo={key:1,class:"loading"};function co(D,o,p,n,v,y){return c(),f("div",S,[a("div",q,[a("i",{class:"fas fa-arrow-left back-button",onClick:o[0]||(o[0]=(...l)=>n.goBack&&n.goBack(...l))}),o[15]||(o[15]=a("div",{class:"title"},"排行榜管理",-1)),a("i",{class:"fas fa-plus add-button",onClick:o[1]||(o[1]=(...l)=>n.openAddDialog&&n.openAddDialog(...l))})]),a("div",T,[a("div",j,[(c(!0),f(F,null,L(n.rankings,(l,k)=>(c(),f("div",{key:l._id,class:z(["ranking-item",{"top-three":k<3}])},[a("div",G,[a("div",H,i(k+1),1),a("div",{class:"user-avatar",style:I({backgroundColor:l.color})},i(l.initial),5),a("div",J,[a("div",K,i(l.name),1),a("div",O,i(l.level),1)])]),a("div",P,[a("div",Q,"日: "+i(l.dailyEarnings)+"U",1),a("div",W,"月: "+i(l.monthlyEarnings)+"U",1),a("div",X,"总: "+i(l.totalEarnings)+"U",1)]),a("div",Y,[a("i",{class:"fas fa-edit edit-btn",onClick:g=>n.editRanking(l)},null,8,Z),a("i",{class:"fas fa-trash delete-btn",onClick:g=>n.deleteRanking(l._id)},null,8,$)])],2))),128))])]),n.showDialog?(c(),f("div",{key:0,class:"dialog-overlay",onClick:o[14]||(o[14]=(...l)=>n.closeDialog&&n.closeDialog(...l))},[a("div",{class:"dialog",onClick:o[13]||(o[13]=A(()=>{},["stop"]))},[a("div",oo,[a("h3",null,i(n.isEditing?"编辑":"添加")+"排行榜条目",1),a("i",{class:"fas fa-times close-btn",onClick:o[2]||(o[2]=(...l)=>n.closeDialog&&n.closeDialog(...l))})]),a("div",ao,[a("div",lo,[o[16]||(o[16]=a("label",null,"姓名",-1)),d(a("input",{"onUpdate:modelValue":o[3]||(o[3]=l=>n.formData.name=l),type:"text",placeholder:"请输入姓名"},null,512),[[m,n.formData.name]])]),a("div",no,[o[17]||(o[17]=a("label",null,"首字母",-1)),d(a("input",{"onUpdate:modelValue":o[4]||(o[4]=l=>n.formData.initial=l),type:"text",maxlength:"2",placeholder:"请输入首字母"},null,512),[[m,n.formData.initial]])]),a("div",so,[o[18]||(o[18]=a("label",null,"日收益 (U)",-1)),d(a("input",{"onUpdate:modelValue":o[5]||(o[5]=l=>n.formData.dailyEarnings=l),type:"number",step:"0.01",placeholder:"0.00"},null,512),[[m,n.formData.dailyEarnings,void 0,{number:!0}]])]),a("div",eo,[o[19]||(o[19]=a("label",null,"月收益 (U)",-1)),d(a("input",{"onUpdate:modelValue":o[6]||(o[6]=l=>n.formData.monthlyEarnings=l),type:"number",step:"0.01",placeholder:"0.00"},null,512),[[m,n.formData.monthlyEarnings,void 0,{number:!0}]])]),a("div",to,[o[20]||(o[20]=a("label",null,"总收益 (U)",-1)),d(a("input",{"onUpdate:modelValue":o[7]||(o[7]=l=>n.formData.totalEarnings=l),type:"number",step:"0.01",placeholder:"0.00"},null,512),[[m,n.formData.totalEarnings,void 0,{number:!0}]])]),a("div",io,[o[21]||(o[21]=a("label",null,"等级",-1)),d(a("input",{"onUpdate:modelValue":o[8]||(o[8]=l=>n.formData.level=l),type:"text",placeholder:"LV.1"},null,512),[[m,n.formData.level]])]),a("div",ro,[o[22]||(o[22]=a("label",null,"颜色",-1)),d(a("input",{"onUpdate:modelValue":o[9]||(o[9]=l=>n.formData.color=l),type:"color"},null,512),[[m,n.formData.color]])]),a("div",mo,[o[23]||(o[23]=a("label",null,"排序",-1)),d(a("input",{"onUpdate:modelValue":o[10]||(o[10]=l=>n.formData.order=l),type:"number",placeholder:"0"},null,512),[[m,n.formData.order,void 0,{number:!0}]])])]),a("div",go,[a("button",{class:"cancel-btn",onClick:o[11]||(o[11]=(...l)=>n.closeDialog&&n.closeDialog(...l))},"取消"),a("button",{class:"confirm-btn",onClick:o[12]||(o[12]=(...l)=>n.saveRanking&&n.saveRanking(...l)),disabled:!n.isFormValid},i(n.isEditing?"更新":"添加"),9,uo)])])])):E("",!0),n.loading?(c(),f("div",vo,o[24]||(o[24]=[a("i",{class:"fas fa-spinner fa-spin"},null,-1),a("span",null,"加载中...",-1)]))):E("",!0)])}const po=x(N,[["render",co],["__scopeId","data-v-d56bd5e9"]]);export{po as default};
