import{_ as y,u as k,r as c,o as g,c as a,d as o,e,F as M,p as C,g as l,t as i,H as v,n as w,i as x}from"./index-a2cd3c28.js";const B={name:"MembershipView",setup(){const p=k(),s=c([]),u=c(null),r=c(!1),d=c(""),h=()=>{p.go(-1)},t=()=>{alert("联系客服功能暂时禁用，请稍后再试")},_=n=>({none:"普通用户",monthly:"月度会员",quarterly:"季度会员",yearly:"年会员",lifetime:"永久会员"})[n]||"未知",b=async()=>{try{r.value=!0,d.value="";const n=await v.get("/api/membership/config");n.data.success?s.value=n.data.data:d.value=n.data.error||"获取会员配置失败"}catch(n){console.error("获取会员配置错误:",n),d.value="网络错误，请检查连接"}finally{r.value=!1}},f=async()=>{try{const n=localStorage.getItem("token");if(!n)return;const m=await v.get("/api/membership/info",{headers:{Authorization:`Bearer ${n}`}});m.data.success&&(u.value=m.data.data)}catch(n){console.error("获取会员信息错误:",n)}};return g(async()=>{await b(),await f()}),{membershipConfigs:s,currentMembership:u,loading:r,error:d,goBack:h,goToContactService:t,getMembershipLevelName:_,loadMembershipConfigs:b}}},L={class:"membership-view"},N={class:"header"},V={class:"header-content"},D={class:"main-content"},S={key:0,class:"membership-cards"},z={key:0,class:"popular-badge"},F={class:"card-header"},I={class:"price-section"},P={class:"current-price"},T={key:0,class:"original-price"},q={class:"features-section"},A={class:"feature-category"},E={key:0},H={key:1},R={class:"feature-category"},j={class:"feature-category"},G={class:"benefits-list"},J={key:0},K={key:1},O={key:2},Q={key:1,class:"loading-section"},U={key:2,class:"error-section"},W={key:3,class:"current-membership"},X={class:"membership-status"},Y={class:"status-item"},Z={class:"value"},$={key:0,class:"status-item"},ee={class:"value"};function se(p,s,u,r,d,h){return a(),o("div",L,[e("header",N,[e("div",V,[e("button",{class:"back-button",onClick:s[0]||(s[0]=(...t)=>r.goBack&&r.goBack(...t))},s[2]||(s[2]=[e("i",{class:"fas fa-arrow-left"},null,-1)])),s[3]||(s[3]=e("h1",null,"开通会员",-1))])]),e("main",D,[s[13]||(s[13]=e("div",{class:"intro-section"},[e("div",{class:"intro-card"},[e("h2",null,"🎯 会员特权"),e("p",null,"升级会员，解锁更多策略权限和专属服务，让您的交易更加高效便捷！")])],-1)),r.membershipConfigs.length>0?(a(),o("div",S,[(a(!0),o(M,null,C(r.membershipConfigs,t=>(a(),o("div",{class:w(["membership-card",{popular:t.level==="yearly"}]),key:t.level},[t.level==="yearly"?(a(),o("div",z," 推荐 ")):l("",!0),e("div",F,[e("h3",null,i(t.name),1),e("div",I,[e("div",P,"¥"+i(t.price),1),t.originalPrice>t.price?(a(),o("div",T," 原价: ¥"+i(t.originalPrice),1)):l("",!0)])]),e("div",q,[e("div",A,[s[4]||(s[4]=e("h4",null,"📊 策略权限",-1)),t.features.strategies.contracts===999?(a(),o("p",E," 全策略永久使用 + 自动更新 ")):(a(),o("p",H,i(t.features.strategies.contracts)+" 合约 + "+i(t.features.strategies.spot)+" 现货策略 ",1))]),e("div",R,[s[5]||(s[5]=e("h4",null,"🎧 服务支持",-1)),e("p",null,i(t.features.service.support),1),e("p",null,i(t.features.service.response),1)]),e("div",j,[s[6]||(s[6]=e("h4",null,"🎁 专属权益",-1)),e("div",G,[t.features.benefits.discount?(a(),o("p",J,i(t.features.benefits.discount),1)):l("",!0),t.features.benefits.trial?(a(),o("p",K,i(t.features.benefits.trial),1)):l("",!0),t.features.benefits.referral?(a(),o("p",O,i(t.features.benefits.referral),1)):l("",!0)])])]),s[7]||(s[7]=e("button",{class:"activate-button disabled",disabled:""},[e("i",{class:"fas fa-headset"}),x(" 联系客服开通（暂时禁用） ")],-1))],2))),128))])):l("",!0),r.loading&&r.membershipConfigs.length===0?(a(),o("div",Q,s[8]||(s[8]=[e("i",{class:"fas fa-spinner fa-spin"},null,-1),e("p",null,"正在加载会员信息...",-1)]))):l("",!0),r.error?(a(),o("div",U,[s[9]||(s[9]=e("i",{class:"fas fa-exclamation-triangle"},null,-1)),e("p",null,i(r.error),1),e("button",{onClick:s[1]||(s[1]=(...t)=>r.loadMembershipConfigs&&r.loadMembershipConfigs(...t)),class:"retry-button"}," 重试 ")])):l("",!0),r.currentMembership?(a(),o("div",W,[s[12]||(s[12]=e("h3",null,"当前会员状态",-1)),e("div",X,[e("div",Y,[s[10]||(s[10]=e("span",{class:"label"},"会员等级:",-1)),e("span",Z,i(r.getMembershipLevelName(r.currentMembership.membershipLevel)),1)]),r.currentMembership.membershipLevel!=="none"?(a(),o("div",$,[s[11]||(s[11]=e("span",{class:"label"},"剩余天数:",-1)),e("span",ee,i(r.currentMembership.membershipDaysLeft===-1?"永久":r.currentMembership.membershipDaysLeft+" 天"),1)])):l("",!0)])])):l("",!0)])])}const re=y(B,[["render",se],["__scopeId","data-v-6d7a7ac6"]]);export{re as default};
