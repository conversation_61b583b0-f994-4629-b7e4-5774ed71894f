import{_ as N,u as D,r as f,o as B,c as d,d as c,e,i as h,F as O,p as K,n as p,t as u,w as V,g as S,k as X}from"./index-a2cd3c28.js";const z={name:"StrategyPermissionView",setup(){const M=D(),t=f([]),m=f(!1),s=f("加载中..."),g=f(!1),y=f(null),r=f([]),v=f([{id:"spot1",name:"现货策略1：顺势抄底 [OKX + 币安]",type:"spot",description:"以RSI6为指标方向，当RSI6趋势向下时买入（适用于OKX和币安）"},{id:"spot2",name:"现货策略2：顺势涨利 [OKX + 币安]",type:"spot",description:"以RSI6为指标方向，当RSI6趋势向上时买入（适用于OKX和币安）"},{id:"spot3",name:"现货策略3：低买高卖 [OKX + 币安]",type:"spot",description:"当RSI14低于30且跌破布林线时买入（适用于OKX和币安）"},{id:"spot4",name:"现货策略4：大单追踪 [OKX + 币安]",type:"spot",description:"当底部大买单成交后，价格上穿ma120时买入（适用于OKX和币安）"},{id:"spot5",name:"策略方案5：多多益善 [OKX + 币安]",type:"spot",description:"现货多多益善策略（适用于OKX和币安）"},{id:"futures1",name:"策略方案1：趋势动量追击 [OKX + 币安]",type:"futures",description:"MA(20)上穿MA(60) + 成交量放大（适用于OKX和币安）"},{id:"futures2",name:"策略方案2：震荡区间套利 [OKX + 币安]",type:"futures",description:"RSI(14)<30 + 布林线下轨做多（适用于OKX和币安）"},{id:"futures3",name:"策略方案3：突破追击 [OKX + 币安]",type:"futures",description:"价格突破前高/低 + 链上大额转账异动（适用于OKX和币安）"},{id:"futures4",name:"策略方案4：高频震荡双向狙击 [OKX + 币安]",type:"futures",description:"高波动率(ATR>5%) + 多空双向挂单（适用于OKX和币安）"},{id:"futures5",name:"策略方案6：超级高胜率策略 [OKX + 币安]",type:"futures",description:"TD=-10 + RSI14上升趋势开多（适用于OKX和币安）"},{id:"futures6",name:"策略方案7：MACD超神策略 [OKX + 币安]",type:"futures",description:"MACD(2,8,7)指标策略（适用于OKX和币安）"},{id:"futures7",name:"策略方案8：RSI顺势策略 [OKX + 币安]",type:"futures",description:"RSI14上穿65开多，RSI14下穿35开空（适用于OKX和币安）"},{id:"futures8",name:"策略方案9：TD战法策略 [OKX + 币安]",type:"futures",description:"TD=-13开多，TD=13开空（适用于OKX和币安）"},{id:"futures9",name:"策略方案10：顺风收益王者 [OKX + 币安]",type:"futures",description:"2h周期SMMA策略（适用于OKX和币安）"},{id:"futures10",name:"策略方案11：超长线策略 [OKX + 币安]",type:"futures",description:"MA(25)上穿MA(99)开多，MA(25)下穿MA(99)开空（适用于OKX和币安）"}]),i=()=>{M.go(-1)},l=a=>({none:"所有会员",monthly:"月度会员",quarterly:"季度会员",yearly:"年度会员",lifetime:"永久会员"})[a]||a,C=a=>({none:"fas fa-users",monthly:"fas fa-calendar-alt",quarterly:"fas fa-calendar-check",yearly:"fas fa-crown",lifetime:"fas fa-gem"})[a]||"fas fa-user",I=a=>`membership-${a}`,_=async()=>{var a,n;try{m.value=!0,s.value="加载权限配置...";const o=await X.get("/strategy-permission/config");if(o.data.success)t.value=o.data.data;else throw new Error(o.data.error||"获取权限配置失败")}catch(o){console.error("加载权限配置失败:",o),alert("加载权限配置失败: "+(((n=(a=o.response)==null?void 0:a.data)==null?void 0:n.error)||o.message))}finally{m.value=!1}},P=async()=>{var a,n;if(confirm("确定要初始化策略权限配置吗？这将重置所有现有配置。"))try{m.value=!0,s.value="初始化权限配置...";const o=await X.post("/strategy-permission/init",{});if(o.data.success)alert("权限配置初始化成功"),await _();else throw new Error(o.data.error||"初始化权限配置失败")}catch(o){console.error("初始化权限配置失败:",o),alert("初始化权限配置失败: "+(((n=(a=o.response)==null?void 0:a.data)==null?void 0:n.error)||o.message))}finally{m.value=!1}},E=a=>{y.value={...a},r.value=a.allowedStrategies.map(n=>n.strategyId),g.value=!0},k=()=>{g.value=!1,y.value=null,r.value=[]},R=a=>r.value.includes(a),T=a=>{const n=r.value.indexOf(a);n>-1?r.value.splice(n,1):r.value.push(a)},x=async()=>{var a,n;try{m.value=!0,s.value="保存权限配置...";const o=r.value.map(A=>{const b=v.value.find(L=>L.id===A);return{strategyId:b.id,strategyName:b.name,strategyType:b.type,description:b.description}}),w=await X.put(`/strategy-permission/config/${y.value.membershipLevel}`,{allowedStrategies:o});if(w.data.success)alert("权限配置保存成功"),await _(),k();else throw new Error(w.data.error||"保存权限配置失败")}catch(o){console.error("保存权限配置失败:",o),alert("保存权限配置失败: "+(((n=(a=o.response)==null?void 0:a.data)==null?void 0:n.error)||o.message))}finally{m.value=!1}};return B(()=>{_()}),{permissions:t,loading:m,loadingText:s,showEditModal:g,editingPermission:y,selectedStrategies:r,availableStrategies:v,goBack:i,getMembershipName:l,getMembershipIcon:C,getMembershipClass:I,loadPermissions:_,initializePermissions:P,editPermission:E,closeEditModal:k,isStrategySelected:R,toggleStrategy:T,savePermission:x}}},q={class:"strategy-permission-view"},F={class:"header"},j={class:"header-actions"},G={class:"content"},H={class:"permission-cards"},J={class:"card-header"},Q={class:"membership-info"},U={class:"strategy-count"},W={class:"card-body"},Y={class:"strategies-list"},Z={class:"strategy-info"},$={class:"strategy-name"},ee={class:"strategy-type"},se={class:"strategy-description"},te={class:"card-footer"},ie=["onClick","disabled"],ae={class:"modal-header"},oe={class:"modal-body"},ne={class:"available-strategies"},re={class:"strategies-grid"},le=["onClick"],de={class:"strategy-checkbox"},ce={key:0,class:"fas fa-check"},ue={class:"strategy-details"},me={class:"strategy-name"},fe={class:"strategy-type"},pe={class:"strategy-description"},ye={class:"modal-footer"},ve=["disabled"],ge=["disabled"],_e={key:1,class:"loading-overlay"},be={class:"loading-spinner"};function he(M,t,m,s,g,y){var r,v;return d(),c("div",q,[e("div",F,[e("i",{class:"fas fa-arrow-left back-button",onClick:t[0]||(t[0]=(...i)=>s.goBack&&s.goBack(...i))}),t[8]||(t[8]=e("div",{class:"title"},"策略权限控制",-1)),e("div",j,[e("button",{class:"btn btn-primary",onClick:t[1]||(t[1]=(...i)=>s.initializePermissions&&s.initializePermissions(...i))},t[7]||(t[7]=[e("i",{class:"fas fa-sync"},null,-1),h(" 初始化配置 ")]))])]),e("div",G,[e("div",H,[(d(!0),c(O,null,K(s.permissions,i=>(d(),c("div",{key:i.membershipLevel,class:p(["permission-card",s.getMembershipClass(i.membershipLevel)])},[e("div",J,[e("div",Q,[e("i",{class:p(s.getMembershipIcon(i.membershipLevel))},null,2),e("h3",null,u(s.getMembershipName(i.membershipLevel)),1)]),e("div",U,u(i.allowedStrategies.length)+" 个策略 ",1)]),e("div",W,[e("div",Y,[(d(!0),c(O,null,K(i.allowedStrategies,l=>(d(),c("div",{key:l.strategyId,class:p(["strategy-item",l.strategyType])},[e("div",Z,[e("div",$,u(l.strategyName),1),e("div",ee,[e("span",{class:p(["type-badge",l.strategyType])},u(l.strategyType==="spot"?"现货":"合约"),3)])]),e("div",se,u(l.description),1)],2))),128))])]),e("div",te,[e("button",{class:"btn btn-outline",onClick:l=>s.editPermission(i),disabled:s.loading},t[9]||(t[9]=[e("i",{class:"fas fa-edit"},null,-1),h(" 编辑权限 ")]),8,ie)])],2))),128))])]),s.showEditModal?(d(),c("div",{key:0,class:"modal-overlay",onClick:t[6]||(t[6]=(...i)=>s.closeEditModal&&s.closeEditModal(...i))},[e("div",{class:"modal-content",onClick:t[5]||(t[5]=V(()=>{},["stop"]))},[e("div",ae,[e("h3",null,[e("i",{class:p(s.getMembershipIcon((r=s.editingPermission)==null?void 0:r.membershipLevel))},null,2),h(" 编辑 "+u(s.getMembershipName((v=s.editingPermission)==null?void 0:v.membershipLevel))+" 权限 ",1)]),e("button",{class:"close-button",onClick:t[2]||(t[2]=(...i)=>s.closeEditModal&&s.closeEditModal(...i))},t[10]||(t[10]=[e("i",{class:"fas fa-times"},null,-1)]))]),e("div",oe,[e("div",ne,[t[11]||(t[11]=e("h4",null,"可选策略方案",-1)),e("div",re,[(d(!0),c(O,null,K(s.availableStrategies,i=>(d(),c("div",{key:i.id,class:p(["strategy-option",{selected:s.isStrategySelected(i.id),[i.type]:!0}]),onClick:l=>s.toggleStrategy(i.id)},[e("div",de,[s.isStrategySelected(i.id)?(d(),c("i",ce)):S("",!0)]),e("div",ue,[e("div",me,u(i.name),1),e("div",fe,[e("span",{class:p(["type-badge",i.type])},u(i.type==="spot"?"现货":"合约"),3)]),e("div",pe,u(i.description),1)])],10,le))),128))])])]),e("div",ye,[e("button",{class:"btn btn-secondary",onClick:t[3]||(t[3]=(...i)=>s.closeEditModal&&s.closeEditModal(...i)),disabled:s.loading}," 取消 ",8,ve),e("button",{class:"btn btn-primary",onClick:t[4]||(t[4]=(...i)=>s.savePermission&&s.savePermission(...i)),disabled:s.loading},t[12]||(t[12]=[e("i",{class:"fas fa-save"},null,-1),h(" 保存 ")]),8,ge)])])])):S("",!0),s.loading?(d(),c("div",_e,[e("div",be,[t[13]||(t[13]=e("i",{class:"fas fa-spinner fa-spin"},null,-1)),e("div",null,u(s.loadingText),1)])])):S("",!0)])}const Ke=N(z,[["render",he],["__scopeId","data-v-f0363b9d"]]);export{Ke as default};
