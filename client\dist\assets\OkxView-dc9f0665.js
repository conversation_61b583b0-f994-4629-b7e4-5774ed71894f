import{_ as re,r as y,b as Re,c as n,d as i,e as a,i as I,g as c,f as k,v as q,n as B,t as d,w as me,k as M,l as R,z as ne,o as we,A as J,B as ie,F as N,p as V,C as F,j as Y,m as qe,x as fe,D as Ge,y as xe,E as We,G as Qe}from"./index-a2cd3c28.js";import{s as pe}from"./strategyPermissionService-52b945bf.js";const ze={name:"OkxApiModal",props:{show:{type:Boolean,default:!1},isReconfiguring:{type:Boolean,default:!1}},emits:["close","api-validated"],setup(D,{emit:e}){const m=y(""),t=y(""),S=y(""),P=y(!1),o=y(""),b=Re({apiKey:"",secretKey:"",passphrase:""}),W=()=>{let _=!0;return b.apiKey="",b.secretKey="",b.passphrase="",o.value="",m.value.trim()||(b.apiKey="API Key不能为空",_=!1),t.value.trim()||(b.secretKey="Secret Key不能为空",_=!1),S.value.trim()||(b.passphrase="Passphrase不能为空",_=!1),_},h=async()=>{var _;if(W()){P.value=!0,o.value="";try{console.log("保存OKX API密钥...");let T="default";const v=localStorage.getItem("user");if(v)try{const x=JSON.parse(v);x&&x.uid&&(T=x.uid)}catch(x){console.error("解析用户信息失败:",x)}console.log("API密钥信息:",{apiKey:m.value?`${m.value.substring(0,4)}...${m.value.substring(m.value.length-4)}`:"",secretKeyLength:t.value?t.value.length:0,passphraseLength:S.value?S.value.length:0,userId:T});const C=await M.post("/okx/save-keys",{apiKey:m.value,secretKey:t.value,passphrase:S.value,userId:T});console.log("API保存响应:",C.data),C.data.success?(console.log("API密钥保存成功"),e("api-validated",{apiKey:m.value,secretKey:t.value,passphrase:S.value}),l()):(console.log("API密钥保存失败:",C.data.error),o.value=C.data.error||"API密钥保存失败")}catch(T){console.error("API密钥保存错误:",T),T.response?(console.error("错误响应:",T.response.status,T.response.data),o.value=((_=T.response.data)==null?void 0:_.error)||`保存失败 (${T.response.status})`):T.request?(console.error("无响应错误:",T.request),o.value="服务器未响应，请检查网络连接"):(console.error("请求配置错误:",T.message),o.value="请求配置错误: "+T.message)}finally{P.value=!1}}},l=()=>{e("close")};return{apiKey:m,secretKey:t,passphrase:S,loading:P,apiError:o,errors:b,saveApiKeys:h,closeModal:l,isReconfiguring:D.isReconfiguring}}},Je={class:"modal-content"},je={class:"modal-header"},Ze={class:"modal-body"},$e={key:0,class:"loading-spinner"},et={key:1},tt={class:"api-instructions"},at={key:0,class:"update-note"},st={class:"form-group"},ot={key:0,class:"error-message"},lt={class:"form-group"},nt={key:0,class:"error-message"},it={class:"form-group"},rt={key:0,class:"error-message"},ut={key:0,class:"error-message"},dt={class:"form-actions"};function ct(D,e,m,t,S,P){return m.show?(n(),i("div",{key:0,class:"modal-overlay",onClick:e[6]||(e[6]=me((...o)=>t.closeModal&&t.closeModal(...o),["self"]))},[a("div",Je,[a("div",je,[e[7]||(e[7]=a("h3",null,"OKX API设置",-1)),a("button",{class:"close-button",onClick:e[0]||(e[0]=(...o)=>t.closeModal&&t.closeModal(...o))},"×")]),a("div",Ze,[t.loading?(n(),i("div",$e,e[8]||(e[8]=[a("div",{class:"spinner"},null,-1),a("div",{style:{"margin-left":"10px"}},"验证中...",-1)]))):(n(),i("div",et,[a("div",tt,[e[10]||(e[10]=a("p",null,"请输入您的OKX API密钥信息，用于连接到OKX交易平台。",-1)),e[11]||(e[11]=a("p",null,[I("您可以在 "),a("a",{href:"https://www.okx.com/account/my-api",target:"_blank"},"OKX官网"),I(" 创建API密钥。")],-1)),e[12]||(e[12]=a("p",{class:"important-note"},"注意：请确保您的API密钥具有交易权限，并且没有IP地址限制。",-1)),e[13]||(e[13]=a("p",null,"您的API密钥将被安全地保存，用于后续的交易操作。",-1)),t.isReconfiguring?(n(),i("p",at,e[9]||(e[9]=[a("i",{class:"fas fa-info-circle"},null,-1),I(" 您正在更新API密钥，新的密钥将替换当前配置。 ")]))):c("",!0)]),a("div",st,[e[14]||(e[14]=a("label",{for:"apiKey"},"API Key",-1)),k(a("input",{type:"text",id:"apiKey","onUpdate:modelValue":e[1]||(e[1]=o=>t.apiKey=o),placeholder:"输入OKX API Key",class:B({error:t.errors.apiKey})},null,2),[[q,t.apiKey]]),t.errors.apiKey?(n(),i("div",ot,d(t.errors.apiKey),1)):c("",!0)]),a("div",lt,[e[15]||(e[15]=a("label",{for:"secretKey"},"Secret Key",-1)),k(a("input",{type:"password",id:"secretKey","onUpdate:modelValue":e[2]||(e[2]=o=>t.secretKey=o),placeholder:"输入OKX Secret Key",class:B({error:t.errors.secretKey})},null,2),[[q,t.secretKey]]),t.errors.secretKey?(n(),i("div",nt,d(t.errors.secretKey),1)):c("",!0)]),a("div",it,[e[16]||(e[16]=a("label",{for:"passphrase"},"Passphrase",-1)),k(a("input",{type:"password",id:"passphrase","onUpdate:modelValue":e[3]||(e[3]=o=>t.passphrase=o),placeholder:"输入OKX Passphrase",class:B({error:t.errors.passphrase})},null,2),[[q,t.passphrase]]),t.errors.passphrase?(n(),i("div",rt,d(t.errors.passphrase),1)):c("",!0)]),t.apiError?(n(),i("div",ut,d(t.apiError),1)):c("",!0),a("div",dt,[a("button",{class:"cancel-button",onClick:e[4]||(e[4]=(...o)=>t.closeModal&&t.closeModal(...o))},"取消"),a("button",{class:"submit-button",onClick:e[5]||(e[5]=(...o)=>t.saveApiKeys&&t.saveApiKeys(...o))},"保存API密钥")])]))])])])):c("",!0)}const vt=re(ze,[["render",ct],["__scopeId","data-v-0ce35b8c"]]);const gt={name:"OkxStrategyForm",props:{apiConnected:{type:Boolean,default:!1},userId:{type:String,default:"default"}},emits:["strategy-created"],setup(D,{emit:e}){const m=y("spot"),t=y(""),S=y("BTC-USDT"),P=y(100),o=y(10),b=y("both"),W=y(!1),h=y(""),l=y("single"),_=y(!1),T=y([]),v=y({directions:["both"],tradingPairs:["BTC-USDT"]}),C=y([]),x=y(!1),G=[{value:"BTC-USDT",label:"BTC-USDT (比特币)"},{value:"ETH-USDT",label:"ETH-USDT (以太坊)"},{value:"XRP-USDT",label:"XRP-USDT (瑞波币)"},{value:"LTC-USDT",label:"LTC-USDT (莱特币)"},{value:"BCH-USDT",label:"BCH-USDT (比特币现金)"},{value:"EOS-USDT",label:"EOS-USDT (柚子)"},{value:"DOT-USDT",label:"DOT-USDT (波卡)"},{value:"ADA-USDT",label:"ADA-USDT (艾达币)"}],Q=[{value:"UNI-USDT",label:"UNI-USDT (Uniswap)"},{value:"AAVE-USDT",label:"AAVE-USDT (Aave)"},{value:"COMP-USDT",label:"COMP-USDT (Compound)"},{value:"SUSHI-USDT",label:"SUSHI-USDT (SushiSwap)"},{value:"CRV-USDT",label:"CRV-USDT (Curve)"},{value:"SNX-USDT",label:"SNX-USDT (Synthetix)"},{value:"MKR-USDT",label:"MKR-USDT (Maker)"},{value:"YFI-USDT",label:"YFI-USDT (Yearn.Finance)"}],w=[{value:"BTC-USDT-SWAP",label:"BTC-USDT-SWAP (比特币永续)"},{value:"ETH-USDT-SWAP",label:"ETH-USDT-SWAP (以太坊永续)"},{value:"XRP-USDT-SWAP",label:"XRP-USDT-SWAP (瑞波币永续)"},{value:"LTC-USDT-SWAP",label:"LTC-USDT-SWAP (莱特币永续)"},{value:"BCH-USDT-SWAP",label:"BCH-USDT-SWAP (比特币现金永续)"},{value:"EOS-USDT-SWAP",label:"EOS-USDT-SWAP (柚子永续)"},{value:"DOT-USDT-SWAP",label:"DOT-USDT-SWAP (波卡永续)"},{value:"ADA-USDT-SWAP",label:"ADA-USDT-SWAP (艾达币永续)"}],te=[{value:"UNI-USDT-SWAP",label:"UNI-USDT-SWAP (Uniswap永续)"},{value:"AAVE-USDT-SWAP",label:"AAVE-USDT-SWAP (Aave永续)"},{value:"COMP-USDT-SWAP",label:"COMP-USDT-SWAP (Compound永续)"},{value:"SUSHI-USDT-SWAP",label:"SUSHI-USDT-SWAP (SushiSwap永续)"},{value:"CRV-USDT-SWAP",label:"CRV-USDT-SWAP (Curve永续)"},{value:"SNX-USDT-SWAP",label:"SNX-USDT-SWAP (Synthetix永续)"},{value:"MKR-USDT-SWAP",label:"MKR-USDT-SWAP (Maker永续)"},{value:"YFI-USDT-SWAP",label:"YFI-USDT-SWAP (Yearn.Finance永续)"}],Se=[{value:"spot1",label:"现货策略1（顺势抄底）"},{value:"spot2",label:"现货策略2（顺势涨利）"},{value:"spot3",label:"现货策略3（低买高卖）"},{value:"spot4",label:"现货策略4（大单追踪）"},{value:"spot5",label:"现货策略5（多多益善）"}],be=[{value:"futures1",label:"方案一：趋势动量追击"},{value:"futures2",label:"方案二：震荡区间套利"},{value:"futures3",label:"方案三：突破追击"},{value:"futures4",label:"方案四：高频震荡双向狙击"},{value:"futures5",label:"方案五：超级高胜率策略"},{value:"futures6",label:"方案六：MACD超神策略"},{value:"futures7",label:"方案七：RSI顺势策略"},{value:"futures8",label:"方案八：TD战法策略"},{value:"futures9",label:"方案九：顺风收益王者"},{value:"futures10",label:"方案十：超长线策略"}],H=R(()=>{if(!C.value.length)return[];const u=C.value.map(g=>g.strategyId);return Se.filter(g=>{const A=pe.mapStrategyTemplateToId(g.value);return u.includes(A)})}),ue=R(()=>{if(!C.value.length)return[];const u=C.value.map(g=>g.strategyId);return be.filter(g=>{const A=pe.mapStrategyTemplateToId(g.value);return u.includes(A)})}),ae=R(()=>m.value==="spot"?[...G,...Q].map(g=>g.value).every(g=>v.value.tradingPairs.includes(g)):[...w,...te].map(g=>g.value).every(g=>v.value.tradingPairs.includes(g))),j=()=>{if(m.value==="spot"){const u=[...G,...Q].map(g=>g.value);ae.value?v.value.tradingPairs=[]:v.value.tradingPairs=[...u]}else{const u=[...w,...te].map(g=>g.value);ae.value?v.value.tradingPairs=[]:v.value.tradingPairs=[...u]}},L=u=>({spot1:"现货策略1（顺势抄底）",spot2:"现货策略2（顺势涨利）",spot3:"现货策略3（低买高卖）",spot4:"现货策略4（大单追踪）",spot5:"现货策略5（多多益善）",futures1:"方案一：趋势动量追击",futures2:"方案二：震荡区间套利",futures3:"方案三：突破追击",futures4:"方案四：高频震荡双向狙击",futures5:"方案五：超级高胜率策略",futures6:"方案六：MACD超神策略",futures7:"方案七：RSI顺势策略",futures8:"方案八：TD战法策略",futures9:"方案九：顺风收益王者",futures10:"方案十：超长线策略"})[u]||u,de=(u,g=null)=>{const A=g||m.value;switch(u){case"long":return A==="spot"?"买入":"做多";case"short":return"做空";case"both":return"自动判断";default:return u}},Z=()=>{const u=t.value;return{futures1:{min:3,max:20},futures2:{min:5,max:30},futures3:{min:10,max:50},futures4:{min:50,max:100},futures5:{min:5,max:25},futures6:{min:8,max:35},futures7:{min:10,max:40},futures8:{min:3,max:15},futures9:{min:5,max:20},futures10:{min:2,max:10}}[u]||{min:1,max:100}},ye=()=>{const u=Z();return`建议杠杆范围：${u.min}-${u.max}倍，请根据风险承受能力选择`},$=()=>{const u=t.value;return{spot1:{min:50,max:5e3},spot2:{min:100,max:1e4},spot3:{min:200,max:2e4},spot4:{min:500,max:5e4},spot5:{min:20,max:2e3},futures1:{min:100,max:1e4},futures2:{min:50,max:5e3},futures3:{min:200,max:2e4},futures4:{min:20,max:2e3},futures5:{min:100,max:1e4},futures6:{min:150,max:15e3},futures7:{min:80,max:8e3},futures8:{min:300,max:3e4},futures9:{min:100,max:1e4},futures10:{min:500,max:5e4}}[u]||{min:1,max:1e5}},Te=()=>{const u=$(),g=t.value;return{spot1:`建议金额：${u.min}-${u.max} USDT。此金额将直接用于买入现货，RSI6<20时买入，>40时卖出。`,spot2:`建议金额：${u.min}-${u.max} USDT。此金额将直接用于买入现货，RSI6>60时买入，>80时卖出。`,spot3:`建议金额：${u.min}-${u.max} USDT。此金额将直接用于买入现货，RSI14<30+布林线突破时买入。`,spot4:`建议金额：${u.min}-${u.max} USDT。此金额将直接用于买入现货，大买单+MA120突破时买入。`,spot5:`建议金额：${u.min}-${u.max} USDT。此金额将直接用于买入现货，立即买入，1%跌幅加仓0.5倍。`,futures1:`建议总资金：${u.min}-${u.max} USDT。实际开仓金额 = 总资金×2%÷(ATR×3)，根据市场波动自动调整仓位。`,futures2:`建议总资金：${u.min}-${u.max} USDT。实际开仓金额 = 总资金×1.5%÷(区间高度×杠杆)，根据震荡幅度调整仓位。`,futures3:`建议总资金：${u.min}-${u.max} USDT。实际开仓金额 = 总资金×3%÷(突破幅度×2)，根据突破强度调整仓位。`,futures4:`建议总资金：${u.min}-${u.max} USDT。实际开仓金额 = 总资金×10%÷(ATR×杠杆)，高频交易，仓位限制≤总资金×20倍。`,futures5:`建议金额：${u.min}-${u.max} USDT。基础仓位1%，TD=-10/+10时加仓，最多11次加仓，倍数递增。`,futures6:`建议金额：${u.min}-${u.max} USDT。基础仓位1%，MACD金叉/死叉时加仓，最多8次加仓。`,futures7:`建议金额：${u.min}-${u.max} USDT。基础仓位1%，RSI14穿越65/35时加仓，最多11次加仓。`,futures8:`建议金额：${u.min}-${u.max} USDT。基础仓位1%，TD=-15/-17/-19/-21时分别加仓1/1/2/5倍。`,futures9:`建议金额：${u.min}-${u.max} USDT。基础仓位2%，SMMA交叉+EMA200+日线趋势确认时开仓。`,futures10:`建议金额：${u.min}-${u.max} USDT。基础仓位2%，MA25/99交叉时加仓，最多6次加仓，超长线持有。`}[g]||`建议金额范围：${u.min}-${u.max} USDT，请根据资金情况选择`},Ae=()=>{const u=$(),g=t.value;return se()?`总资金：${u.min}-${u.max} USDT（系统将动态计算实际仓位）`:g&&g.startsWith("futures")?`开仓金额：${u.min}-${u.max} USDT（可能有加仓）`:g&&g.startsWith("spot")?`买入金额：${u.min}-${u.max} USDT（直接买入现货）`:`建议范围：${u.min}-${u.max} USDT`},De=()=>se()?"总资金 (USDT)":"开仓金额 (USDT)",se=()=>["futures1","futures2","futures3","futures4"].includes(t.value),Pe=()=>({futures1:"【趋势动量追击】您输入的是总资金，系统会根据ATR(14)市场波动自动计算实际开仓金额。公式：实际开仓 = 总资金×2%÷(ATR×3)。波动大时仓位小，波动小时仓位大，有效控制风险。",futures2:"【震荡区间套利】您输入的是总资金，系统会根据布林线区间高度自动计算实际开仓金额。公式：实际开仓 = 总资金×1.5%÷(区间高度×杠杆)。震荡大时仓位小，震荡小时仓位大。",futures3:"【突破追击】您输入的是总资金，系统会根据突破幅度自动计算实际开仓金额。公式：实际开仓 = 总资金×3%÷(突破幅度×2)。突破强度大时仓位小，突破温和时仓位大。",futures4:"【高频震荡双向狙击】您输入的是总资金，系统会根据ATR(7)短期波动自动计算实际开仓金额。公式：实际开仓 = 总资金×10%÷(ATR×杠杆)。适合高频交易，仓位限制≤总资金×20倍。"})[t.value]||"此策略使用动态仓位计算，实际开仓金额会根据市场条件自动调整。",oe=()=>{const u=t.value;return{spot1:"low-risk",spot2:"medium-risk",spot3:"medium-risk",spot4:"high-risk",spot5:"low-risk",futures1:"medium-risk",futures2:"medium-risk",futures3:"high-risk",futures4:"very-high-risk",futures5:"medium-risk",futures6:"medium-risk",futures7:"high-risk",futures8:"low-risk",futures9:"medium-risk",futures10:"low-risk"}[u]||"medium-risk"},Ue=()=>{const u=oe();return{"low-risk":"低风险","medium-risk":"中等风险","high-risk":"高风险","very-high-risk":"极高风险"}[u]||"中等风险"},ke=()=>{if(!t.value)return 0;const{directions:u,tradingPairs:g}=v.value;return!u.length||!g.length?0:u.length*g.length},he=()=>{const u=[],{directions:g,tradingPairs:A}=v.value;for(const X of A)for(const z of g)m.value==="futures"?u.push({type:m.value,symbol:X,amount:P.value,leverage:o.value,direction:z,strategyTemplate:t.value,strategyName:L(t.value)}):z!=="both"&&u.push({type:m.value,symbol:X,amount:P.value,leverage:1,direction:z,strategyTemplate:t.value,strategyName:L(t.value)});T.value=u,_.value=!0};ne(m,u=>{u==="spot"?((b.value==="both"||b.value==="short")&&(b.value="long"),(v.value.directions.includes("both")||v.value.directions.includes("short"))&&(v.value.directions=v.value.directions.filter(g=>g!=="both"&&g!=="short")),v.value.directions.includes("long")||(v.value.directions=["long"])):u==="futures"&&(b.value==="long"&&(b.value="both"),v.value.directions.length===1&&v.value.directions[0]==="long"&&(v.value.directions=["both"])),t.value="",u==="spot"?(S.value.includes("-SWAP")&&(S.value=S.value.replace("-SWAP","")),v.value.tradingPairs=v.value.tradingPairs.filter(g=>!g.includes("-SWAP")).map(g=>g.replace("-SWAP","")),v.value.tradingPairs.length===0&&(v.value.tradingPairs=["BTC-USDT"])):u==="futures"&&(S.value.includes("-SWAP")||(S.value=S.value+"-SWAP"),v.value.tradingPairs=v.value.tradingPairs.filter(g=>!g.includes("-SWAP")).map(g=>g+"-SWAP"),v.value.tradingPairs.length===0&&(v.value.tradingPairs=["BTC-USDT-SWAP"])),_.value=!1,T.value=[]}),ne(t,u=>{if(u){if(m.value==="futures"){const X=Z(),z=Math.round((X.min+X.max)/2);o.value=z}const g=$(),A=Math.round(g.min*2);P.value=A}});const ce=async()=>{try{x.value=!0,C.value=await pe.getUserStrategies(),console.log("用户可用策略:",C.value)}catch(u){console.error("加载用户策略权限失败:",u),C.value=[]}finally{x.value=!1}};ne(m,()=>{t.value=""}),ne(l,()=>{_.value=!1,T.value=[]});const _e=async()=>{var u,g;if(!D.apiConnected){h.value="请先连接OKX API";return}if(le()){try{await pe.validateStrategyPermission(t.value)}catch(A){h.value=A.message;return}W.value=!0,h.value="";try{console.log("OkxStrategyForm创建单个策略");const A=await M.post("/okx/strategy",{userId:D.userId,type:m.value,symbol:S.value,amount:P.value,leverage:m.value==="futures"?o.value:1,direction:b.value,strategyTemplate:t.value||null,strategyName:t.value?L(t.value):null});A.data.success?(console.log("策略创建成功，后端响应:",A.data),m.value==="futures"&&t.value==="futures2"?alert(`策略创建成功！

注意：系统会等待市场条件满足后再开仓。

对于futures2策略，需要满足以下条件：
- RSI<45且价格接近布林带下轨（做多）
- 或RSI>55且价格接近布林带上轨（做空）

您可以在策略管理中查看详细状态。`):alert("策略创建成功！系统会等待市场条件满足后开仓。"),e("strategy-created",{...A.data,type:m.value,symbol:S.value,amount:P.value,leverage:m.value==="futures"?o.value:1,direction:b.value,strategyTemplate:t.value,strategyName:t.value?L(t.value):null}),K()):h.value=A.data.error||"创建策略失败"}catch(A){console.error("创建策略失败:",A),(g=(u=A.response)==null?void 0:u.data)!=null&&g.error?h.value=A.response.data.error:A.message?h.value=`创建策略失败: ${A.message}`:h.value="创建策略失败，请检查网络连接或API配置"}finally{W.value=!1}}},Ce=async()=>{var u,g;if(!D.apiConnected){h.value="请先连接OKX API";return}if(T.value.length===0){h.value="请先预览策略列表";return}W.value=!0,h.value="";try{console.log("OkxStrategyForm创建批量策略");const A=await M.post("/okx/batch-strategies",{userId:D.userId,strategies:T.value});if(A.data.success){console.log("批量策略创建成功，后端响应:",A.data),m.value==="futures"?alert(`成功创建 ${A.data.strategies.length} 个策略！

注意：系统会等待市场条件满足后再开仓。

您可以在策略管理中查看详细状态。`):alert(`成功创建 ${A.data.strategies.length} 个策略！系统会等待市场条件满足后开仓。`);for(const X of A.data.strategies)e("strategy-created",X);K(),_.value=!1,T.value=[],h.value=`成功创建 ${A.data.strategies.length} 个策略`}else h.value=A.data.error||"批量创建策略失败"}catch(A){console.error("批量创建策略失败:",A),(g=(u=A.response)==null?void 0:u.data)!=null&&g.error?h.value=A.response.data.error:A.message?h.value=`批量创建策略失败: ${A.message}`:h.value="批量创建策略失败，请检查网络连接或API配置"}finally{W.value=!1}},le=()=>{if(h.value="",!P.value||P.value<=0)return h.value="请输入有效的开仓金额",!1;if(l.value==="batch"){if(!v.value.directions.length)return h.value="请选择至少一个交易方向",!1;if(!v.value.tradingPairs.length)return h.value="请选择至少一个交易对",!1}return!0},K=()=>{t.value="",P.value=100,o.value=10,m.value==="spot"?b.value="long":b.value="both",l.value==="batch"&&(m.value==="spot"?(v.value.tradingPairs=["BTC-USDT"],v.value.directions=["long"]):(v.value.tradingPairs=["BTC-USDT-SWAP"],v.value.directions=["both"]))};return we(()=>{ce()}),{strategyType:m,strategyTemplate:t,symbol:S,amount:P,leverage:o,direction:b,loading:W,error:h,createMode:l,batchParams:v,showPreview:_,batchStrategies:T,spotMainPairs:G,spotDefiPairs:Q,futuresMainPairs:w,futuresDefiPairs:te,userStrategies:C,permissionLoading:x,availableSpotStrategies:H,availableFuturesStrategies:ue,loadUserPermissions:ce,isAllTradingPairsSelected:ae,toggleAllTradingPairs:j,getStrategyTitle:L,getDirectionText:de,getLeverageRange:Z,getLeverageHint:ye,getAmountRange:$,getAmountHint:Te,getAmountPlaceholder:Ae,getAmountLabel:De,isDynamicPositionStrategy:se,getDynamicPositionExplanation:Pe,getRiskLevel:oe,getRiskLevelText:Ue,calculateTotalStrategies:ke,previewBatchStrategies:he,submitStrategy:_e,submitBatchStrategies:Ce}}},ft={class:"strategy-form"},pt={key:0,class:"loading-spinner"},mt={key:1},St={class:"form-group"},bt={class:"radio-group"},yt={class:"radio-label"},Tt={class:"radio-label"},At={class:"form-group"},Dt={class:"radio-group"},Pt={class:"radio-label"},Ut={class:"radio-label"},kt={class:"form-group"},ht={key:0,label:"现货策略"},_t=["value"],Ct={key:1,label:"合约策略"},xt=["value"],wt={key:2,label:"现货策略"},It={key:3,label:"合约策略"},Ot={key:0,class:"strategy-info"},Wt={class:"strategy-basic-info"},Rt={class:"strategy-type-badge"},Mt={class:"form-group"},Nt={key:0,label:"主流币种"},Kt={key:1,label:"DeFi币种"},Bt={key:2,label:"Layer 2和公链"},Vt={key:3,label:"热门币种"},Lt={key:4,label:"主流合约"},Et={key:5,label:"DeFi合约"},Ft={key:6,label:"Layer 2和公链合约"},Ht={key:7,label:"热门合约"},Xt={key:1},Yt={class:"form-group"},qt={for:"amount"},Gt=["placeholder","min","max"],Qt={class:"amount-hint"},zt={key:0,class:"dynamic-position-info"},Jt={class:"info-content"},jt={key:0,class:"form-group"},Zt={class:"slider-container"},$t=["min","max"],ea={class:"slider-value"},ta={class:"leverage-hint"},aa={class:"form-group"},sa={class:"radio-group"},oa={class:"radio-label"},la={key:0,class:"radio-label"},na={key:1,class:"radio-label"},ia={key:0,class:"direction-hint"},ra={key:1,class:"direction-hint"},ua={key:2},da={class:"form-group"},ca={class:"trading-pairs-selection"},va={class:"trading-pairs-header"},ga={class:"trading-pairs-container"},fa={key:0,class:"trading-pairs-group"},pa={class:"trading-pairs-list"},ma=["value"],Sa={key:1,class:"trading-pairs-group"},ba={class:"trading-pairs-list"},ya=["value"],Ta={key:2,class:"trading-pairs-group"},Aa={class:"trading-pairs-list"},Da=["value"],Pa={key:3,class:"trading-pairs-group"},Ua={class:"trading-pairs-list"},ka=["value"],ha={class:"selected-pairs-count"},_a={class:"form-group"},Ca={for:"batchAmount"},xa=["placeholder","min","max"],wa={key:0,class:"amount-hint"},Ia={key:1,class:"dynamic-position-info"},Oa={class:"info-content"},Wa={key:0,class:"form-group"},Ra={class:"slider-container"},Ma=["min","max"],Na={class:"slider-value"},Ka={key:0,class:"leverage-hint"},Ba={class:"form-group"},Va={class:"checkbox-group"},La={class:"checkbox-label"},Ea={key:0,class:"checkbox-label"},Fa={key:1,class:"checkbox-label"},Ha={key:0,class:"direction-hint"},Xa={class:"form-group"},Ya=["disabled"],qa={key:1,class:"preview-section"},Ga={class:"preview-list"},Qa={key:0},za={key:1,class:"dynamic-note"},Ja={key:3,class:"error-message"},ja={class:"form-actions"},Za=["disabled"];function $a(D,e,m,t,S,P){return n(),i("div",ft,[e[66]||(e[66]=a("div",{class:"section-title"},"策略设置",-1)),t.loading?(n(),i("div",pt,e[24]||(e[24]=[a("div",{class:"spinner"},null,-1),a("div",{style:{"margin-left":"10px"}},"提交中...",-1)]))):(n(),i("div",mt,[a("div",St,[e[27]||(e[27]=a("label",null,"策略类型",-1)),a("div",bt,[a("label",yt,[k(a("input",{type:"radio","onUpdate:modelValue":e[0]||(e[0]=o=>t.strategyType=o),value:"spot"},null,512),[[J,t.strategyType]]),e[25]||(e[25]=a("span",null,"现货策略",-1))]),a("label",Tt,[k(a("input",{type:"radio","onUpdate:modelValue":e[1]||(e[1]=o=>t.strategyType=o),value:"futures"},null,512),[[J,t.strategyType]]),e[26]||(e[26]=a("span",null,"合约策略",-1))])])]),a("div",At,[e[30]||(e[30]=a("label",null,"创建模式",-1)),a("div",Dt,[a("label",Pt,[k(a("input",{type:"radio","onUpdate:modelValue":e[2]||(e[2]=o=>t.createMode=o),value:"single"},null,512),[[J,t.createMode]]),e[28]||(e[28]=a("span",null,"单个策略",-1))]),a("label",Ut,[k(a("input",{type:"radio","onUpdate:modelValue":e[3]||(e[3]=o=>t.createMode=o),value:"batch"},null,512),[[J,t.createMode]]),e[29]||(e[29]=a("span",null,"批量测量",-1))])])]),a("div",kt,[e[34]||(e[34]=a("label",{for:"strategyTemplate"},"策略模板",-1)),k(a("select",{id:"strategyTemplate","onUpdate:modelValue":e[4]||(e[4]=o=>t.strategyTemplate=o)},[e[33]||(e[33]=a("option",{value:""},"-- 选择策略模板 --",-1)),t.strategyType==="spot"&&t.availableSpotStrategies.length>0?(n(),i("optgroup",ht,[(n(!0),i(N,null,V(t.availableSpotStrategies,o=>(n(),i("option",{key:o.value,value:o.value},d(o.label),9,_t))),128))])):c("",!0),t.strategyType==="futures"&&t.availableFuturesStrategies.length>0?(n(),i("optgroup",Ct,[(n(!0),i(N,null,V(t.availableFuturesStrategies,o=>(n(),i("option",{key:o.value,value:o.value},d(o.label),9,xt))),128))])):c("",!0),t.strategyType==="spot"&&t.availableSpotStrategies.length===0?(n(),i("optgroup",wt,e[31]||(e[31]=[a("option",{disabled:""},"您当前的会员等级无可用的现货策略",-1)]))):c("",!0),t.strategyType==="futures"&&t.availableFuturesStrategies.length===0?(n(),i("optgroup",It,e[32]||(e[32]=[a("option",{disabled:""},"您当前的会员等级无可用的合约策略",-1)]))):c("",!0)],512),[[ie,t.strategyTemplate]])]),t.strategyTemplate?(n(),i("div",Ot,[a("h4",null,d(t.getStrategyTitle(t.strategyTemplate)),1),a("div",Wt,[a("span",Rt,d(t.strategyType==="spot"?"现货策略":"合约策略"),1),a("span",{class:B(["strategy-risk-badge",t.getRiskLevel()])},d(t.getRiskLevelText()),3)])])):c("",!0),a("div",Mt,[e[43]||(e[43]=a("label",{for:"symbol"},"交易对",-1)),k(a("select",{id:"symbol","onUpdate:modelValue":e[5]||(e[5]=o=>t.symbol=o)},[t.strategyType==="spot"?(n(),i("optgroup",Nt,e[35]||(e[35]=[F('<option value="BTC-USDT" data-v-a8029b0b>BTC-USDT (比特币)</option><option value="ETH-USDT" data-v-a8029b0b>ETH-USDT (以太坊)</option><option value="XRP-USDT" data-v-a8029b0b>XRP-USDT (瑞波币)</option><option value="LTC-USDT" data-v-a8029b0b>LTC-USDT (莱特币)</option><option value="BCH-USDT" data-v-a8029b0b>BCH-USDT (比特币现金)</option><option value="EOS-USDT" data-v-a8029b0b>EOS-USDT (柚子)</option><option value="DOT-USDT" data-v-a8029b0b>DOT-USDT (波卡)</option><option value="ADA-USDT" data-v-a8029b0b>ADA-USDT (艾达币)</option>',8)]))):c("",!0),t.strategyType==="spot"?(n(),i("optgroup",Kt,e[36]||(e[36]=[F('<option value="UNI-USDT" data-v-a8029b0b>UNI-USDT (Uniswap)</option><option value="AAVE-USDT" data-v-a8029b0b>AAVE-USDT (Aave)</option><option value="COMP-USDT" data-v-a8029b0b>COMP-USDT (Compound)</option><option value="SUSHI-USDT" data-v-a8029b0b>SUSHI-USDT (SushiSwap)</option><option value="CRV-USDT" data-v-a8029b0b>CRV-USDT (Curve)</option><option value="SNX-USDT" data-v-a8029b0b>SNX-USDT (Synthetix)</option><option value="MKR-USDT" data-v-a8029b0b>MKR-USDT (Maker)</option><option value="YFI-USDT" data-v-a8029b0b>YFI-USDT (Yearn.Finance)</option>',8)]))):c("",!0),t.strategyType==="spot"?(n(),i("optgroup",Bt,e[37]||(e[37]=[F('<option value="MATIC-USDT" data-v-a8029b0b>MATIC-USDT (Polygon)</option><option value="SOL-USDT" data-v-a8029b0b>SOL-USDT (Solana)</option><option value="AVAX-USDT" data-v-a8029b0b>AVAX-USDT (Avalanche)</option><option value="NEAR-USDT" data-v-a8029b0b>NEAR-USDT (Near Protocol)</option><option value="FTM-USDT" data-v-a8029b0b>FTM-USDT (Fantom)</option><option value="ATOM-USDT" data-v-a8029b0b>ATOM-USDT (Cosmos)</option><option value="ALGO-USDT" data-v-a8029b0b>ALGO-USDT (Algorand)</option><option value="ONE-USDT" data-v-a8029b0b>ONE-USDT (Harmony)</option>',8)]))):c("",!0),t.strategyType==="spot"?(n(),i("optgroup",Vt,e[38]||(e[38]=[F('<option value="DOGE-USDT" data-v-a8029b0b>DOGE-USDT (狗狗币)</option><option value="SHIB-USDT" data-v-a8029b0b>SHIB-USDT (柴犬币)</option><option value="LINK-USDT" data-v-a8029b0b>LINK-USDT (Chainlink)</option><option value="FIL-USDT" data-v-a8029b0b>FIL-USDT (Filecoin)</option><option value="VET-USDT" data-v-a8029b0b>VET-USDT (唯链)</option><option value="THETA-USDT" data-v-a8029b0b>THETA-USDT (Theta)</option><option value="XLM-USDT" data-v-a8029b0b>XLM-USDT (恒星币)</option><option value="TRX-USDT" data-v-a8029b0b>TRX-USDT (波场)</option>',8)]))):c("",!0),t.strategyType==="futures"?(n(),i("optgroup",Lt,e[39]||(e[39]=[F('<option value="BTC-USDT-SWAP" data-v-a8029b0b>BTC-USDT-SWAP (比特币永续)</option><option value="ETH-USDT-SWAP" data-v-a8029b0b>ETH-USDT-SWAP (以太坊永续)</option><option value="XRP-USDT-SWAP" data-v-a8029b0b>XRP-USDT-SWAP (瑞波币永续)</option><option value="LTC-USDT-SWAP" data-v-a8029b0b>LTC-USDT-SWAP (莱特币永续)</option><option value="BCH-USDT-SWAP" data-v-a8029b0b>BCH-USDT-SWAP (比特币现金永续)</option><option value="EOS-USDT-SWAP" data-v-a8029b0b>EOS-USDT-SWAP (柚子永续)</option><option value="DOT-USDT-SWAP" data-v-a8029b0b>DOT-USDT-SWAP (波卡永续)</option><option value="ADA-USDT-SWAP" data-v-a8029b0b>ADA-USDT-SWAP (艾达币永续)</option>',8)]))):c("",!0),t.strategyType==="futures"?(n(),i("optgroup",Et,e[40]||(e[40]=[F('<option value="UNI-USDT-SWAP" data-v-a8029b0b>UNI-USDT-SWAP (Uniswap永续)</option><option value="AAVE-USDT-SWAP" data-v-a8029b0b>AAVE-USDT-SWAP (Aave永续)</option><option value="COMP-USDT-SWAP" data-v-a8029b0b>COMP-USDT-SWAP (Compound永续)</option><option value="SUSHI-USDT-SWAP" data-v-a8029b0b>SUSHI-USDT-SWAP (SushiSwap永续)</option><option value="CRV-USDT-SWAP" data-v-a8029b0b>CRV-USDT-SWAP (Curve永续)</option><option value="SNX-USDT-SWAP" data-v-a8029b0b>SNX-USDT-SWAP (Synthetix永续)</option><option value="MKR-USDT-SWAP" data-v-a8029b0b>MKR-USDT-SWAP (Maker永续)</option><option value="YFI-USDT-SWAP" data-v-a8029b0b>YFI-USDT-SWAP (Yearn.Finance永续)</option>',8)]))):c("",!0),t.strategyType==="futures"?(n(),i("optgroup",Ft,e[41]||(e[41]=[F('<option value="MATIC-USDT-SWAP" data-v-a8029b0b>MATIC-USDT-SWAP (Polygon永续)</option><option value="SOL-USDT-SWAP" data-v-a8029b0b>SOL-USDT-SWAP (Solana永续)</option><option value="AVAX-USDT-SWAP" data-v-a8029b0b>AVAX-USDT-SWAP (Avalanche永续)</option><option value="NEAR-USDT-SWAP" data-v-a8029b0b>NEAR-USDT-SWAP (Near Protocol永续)</option><option value="FTM-USDT-SWAP" data-v-a8029b0b>FTM-USDT-SWAP (Fantom永续)</option><option value="ATOM-USDT-SWAP" data-v-a8029b0b>ATOM-USDT-SWAP (Cosmos永续)</option><option value="ALGO-USDT-SWAP" data-v-a8029b0b>ALGO-USDT-SWAP (Algorand永续)</option><option value="ONE-USDT-SWAP" data-v-a8029b0b>ONE-USDT-SWAP (Harmony永续)</option>',8)]))):c("",!0),t.strategyType==="futures"?(n(),i("optgroup",Ht,e[42]||(e[42]=[F('<option value="DOGE-USDT-SWAP" data-v-a8029b0b>DOGE-USDT-SWAP (狗狗币永续)</option><option value="SHIB-USDT-SWAP" data-v-a8029b0b>SHIB-USDT-SWAP (柴犬币永续)</option><option value="LINK-USDT-SWAP" data-v-a8029b0b>LINK-USDT-SWAP (Chainlink永续)</option><option value="FIL-USDT-SWAP" data-v-a8029b0b>FIL-USDT-SWAP (Filecoin永续)</option><option value="VET-USDT-SWAP" data-v-a8029b0b>VET-USDT-SWAP (唯链永续)</option><option value="THETA-USDT-SWAP" data-v-a8029b0b>THETA-USDT-SWAP (Theta永续)</option><option value="XLM-USDT-SWAP" data-v-a8029b0b>XLM-USDT-SWAP (恒星币永续)</option><option value="TRX-USDT-SWAP" data-v-a8029b0b>TRX-USDT-SWAP (波场永续)</option>',8)]))):c("",!0)],512),[[ie,t.symbol]])]),t.createMode==="single"?(n(),i("div",Xt,[a("div",Yt,[a("label",qt,d(t.getAmountLabel()),1),k(a("input",{type:"number",id:"amount","onUpdate:modelValue":e[6]||(e[6]=o=>t.amount=o),placeholder:t.getAmountPlaceholder(),min:t.getAmountRange().min,max:t.getAmountRange().max},null,8,Gt),[[q,t.amount]]),a("div",Qt,[e[44]||(e[44]=a("i",{class:"hint-icon"},"ℹ️",-1)),a("span",null,d(t.getAmountHint()),1)]),t.strategyTemplate&&t.isDynamicPositionStrategy()?(n(),i("div",zt,[e[45]||(e[45]=a("div",{class:"info-title"},"💡 动态仓位计算说明",-1)),a("div",Jt,d(t.getDynamicPositionExplanation()),1)])):c("",!0)]),t.strategyType==="futures"?(n(),i("div",jt,[e[47]||(e[47]=a("label",{for:"leverage"},"杠杆倍数",-1)),a("div",Zt,[k(a("input",{type:"range",id:"leverage","onUpdate:modelValue":e[7]||(e[7]=o=>t.leverage=o),min:t.getLeverageRange().min,max:t.getLeverageRange().max,step:"1"},null,8,$t),[[q,t.leverage]]),a("div",ea,d(t.leverage)+"x",1)]),a("div",ta,[e[46]||(e[46]=a("i",{class:"hint-icon"},"ℹ️",-1)),a("span",null,d(t.getLeverageHint()),1)])])):c("",!0),a("div",aa,[e[52]||(e[52]=a("label",null,"策略方向",-1)),a("div",sa,[a("label",oa,[k(a("input",{type:"radio","onUpdate:modelValue":e[8]||(e[8]=o=>t.direction=o),value:"long"},null,512),[[J,t.direction]]),a("span",null,d(t.strategyType==="spot"?"买入":"做多"),1)]),t.strategyType==="futures"?(n(),i("label",la,[k(a("input",{type:"radio","onUpdate:modelValue":e[9]||(e[9]=o=>t.direction=o),value:"short"},null,512),[[J,t.direction]]),e[48]||(e[48]=a("span",null,"做空",-1))])):c("",!0),t.strategyType==="futures"?(n(),i("label",na,[k(a("input",{type:"radio","onUpdate:modelValue":e[10]||(e[10]=o=>t.direction=o),value:"both"},null,512),[[J,t.direction]]),e[49]||(e[49]=a("span",null,"自动判断",-1))])):c("",!0)]),t.direction==="both"?(n(),i("div",ia,e[50]||(e[50]=[a("i",{class:"hint-icon"},"ℹ️",-1),a("span",null,"自动判断模式：系统将根据策略条件自动判断做多或做空，而不是同时开多空两个单子。",-1)]))):c("",!0),t.strategyType==="spot"?(n(),i("div",ra,e[51]||(e[51]=[a("i",{class:"hint-icon"},"ℹ️",-1),a("span",null,"现货交易只能买入持有，不支持做空操作。",-1)]))):c("",!0)])])):t.createMode==="batch"?(n(),i("div",ua,[a("div",da,[e[57]||(e[57]=a("label",null,"选择交易对",-1)),a("div",ca,[a("div",va,[a("button",{class:"select-all-button",onClick:e[11]||(e[11]=(...o)=>t.toggleAllTradingPairs&&t.toggleAllTradingPairs(...o))},d(t.isAllTradingPairsSelected?"取消全选":"全选"),1)]),a("div",ga,[t.strategyType==="spot"?(n(),i("div",fa,[e[53]||(e[53]=a("div",{class:"trading-pairs-group-title"},"主流币种",-1)),a("div",pa,[(n(!0),i(N,null,V(t.spotMainPairs,o=>(n(),i("label",{key:o.value,class:"trading-pair-checkbox"},[k(a("input",{type:"checkbox","onUpdate:modelValue":e[12]||(e[12]=b=>t.batchParams.tradingPairs=b),value:o.value},null,8,ma),[[Y,t.batchParams.tradingPairs]]),a("span",null,d(o.label),1)]))),128))])])):c("",!0),t.strategyType==="spot"?(n(),i("div",Sa,[e[54]||(e[54]=a("div",{class:"trading-pairs-group-title"},"DeFi币种",-1)),a("div",ba,[(n(!0),i(N,null,V(t.spotDefiPairs,o=>(n(),i("label",{key:o.value,class:"trading-pair-checkbox"},[k(a("input",{type:"checkbox","onUpdate:modelValue":e[13]||(e[13]=b=>t.batchParams.tradingPairs=b),value:o.value},null,8,ya),[[Y,t.batchParams.tradingPairs]]),a("span",null,d(o.label),1)]))),128))])])):c("",!0),t.strategyType==="futures"?(n(),i("div",Ta,[e[55]||(e[55]=a("div",{class:"trading-pairs-group-title"},"主流合约",-1)),a("div",Aa,[(n(!0),i(N,null,V(t.futuresMainPairs,o=>(n(),i("label",{key:o.value,class:"trading-pair-checkbox"},[k(a("input",{type:"checkbox","onUpdate:modelValue":e[14]||(e[14]=b=>t.batchParams.tradingPairs=b),value:o.value},null,8,Da),[[Y,t.batchParams.tradingPairs]]),a("span",null,d(o.label),1)]))),128))])])):c("",!0),t.strategyType==="futures"?(n(),i("div",Pa,[e[56]||(e[56]=a("div",{class:"trading-pairs-group-title"},"DeFi合约",-1)),a("div",Ua,[(n(!0),i(N,null,V(t.futuresDefiPairs,o=>(n(),i("label",{key:o.value,class:"trading-pair-checkbox"},[k(a("input",{type:"checkbox","onUpdate:modelValue":e[15]||(e[15]=b=>t.batchParams.tradingPairs=b),value:o.value},null,8,ka),[[Y,t.batchParams.tradingPairs]]),a("span",null,d(o.label),1)]))),128))])])):c("",!0)]),a("div",ha," 已选择 "+d(t.batchParams.tradingPairs.length)+" 个交易对 ",1)])]),a("div",_a,[a("label",Ca,d(t.getAmountLabel()),1),k(a("input",{type:"number",id:"batchAmount","onUpdate:modelValue":e[16]||(e[16]=o=>t.amount=o),placeholder:t.getAmountPlaceholder(),min:t.getAmountRange().min,max:t.getAmountRange().max},null,8,xa),[[q,t.amount]]),t.strategyTemplate?(n(),i("div",wa,[e[58]||(e[58]=a("i",{class:"hint-icon"},"ℹ️",-1)),a("span",null,d(t.getAmountHint()),1)])):c("",!0),t.strategyTemplate&&t.isDynamicPositionStrategy()?(n(),i("div",Ia,[e[59]||(e[59]=a("div",{class:"info-title"},"💡 动态仓位计算说明",-1)),a("div",Oa,d(t.getDynamicPositionExplanation()),1)])):c("",!0)]),t.strategyType==="futures"?(n(),i("div",Wa,[e[61]||(e[61]=a("label",{for:"batchLeverage"},"杠杆倍数",-1)),a("div",Ra,[k(a("input",{type:"range",id:"batchLeverage","onUpdate:modelValue":e[17]||(e[17]=o=>t.leverage=o),min:t.getLeverageRange().min,max:t.getLeverageRange().max,step:"1"},null,8,Ma),[[q,t.leverage]]),a("div",Na,d(t.leverage)+"x",1)]),t.strategyTemplate?(n(),i("div",Ka,[e[60]||(e[60]=a("i",{class:"hint-icon"},"ℹ️",-1)),a("span",null,d(t.getLeverageHint()),1)])):c("",!0)])):c("",!0),a("div",Ba,[e[65]||(e[65]=a("label",null,"交易方向",-1)),a("div",Va,[a("label",La,[k(a("input",{type:"checkbox","onUpdate:modelValue":e[18]||(e[18]=o=>t.batchParams.directions=o),value:"long"},null,512),[[Y,t.batchParams.directions]]),a("span",null,d(t.strategyType==="spot"?"买入":"做多"),1)]),t.strategyType==="futures"?(n(),i("label",Ea,[k(a("input",{type:"checkbox","onUpdate:modelValue":e[19]||(e[19]=o=>t.batchParams.directions=o),value:"short"},null,512),[[Y,t.batchParams.directions]]),e[62]||(e[62]=a("span",null,"做空",-1))])):c("",!0),t.strategyType==="futures"?(n(),i("label",Fa,[k(a("input",{type:"checkbox","onUpdate:modelValue":e[20]||(e[20]=o=>t.batchParams.directions=o),value:"both"},null,512),[[Y,t.batchParams.directions]]),e[63]||(e[63]=a("span",null,"自动判断",-1))])):c("",!0)]),t.strategyType==="spot"?(n(),i("div",Ha,e[64]||(e[64]=[a("i",{class:"hint-icon"},"ℹ️",-1),a("span",null,"现货交易只能买入持有，不支持做空操作。",-1)]))):c("",!0)]),a("div",Xa,[a("label",null,"预计创建策略数量: "+d(t.calculateTotalStrategies()),1),a("button",{class:"preview-button",onClick:e[21]||(e[21]=(...o)=>t.previewBatchStrategies&&t.previewBatchStrategies(...o)),disabled:t.calculateTotalStrategies()===0}," 预览策略列表 ",8,Ya)]),t.showPreview?(n(),i("div",qa,[a("h4",null,"预览策略列表 ("+d(t.batchStrategies.length)+"个)",1),a("div",Ga,[(n(!0),i(N,null,V(t.batchStrategies,(o,b)=>(n(),i("div",{key:b,class:"preview-item"},[a("span",null,d(b+1)+". "+d(t.strategyType==="spot"?"现货":"合约")+" - "+d(t.getStrategyTitle(t.strategyTemplate)),1),a("span",null,"交易对: "+d(o.symbol),1),a("span",null,d(t.getAmountLabel())+": "+d(o.amount)+"USDT",1),t.strategyType==="futures"?(n(),i("span",Qa,"杠杆: "+d(o.leverage)+"x",1)):c("",!0),a("span",null,"方向: "+d(t.getDirectionText(o.direction)),1),t.isDynamicPositionStrategy()?(n(),i("span",za,"（动态仓位计算）")):c("",!0)]))),128))])])):c("",!0)])):c("",!0),t.error?(n(),i("div",Ja,d(t.error),1)):c("",!0),a("div",ja,[t.createMode==="single"?(n(),i("button",{key:0,class:"submit-button",onClick:e[22]||(e[22]=(...o)=>t.submitStrategy&&t.submitStrategy(...o))}," 创建策略 ")):t.createMode==="batch"?(n(),i("button",{key:1,class:"submit-button",onClick:e[23]||(e[23]=(...o)=>t.submitBatchStrategies&&t.submitBatchStrategies(...o)),disabled:!t.showPreview||t.batchStrategies.length===0}," 批量创建策略 ",8,Za)):c("",!0)])]))])}const es=re(gt,[["render",$a],["__scopeId","data-v-a8029b0b"]]);const ts={name:"OkxOrderHistory",props:{apiConnected:{type:Boolean,default:!1},userId:{type:String,default:"default"},visible:{type:Boolean,default:!1}},setup(D){const e=y([]),m=y(!1),t=y(""),S=y(1),P=10,o=Re({instType:"SPOT",state:"filled"}),b=v=>v?parseFloat(v).toFixed(4):"0",W=v=>v?new Date(parseInt(v)).toLocaleString():"-",h=v=>({market:"市价单",limit:"限价单",post_only:"只做挂单",fok:"全部成交或立即取消",ioc:"立即成交并取消剩余"})[v]||v,l=v=>({canceled:"已撤销",live:"未成交",partially_filled:"部分成交",filled:"已成交"})[v]||v,_=async()=>{var v,C;if(!D.apiConnected){t.value="请先连接OKX API";return}m.value=!0,t.value="";try{const x=await M.get("/okx/orders",{params:{userId:D.userId,instType:o.instType,state:o.state,limit:P,after:(S.value-1)*P}});x.data.success?e.value=x.data.orders||[]:t.value=x.data.error||"获取历史订单失败"}catch(x){console.error("获取历史订单失败:",x),t.value=((C=(v=x.response)==null?void 0:v.data)==null?void 0:C.error)||"获取历史订单失败"}finally{m.value=!1}},T=v=>{S.value=v,_()};return ne(()=>D.visible,v=>{v&&D.apiConnected&&_()}),we(()=>{D.visible&&D.apiConnected&&_()}),{orders:e,loading:m,error:t,filters:o,currentPage:S,pageSize:P,formatNumber:b,formatDate:W,getOrderType:h,getOrderStatus:l,fetchOrders:_,changePage:T}}},as={class:"order-history"},ss={key:0,class:"loading-spinner"},os={key:1,class:"error-message"},ls={key:2},ns={class:"filter-section"},is={class:"form-group"},rs={class:"form-group"},us={key:0,class:"orders-table"},ds={key:1,class:"no-orders"},cs={key:2,class:"pagination"},vs=["disabled"],gs={class:"page-info"},fs=["disabled"];function ps(D,e,m,t,S,P){return n(),i("div",as,[e[12]||(e[12]=a("div",{class:"section-title"},"历史订单",-1)),t.loading?(n(),i("div",ss,e[6]||(e[6]=[a("div",{class:"spinner"},null,-1),a("div",{style:{"margin-left":"10px"}},"加载历史订单...",-1)]))):t.error?(n(),i("div",os,d(t.error),1)):(n(),i("div",ls,[a("div",ns,[a("div",is,[e[8]||(e[8]=a("label",{for:"instType"},"产品类型",-1)),k(a("select",{id:"instType","onUpdate:modelValue":e[0]||(e[0]=o=>t.filters.instType=o),onChange:e[1]||(e[1]=(...o)=>t.fetchOrders&&t.fetchOrders(...o))},e[7]||(e[7]=[a("option",{value:"SPOT"},"现货",-1),a("option",{value:"MARGIN"},"杠杆",-1),a("option",{value:"SWAP"},"永续合约",-1),a("option",{value:"FUTURES"},"交割合约",-1)]),544),[[ie,t.filters.instType]])]),a("div",rs,[e[10]||(e[10]=a("label",{for:"state"},"订单状态",-1)),k(a("select",{id:"state","onUpdate:modelValue":e[2]||(e[2]=o=>t.filters.state=o),onChange:e[3]||(e[3]=(...o)=>t.fetchOrders&&t.fetchOrders(...o))},e[9]||(e[9]=[a("option",{value:"filled"},"已成交",-1),a("option",{value:"canceled"},"已撤销",-1),a("option",{value:"live"},"未成交",-1)]),544),[[ie,t.filters.state]])])]),t.orders.length>0?(n(),i("div",us,[a("table",null,[e[11]||(e[11]=a("thead",null,[a("tr",null,[a("th",null,"交易对"),a("th",null,"类型"),a("th",null,"方向"),a("th",null,"价格"),a("th",null,"数量"),a("th",null,"成交量"),a("th",null,"状态"),a("th",null,"创建时间")])],-1)),a("tbody",null,[(n(!0),i(N,null,V(t.orders,(o,b)=>(n(),i("tr",{key:b},[a("td",null,d(o.instId),1),a("td",null,d(t.getOrderType(o.ordType)),1),a("td",{class:B(o.side==="buy"?"buy":"sell")},d(o.side==="buy"?"买入":"卖出"),3),a("td",null,d(t.formatNumber(o.px)),1),a("td",null,d(t.formatNumber(o.sz)),1),a("td",null,d(t.formatNumber(o.accFillSz)),1),a("td",null,d(t.getOrderStatus(o.state)),1),a("td",null,d(t.formatDate(o.cTime)),1)]))),128))])])])):(n(),i("div",ds," 暂无历史订单 ")),t.orders.length>0?(n(),i("div",cs,[a("button",{class:"pagination-button",disabled:t.currentPage===1,onClick:e[4]||(e[4]=o=>t.changePage(t.currentPage-1))}," 上一页 ",8,vs),a("span",gs,"第 "+d(t.currentPage)+" 页",1),a("button",{class:"pagination-button",disabled:t.orders.length<t.pageSize,onClick:e[5]||(e[5]=o=>t.changePage(t.currentPage+1))}," 下一页 ",8,fs)])):c("",!0)]))])}const ms=re(ts,[["render",ps],["__scopeId","data-v-534e1e0c"]]);const Ss={name:"AccountInfoPanel",setup(){const D=y(!1),e=y(null),m=y("");return{loadingBalance:D,balanceData:e,balanceError:m,getBalance:async()=>{var S,P;D.value=!0,m.value="",e.value=null;try{const o=await M.get("/python/okx/balance");console.log("余额响应:",o.data),o.data.success&&o.data.balance?e.value=o.data.balance:m.value=o.data.error||"获取余额失败"}catch(o){console.error("获取余额错误:",o),m.value=((P=(S=o.response)==null?void 0:S.data)==null?void 0:P.error)||"获取余额请求失败"}finally{D.value=!1}}}}},bs={class:"python-trade-panel"},ys={class:"panel-section"},Ts=["disabled"],As={key:0,class:"error-message"},Ds={key:1,class:"balance-data"};function Ps(D,e,m,t,S,P){return n(),i("div",bs,[a("div",ys,[e[1]||(e[1]=a("h4",null,"账户余额",-1)),a("button",{onClick:e[0]||(e[0]=(...o)=>t.getBalance&&t.getBalance(...o)),disabled:t.loadingBalance},d(t.loadingBalance?"加载中...":"获取余额"),9,Ts),t.balanceError?(n(),i("div",As,d(t.balanceError),1)):c("",!0),t.balanceData?(n(),i("div",Ds,[(n(!0),i(N,null,V(t.balanceData,(o,b)=>(n(),i("div",{key:b,class:"balance-item"},[a("strong",null,d(b)+":",1),a("span",null,"可用: "+d(o.free)+", 冻结: "+d(o.used)+", 总计: "+d(o.total),1)]))),128))])):c("",!0)])])}const Us=re(Ss,[["render",Ps],["__scopeId","data-v-f9aaf92c"]]);const ks={name:"OkxView",components:{OkxApiModal:vt,OkxStrategyForm:es,OkxOrderHistory:ms,AccountInfoPanel:Us},setup(){const D=y(!1),e=y(""),m=y(!1),t=y(!1),S=y([]),P=y(!1),o=y("card"),b=y("all"),W=y(""),h=y(!1),l=y(!1),_=y(!1),T=y(!1),v=y([]),C=y(!1),x=y(!1),G=y(!1),Q=y(!1);let w=null;const te=R(()=>S.value.filter(s=>s.status==="waiting"||s.status==="active").length),Se=R(()=>S.value.filter(s=>s.status==="completed").length),be=R(()=>[...S.value].sort((s,r)=>{const f=s.status==="waiting"||s.status==="active",p=r.status==="waiting"||r.status==="active";return f&&!p?-1:!f&&p?1:new Date(r.createdAt||0)-new Date(s.createdAt||0)})),H=R(()=>{let s=be.value;if(b.value==="running"?s=s.filter(r=>r.status==="waiting"||r.status==="active"):b.value==="completed"&&(s=s.filter(r=>r.status==="completed")),W.value.trim()){const r=W.value.toLowerCase().trim();s=s.filter(f=>f.strategyName&&typeof f.strategyName=="string"&&f.strategyName.toLowerCase().includes(r)||f.symbol&&typeof f.symbol=="string"&&f.symbol.toLowerCase().includes(r)||f.type&&typeof f.type=="string"&&f.type.toLowerCase().includes(r))}return s}),ue=R(()=>H.value.length>0&&v.value.length===H.value.length),ae=R(()=>v.value.length>0&&v.value.length<H.value.length),j=R(()=>H.value.filter(s=>v.value.includes(s.id||s._id)&&(s.status==="waiting"||s.status==="active"))),L=R(()=>H.value.filter(s=>v.value.includes(s.id||s._id)&&s.status!=="waiting"&&s.status!=="active")),de=R(()=>j.value.length),Z=R(()=>L.value.length),ye=R(()=>de.value>0),$=R(()=>Z.value>0),Te=s=>s?new Date(s).toLocaleString():"-",Ae=s=>s.status==="waiting"||s.status==="active",De=()=>W.value.trim()?"未找到匹配的策略":b.value==="running"?"暂无运行中的策略":b.value==="completed"?"暂无已完成的策略":"暂无策略",se=()=>{window.scrollTo({top:0,behavior:"smooth"}),l.value=!1},Pe=s=>{var f;let r=null;switch(s){case"account-info":r=document.querySelector(".api-status-section")||document.querySelector(".account-info-panel");break;case"strategy-form":r=document.querySelector(".strategy-form");break;case"strategies-list":r=document.querySelector(".active-strategies");break;case"running-strategies":r=((f=document.querySelector(".strategy-card .status-active, .strategy-card .status-waiting"))==null?void 0:f.closest(".strategy-card"))||document.querySelector(".compact-strategy-row.running-strategy");break;default:return}r&&r.scrollIntoView({behavior:"smooth",block:"start"}),l.value=!1},oe=()=>{h.value=window.scrollY>300,l.value&&window.scrollY>0&&(l.value=!1)},Ue=()=>{t.value=!t.value,t.value&&setTimeout(()=>{const s=document.getElementById("order-history-section");s&&s.scrollIntoView({behavior:"smooth",block:"start"})},100)},ke=()=>{localStorage.setItem("userId",e.value),localStorage.setItem(`apiConnected_${e.value}`,D.value.toString()),console.log("用户状态已保存到本地存储，用户ID:",e.value)},he=async()=>{const s=localStorage.getItem("user");if(s)try{const r=JSON.parse(s);r&&r.uid&&(console.log("从登录用户信息加载用户UID:",r.uid),e.value=r.uid)}catch(r){console.error("解析用户信息失败:",r)}if(!e.value){const r=localStorage.getItem("userId");r?(console.log("从本地存储加载用户ID:",r),e.value=r):(console.log("未找到用户ID，使用默认值"),e.value="default")}await ce()},ce=async()=>{try{console.log("检查API密钥状态...");const s=await M.get("/okx/api-keys");s.data.success&&s.data.hasApiKeys?(console.log("✅ 真实OKX API密钥已配置:",s.data.apiKeyPreview),D.value=!0,localStorage.setItem(`apiConnected_${e.value}`,"true"),le(),await K()):(console.log("❌ 真实OKX API密钥未配置，必须配置才能使用真实交易功能"),D.value=!1,localStorage.setItem(`apiConnected_${e.value}`,"false"),P.value=!1,m.value=!0,ElMessage.warning("本系统仅支持真实交易，请配置有效的OKX API密钥"))}catch(s){console.error("检查API密钥状态失败:",s),localStorage.getItem(`apiConnected_${e.value}`)==="true"?(console.log("从本地存储加载API连接状态: 已连接"),D.value=!0,le()):(console.log("API未连接，需要用户输入API密钥"),D.value=!1,P.value=!1,m.value=!0)}},_e=async s=>{console.log("API验证成功，更新状态"),D.value=!0,m.value=!1,P.value=!1,localStorage.setItem(`apiConnected_${e.value}`,"true"),ke(),le(),S.value=[],await K()},Ce=s=>{S.value.unshift({...s,createdAt:Date.now()})},le=()=>{console.log("正在连接WebSocket...");const s="https://api.frp-end.com";console.log("使用WebSocket URL:",s),w=Qe(s,{transports:["websocket","polling"],reconnectionAttempts:5,reconnectionDelay:1e3,timeout:2e4}),w.on("connect",()=>{console.log("WebSocket connected"),w.emit("subscribeOkx",{userId:e.value})}),w.on("okxUpdate",r=>{console.log("收到OKX更新:",r)}),w.on("okxAccountInit",r=>{console.log("收到OKX账户初始化数据:",r)}),w.on("disconnect",()=>{console.log("WebSocket disconnected")})},K=async()=>{try{console.log("正在获取策略列表，使用测试API端点");const s=await M.get("/test-strategies");console.log("测试API响应:",s.data),s.data&&s.data.success&&s.data.strategies?(console.log(`获取到 ${s.data.strategies.length} 个策略`),s.data.strategies.forEach((r,f)=>{console.log(`策略 ${f+1}:`,{id:r.id||r._id,name:r.strategyName,status:r.status,type:r.type,currentPrice:r.currentPrice,profit:r.profit,profitPercentage:r.profitPercentage,orders:r.orders?r.orders.length:0,entryPrice:r.entryPrice})}),S.value=s.data.strategies):console.error("获取策略列表失败: 返回数据格式不正确",s.data)}catch(s){console.error("获取策略列表失败:",s),s.response&&console.error("错误响应:",s.response.data)}},u=async s=>{var r,f;try{if(!s){console.error("策略ID不能为空"),alert("策略ID不能为空");return}console.log("正在停止策略:",s);const p=await M.post(`/strategies/${s}/stop`);if(console.log("停止策略响应:",p.data),p.data&&p.data.success){console.log("策略停止成功"),alert("策略已停止");const U=S.value.findIndex(O=>O.id===s||O._id===s);U!==-1&&(S.value[U].status="completed",console.log("本地策略状态已更新为completed")),await K()}else console.error("停止策略失败:",p.data.error||"未知错误"),alert("停止策略失败: "+(p.data.error||"未知错误"))}catch(p){console.error("停止策略失败:",p),alert("停止策略失败: "+(((f=(r=p.response)==null?void 0:r.data)==null?void 0:f.error)||p.message||"网络错误"))}},g=async s=>{var r,f;try{if(!s){console.error("策略ID不能为空");return}if(!confirm("确定要恢复此策略吗？系统将重新启动策略监控，继续执行交易。"))return;console.log("正在恢复策略:",s);const p=await M.post(`/strategies/${s}/recover`);if(console.log("恢复策略响应:",p.data),p.data&&p.data.success){console.log("策略恢复成功"),alert("策略已恢复，正在重新启动监控");const U=S.value.findIndex(O=>O.id===s||O._id===s);U!==-1&&(S.value[U].status="waiting",console.log("本地策略状态已更新为waiting")),await K()}else console.error("恢复策略失败:",p.data.error||"未知错误"),alert("恢复策略失败: "+(p.data.error||"未知错误"))}catch(p){console.error("恢复策略失败:",p),alert("恢复策略失败: "+(((f=(r=p.response)==null?void 0:r.data)==null?void 0:f.error)||p.message||"网络错误"))}},A=async s=>{try{if(!s){console.error("策略ID不能为空");return}if(!confirm("确定要删除此策略吗？此操作不可撤销。"))return;console.log("删除策略:",s);const r=await M.delete(`/strategies/${s}`);r.data&&r.data.success?(console.log("删除策略成功"),await K()):console.error("删除策略失败:",r.data.error)}catch(r){console.error("删除策略失败:",r),r.response&&console.error("错误响应:",r.response.data)}},X=s=>{switch(s){case"waiting":return"等待中";case"active":return"运行中";case"completed":return"已完成";case"executing":return"执行中";case"error":return"错误";default:return s||"未知"}},z=s=>{switch(s){case"waiting":return"status-waiting";case"active":return"status-active";case"completed":return"status-completed";case"executing":return"status-executing";case"error":return"status-error";default:return""}},Me=(s,r)=>{switch(s){case"long":return r==="spot"?"买入":"做多";case"short":return"做空";case"both":return"自动判断";default:return s||"未知"}},Ne=s=>Ie(s)?"总资金":"开仓金额",Ie=s=>s&&["futures1","futures2","futures3","futures4"].includes(s.strategyTemplate),Ke=()=>{console.log("用户请求重新配置API"),confirm("确定要重新配置API密钥吗？这将断开当前连接并需要重新输入API信息。")&&(w&&(w.disconnect(),w=null),D.value=!1,localStorage.setItem(`apiConnected_${e.value}`,"false"),S.value=[],P.value=!0,m.value=!0,console.log("API状态已重置，等待用户重新配置"))},Be=async()=>{var s,r;try{T.value=!0,console.log("开始删除OKX API配置...");const f=S.value.filter(U=>U.status==="waiting"||U.status==="active");if(f.length>0){console.log(`正在停止 ${f.length} 个运行中的策略...`);for(const U of f)try{await M.post(`/strategies/${U.id||U._id}/stop`)}catch(O){console.error(`停止策略 ${U.id||U._id} 失败:`,O)}}const p=await M.delete("/okx/api-keys");p.data.success?(console.log("OKX API配置删除成功"),w&&(w.disconnect(),w=null),D.value=!1,localStorage.setItem(`apiConnected_${e.value}`,"false"),S.value=[],_.value=!1,alert("OKX API配置已成功删除！")):(console.error("删除API配置失败:",p.data.error),alert("删除API配置失败: "+(p.data.error||"未知错误")))}catch(f){console.error("删除API配置失败:",f),alert("删除API配置失败: "+(((r=(s=f.response)==null?void 0:s.data)==null?void 0:r.error)||f.message||"网络错误"))}finally{T.value=!1}},Oe=s=>{if(!s)return"0.00";if(s.profit!==void 0&&s.profit!==null){const r=Number(s.profit);return isNaN(r)?"0.00":r.toFixed(2)}if(s.orders&&s.orders.length>0&&s.currentPrice){const r=s.orders.filter(ee=>ee.side==="buy");if(r.length===0)return"0.00";const f=r.reduce((ee,E)=>ee+Number(E.price)*Number(E.amount),0),O=r.reduce((ee,E)=>ee+Number(E.amount),0)*Number(s.currentPrice)-f;return isNaN(O)?"0.00":O.toFixed(2)}return"0.00"},Ve=s=>{if(!s)return"(0.00%)";if(s.profitPercentage!==void 0&&s.profitPercentage!==null){const r=Number(s.profitPercentage);return isNaN(r)?"(0.00%)":`(${r>0?"+":""}${r.toFixed(2)}%)`}if(s.orders&&s.orders.length>0&&s.currentPrice){const r=s.orders.filter(E=>E.side==="buy");if(r.length===0)return"(0.00%)";const f=r.reduce((E,ge)=>E+Number(ge.price)*Number(ge.amount),0),U=r.reduce((E,ge)=>E+Number(ge.amount),0)*Number(s.currentPrice);if(f===0)return"(0.00%)";const O=(U-f)/f*100;return isNaN(O)?"(0.00%)":`(${O>0?"+":""}${O.toFixed(2)}%)`}return"(0.00%)"},Le=s=>{const r=parseFloat(Oe(s));return r>0?"profit-positive":r<0?"profit-negative":""},Ee=s=>s?s.orders&&s.orders.length>0?s.orders.filter(f=>f.side==="buy").length>0:!!(s.entryPrice&&s.entryPrice>0):!1,Fe=async s=>{var r,f;try{if(!s){console.error("策略ID不能为空"),alert("策略ID不能为空");return}if(!confirm("确定要立即平仓所有持仓吗？此操作将卖出所有持有的币种。"))return;console.log("正在执行一键平仓:",s);const p=await M.post(`/strategies/${s}/close-position`);if(console.log("一键平仓响应:",p.data),p.data&&p.data.success){console.log("一键平仓成功"),p.data.autoFixed?alert(`平仓完成！

检测到数据不同步问题已自动修复：
- 实际余额为0，策略记录已更新
- 策略状态已设为完成`):alert("平仓成功！所有持仓已卖出。");const U=S.value.findIndex(O=>O.id===s||O._id===s);U!==-1&&(S.value[U].status="completed",console.log("本地策略状态已更新为completed")),await K()}else{console.error("一键平仓失败:",p.data.error||"未知错误");let U=p.data.error||"未知错误";U.includes("没有可平仓的持仓")&&U.includes("实际余额: 0")&&(U+=`

💡 建议：
1. 该币种可能已被手动卖出
2. 可以尝试删除此策略
3. 或联系技术支持处理数据同步问题`),alert("一键平仓失败: "+U)}}catch(p){console.error("一键平仓失败:",p),alert("一键平仓失败: "+(((f=(r=p.response)==null?void 0:r.data)==null?void 0:f.error)||p.message||"网络错误"))}},He=()=>{ue.value?v.value=[]:v.value=H.value.map(s=>s.id||s._id)},Xe=async()=>{var s,r;if(j.value.length===0){alert("没有选中的运行中策略");return}try{G.value=!0;const f=j.value.map(U=>U.id||U._id);console.log("批量停止策略:",f);const p=await M.post("/okx/strategies/batch-stop",{strategyIds:f});p.data&&p.data.success?(console.log("批量停止策略成功"),alert(`成功停止 ${p.data.stoppedCount} 个策略`),v.value=[],C.value=!1,await K()):(console.error("批量停止策略失败:",p.data.error),alert("批量停止策略失败: "+(p.data.error||"未知错误")))}catch(f){console.error("批量停止策略失败:",f),alert("批量停止策略失败: "+(((r=(s=f.response)==null?void 0:s.data)==null?void 0:r.error)||f.message||"网络错误"))}finally{G.value=!1}},Ye=async()=>{var s,r;if(L.value.length===0){alert("没有选中的可删除策略");return}try{Q.value=!0;const f=L.value.map(U=>U.id||U._id);console.log("批量删除策略:",f);const p=await M.post("/okx/strategies/batch-delete",{strategyIds:f});p.data&&p.data.success?(console.log("批量删除策略成功"),alert(`成功删除 ${p.data.deletedCount} 个策略`),v.value=[],x.value=!1,await K()):(console.error("批量删除策略失败:",p.data.error),alert("批量删除策略失败: "+(p.data.error||"未知错误")))}catch(f){console.error("批量删除策略失败:",f),alert("批量删除策略失败: "+(((r=(s=f.response)==null?void 0:s.data)==null?void 0:r.error)||f.message||"网络错误"))}finally{Q.value=!1}};let ve=null;return we(async()=>{console.log("组件挂载，开始初始化..."),await he(),console.log("初始化完成，策略数量:",S.value.length),ve=setInterval(async()=>{D.value&&S.value.length>0&&(console.log("定时刷新策略数据..."),await K())},3e4),window.addEventListener("scroll",oe)}),qe(()=>{w&&w.disconnect(),ve&&(clearInterval(ve),ve=null),window.removeEventListener("scroll",oe)}),{apiConnected:D,userId:e,showApiModal:m,showOrderHistory:t,strategies:S,isReconfiguring:P,viewMode:o,statusFilter:b,searchQuery:W,showScrollTop:h,showQuickNav:l,showDeleteApiConfirm:_,isDeletingApi:T,selectedStrategies:v,showBatchStopConfirm:C,showBatchDeleteConfirm:x,isBatchStopping:G,isBatchDeleting:Q,runningStrategiesCount:te,completedStrategiesCount:Se,filteredStrategies:H,isAllSelected:ue,isPartiallySelected:ae,selectedRunningStrategies:j,selectedNonRunningStrategies:L,runningSelectedCount:de,nonRunningSelectedCount:Z,hasRunningSelectedStrategies:ye,hasNonRunningSelectedStrategies:$,formatDate:Te,isRunningStrategy:Ae,getNoStrategiesText:De,scrollToTop:se,scrollToSection:Pe,toggleOrderHistory:Ue,onApiValidated:_e,onStrategyCreated:Ce,stopStrategy:u,recoverStrategy:g,deleteStrategy:A,getStatusText:X,getStatusClass:z,getDirectionText:Me,getAmountLabel:Ne,isDynamicPositionStrategy:Ie,fetchStrategies:K,reconfigureApi:Ke,deleteApi:Be,calculateProfitAmount:Oe,calculateProfitPercentage:Ve,getProfitClass:Le,hasPosition:Ee,closePosition:Fe,toggleSelectAll:He,batchStopStrategies:Xe,batchDeleteStrategies:Ye}}},hs={class:"okx-view"},_s={class:"main-content"},Cs={key:0,class:"api-status-section"},xs={class:"api-status-info"},ws={class:"api-actions"},Is=["disabled"],Os={key:1,class:"connect-api-section"},Ws={class:"card"},Rs={class:"card-content"},Ms={key:3,class:"strategy-form"},Ns={key:4,class:"active-strategies"},Ks={class:"section-header"},Bs={class:"view-controls"},Vs={class:"filter-controls"},Ls={class:"filter-group"},Es={key:0,class:"batch-actions"},Fs={class:"select-all-checkbox"},Hs=["checked","indeterminate"],Xs={class:"strategy-stats"},Ys={class:"stat-item"},qs={class:"stat-value running"},Gs={class:"stat-item"},Qs={class:"stat-value completed"},zs={class:"stat-item"},Js={class:"stat-value total"},js={class:"strategies-content"},Zs={key:0,class:"no-strategies"},$s={key:1,class:"strategies-list"},eo={class:"strategy-header"},to={class:"strategy-info"},ao={class:"strategy-name"},so={class:"strategy-meta"},oo={class:"strategy-type"},lo={class:"strategy-symbol"},no={class:"strategy-body"},io={class:"strategy-details"},ro={class:"detail-item"},uo={class:"detail-value"},co={class:"detail-item"},vo={class:"detail-value"},go={key:0,class:"detail-item"},fo={class:"strategy-actions"},po=["onClick"],mo=["onClick"],So={key:1,class:"strategy-status-info"},bo=["onClick"],yo=["onClick"],To={class:"delete-action"},Ao=["onClick"],Do={key:2,class:"strategies-compact-list"},Po={class:"compact-col checkbox-col"},Uo={class:"strategy-checkbox-compact"},ko=["value"],ho={class:"compact-col strategy-name-col"},_o={class:"strategy-name-compact"},Co={class:"strategy-type-compact"},xo={class:"compact-col"},wo={class:"symbol-text"},Io={class:"compact-col"},Oo={class:"compact-col"},Wo={class:"amount-text"},Ro={class:"compact-col"},Mo={key:1,class:"profit-text"},No={class:"compact-col"},Ko={class:"compact-actions"},Bo=["onClick"],Vo=["onClick"],Lo=["onClick"],Eo=["onClick"],Fo={key:0,id:"order-history-section"},Ho={key:0,class:"floating-actions"},Xo={class:"floating-btn-group"},Yo={class:"quick-nav-menu"},qo={class:"delete-confirm-modal"},Go={class:"modal-header"},Qo={class:"modal-footer"},zo=["disabled"],Jo={class:"batch-confirm-modal"},jo={class:"modal-header"},Zo={class:"modal-body"},$o={class:"warning-text"},el={class:"strategy-list-preview"},tl={class:"strategy-name"},al={class:"strategy-symbol"},sl={class:"strategy-status"},ol={class:"modal-footer"},ll=["disabled"],nl={class:"batch-confirm-modal"},il={class:"modal-header"},rl={class:"modal-body"},ul={class:"warning-text"},dl={class:"strategy-list-preview"},cl={class:"strategy-name"},vl={class:"strategy-symbol"},gl={class:"strategy-status"},fl={class:"modal-footer"},pl=["disabled"];function ml(D,e,m,t,S,P){const o=fe("AccountInfoPanel"),b=fe("OkxStrategyForm"),W=fe("OkxOrderHistory"),h=fe("OkxApiModal");return n(),i("div",hs,[e[79]||(e[79]=a("div",{class:"market-header"},[a("h2",null,"OKX 自动交易平台"),a("p",null,"专业的加密货币自动交易策略管理")],-1)),a("div",_s,[t.apiConnected?(n(),i("div",Cs,[a("div",xs,[e[33]||(e[33]=a("div",{class:"api-status-text"},[a("i",{class:"fas fa-check-circle"}),I(" OKX API 已连接 ")],-1)),a("div",ws,[a("button",{class:"reconfigure-button",onClick:e[0]||(e[0]=(...l)=>t.reconfigureApi&&t.reconfigureApi(...l))},e[31]||(e[31]=[a("i",{class:"fas fa-cog"},null,-1),I(" 重新配置 ")])),a("button",{class:"delete-api-button",onClick:e[1]||(e[1]=l=>t.showDeleteApiConfirm=!0),disabled:t.isDeletingApi},e[32]||(e[32]=[a("i",{class:"fas fa-trash"},null,-1),I(" 删除API ")]),8,Is)])])])):(n(),i("div",Os,[a("div",Ws,[a("div",Rs,[e[35]||(e[35]=a("h3",null,"连接OKX API",-1)),e[36]||(e[36]=a("p",null,"请先配置OKX API密钥以使用自动交易功能",-1)),a("button",{class:"connect-button",onClick:e[2]||(e[2]=l=>t.showApiModal=!0)},e[34]||(e[34]=[a("i",{class:"fas fa-plug"},null,-1),I(" 配置API密钥 ")]))])])])),t.apiConnected?(n(),Ge(o,{key:2,userId:t.userId,class:"account-info-panel"},null,8,["userId"])):c("",!0),t.apiConnected?(n(),i("div",Ms,[xe(b,{userId:t.userId,onStrategyCreated:t.fetchStrategies},null,8,["userId","onStrategyCreated"])])):c("",!0),t.apiConnected?(n(),i("div",Ns,[a("div",Ks,[e[39]||(e[39]=a("h3",{class:"section-title"},"策略管理",-1)),a("div",Bs,[a("button",{class:B(["view-btn",{active:t.viewMode==="card"}]),onClick:e[3]||(e[3]=l=>t.viewMode="card")},e[37]||(e[37]=[a("i",{class:"fas fa-th-large"},null,-1),I(" 卡片视图 ")]),2),a("button",{class:B(["view-btn",{active:t.viewMode==="compact"}]),onClick:e[4]||(e[4]=l=>t.viewMode="compact")},e[38]||(e[38]=[a("i",{class:"fas fa-list"},null,-1),I(" 列表视图 ")]),2)])]),a("div",Vs,[a("div",Ls,[k(a("select",{"onUpdate:modelValue":e[5]||(e[5]=l=>t.statusFilter=l),class:"filter-select"},e[40]||(e[40]=[a("option",{value:"all"},"全部状态",-1),a("option",{value:"running"},"运行中",-1),a("option",{value:"completed"},"已完成",-1)]),512),[[ie,t.statusFilter]]),k(a("input",{"onUpdate:modelValue":e[6]||(e[6]=l=>t.searchQuery=l),type:"text",placeholder:"搜索策略名称、交易对...",class:"search-input"},null,512),[[q,t.searchQuery]])]),t.filteredStrategies.length>0?(n(),i("div",Es,[a("label",Fs,[a("input",{type:"checkbox",checked:t.isAllSelected,indeterminate:t.isPartiallySelected,onChange:e[7]||(e[7]=(...l)=>t.toggleSelectAll&&t.toggleSelectAll(...l))},null,40,Hs),e[41]||(e[41]=a("span",{class:"checkmark"},null,-1)),e[42]||(e[42]=I(" 全选 "))]),t.hasRunningSelectedStrategies?(n(),i("button",{key:0,class:"batch-btn stop-btn",onClick:e[8]||(e[8]=l=>t.showBatchStopConfirm=!0)},[e[43]||(e[43]=a("i",{class:"fas fa-stop"},null,-1)),I(" 批量停止 ("+d(t.runningSelectedCount)+") ",1)])):c("",!0),t.hasNonRunningSelectedStrategies?(n(),i("button",{key:1,class:"batch-btn delete-btn",onClick:e[9]||(e[9]=l=>t.showBatchDeleteConfirm=!0)},[e[44]||(e[44]=a("i",{class:"fas fa-trash"},null,-1)),I(" 批量删除 ("+d(t.nonRunningSelectedCount)+") ",1)])):c("",!0)])):c("",!0)]),a("div",Xs,[a("div",Ys,[e[45]||(e[45]=a("span",{class:"stat-label"},"运行中:",-1)),a("span",qs,d(t.runningStrategiesCount),1)]),a("div",Gs,[e[46]||(e[46]=a("span",{class:"stat-label"},"已完成:",-1)),a("span",Qs,d(t.completedStrategiesCount),1)]),a("div",zs,[e[47]||(e[47]=a("span",{class:"stat-label"},"总计:",-1)),a("span",Js,d(t.strategies.length),1)])]),a("div",js,[t.filteredStrategies.length===0?(n(),i("div",Zs,[e[48]||(e[48]=a("i",{class:"fas fa-inbox"},null,-1)),a("p",null,d(t.getNoStrategiesText()),1)])):t.viewMode==="card"?(n(),i("div",$s,[(n(!0),i(N,null,V(t.filteredStrategies,(l,_)=>(n(),i("div",{key:_,class:B(["strategy-card",{"running-strategy":t.isRunningStrategy(l)}])},[a("div",eo,[a("div",to,[a("h4",ao,d(l.strategyName||"未命名策略"),1),a("div",so,[a("span",oo,d(l.type==="spot"?"现货":"合约"),1),a("span",lo,d(l.symbol),1)])]),a("div",{class:B(["strategy-status",t.getStatusClass(l.status)])},d(t.getStatusText(l.status)),3)]),a("div",no,[a("div",io,[a("div",ro,[e[49]||(e[49]=a("span",{class:"detail-label"},"投资金额:",-1)),a("span",uo,d(l.amount)+" USDT",1)]),a("div",co,[e[50]||(e[50]=a("span",{class:"detail-label"},"创建时间:",-1)),a("span",vo,d(t.formatDate(l.createdAt)),1)]),l.profit!==void 0?(n(),i("div",go,[e[51]||(e[51]=a("span",{class:"detail-label"},"当前盈利:",-1)),a("span",{class:B(["detail-value",t.getProfitClass(l)])},d(t.calculateProfitAmount(l))+" USDT ("+d(t.calculateProfitPercentage(l))+"%) ",3)])):c("",!0)]),a("div",fo,[l.status==="waiting"||l.status==="active"?(n(),i(N,{key:0},[a("button",{class:"stop-button",onClick:T=>t.stopStrategy(l.id||l._id)}," 停止策略 ",8,po),l.status==="active"&&t.hasPosition(l)?(n(),i("button",{key:0,class:"close-position-button",onClick:T=>t.closePosition(l.id||l._id),title:"立即平仓所有持仓"},e[52]||(e[52]=[a("i",{class:"fas fa-hand-paper"},null,-1),I(" 一键平仓 ")]),8,mo)):c("",!0)],64)):l.status!=="error"?(n(),i("div",So,d(t.getStatusText(l.status)),1)):c("",!0),l.status==="error"?(n(),i(N,{key:2},[a("button",{class:"recover-button",onClick:T=>t.recoverStrategy(l.id||l._id),title:"恢复策略监控，继续执行交易"},e[53]||(e[53]=[a("i",{class:"fas fa-redo"},null,-1),I(" 恢复策略 ")]),8,bo),a("button",{class:"delete-button",onClick:T=>t.deleteStrategy(l.id||l._id),title:"永久删除策略"},e[54]||(e[54]=[a("i",{class:"fas fa-trash"},null,-1),I(" 删除 ")]),8,yo)],64)):c("",!0)]),a("div",To,[l.status!=="waiting"&&l.status!=="active"?(n(),i("button",{key:0,class:"delete-button-small",title:"删除策略",onClick:T=>t.deleteStrategy(l.id||l._id)},e[55]||(e[55]=[a("span",{class:"delete-icon"},"×",-1)]),8,Ao)):c("",!0)])])],2))),128))])):t.viewMode==="compact"?(n(),i("div",Do,[e[61]||(e[61]=F('<div class="compact-header" data-v-184cfeb8><div class="compact-col checkbox-col" data-v-184cfeb8>选择</div><div class="compact-col strategy-name-col" data-v-184cfeb8>策略名称</div><div class="compact-col" data-v-184cfeb8>交易对</div><div class="compact-col" data-v-184cfeb8>状态</div><div class="compact-col" data-v-184cfeb8>金额</div><div class="compact-col" data-v-184cfeb8>盈利</div><div class="compact-col" data-v-184cfeb8>操作</div></div>',1)),(n(!0),i(N,null,V(t.filteredStrategies,(l,_)=>(n(),i("div",{key:_,class:B(["compact-strategy-row",{"running-strategy":t.isRunningStrategy(l)}])},[a("div",Po,[a("label",Uo,[k(a("input",{type:"checkbox",value:l.id||l._id,"onUpdate:modelValue":e[10]||(e[10]=T=>t.selectedStrategies=T)},null,8,ko),[[Y,t.selectedStrategies]]),e[56]||(e[56]=a("span",{class:"checkmark-small"},null,-1))])]),a("div",ho,[a("div",_o,d(l.strategyName||"未命名策略"),1),a("div",Co,d(l.type==="spot"?"现货":"合约"),1)]),a("div",xo,[a("span",wo,d(l.symbol),1)]),a("div",Io,[a("span",{class:B(["strategy-status",t.getStatusClass(l.status)])},d(t.getStatusText(l.status)),3)]),a("div",Oo,[a("span",Wo,d(l.amount)+" USDT",1)]),a("div",Ro,[l.status==="active"||l.profit!==void 0&&l.profit!==0?(n(),i("span",{key:0,class:B([t.getProfitClass(l),"profit-text"])},d(t.calculateProfitAmount(l))+" USDT ",3)):(n(),i("span",Mo,"-"))]),a("div",No,[a("div",Ko,[l.status==="waiting"||l.status==="active"?(n(),i("button",{key:0,class:"compact-btn stop-btn",onClick:T=>t.stopStrategy(l.id||l._id),title:"停止策略"},e[57]||(e[57]=[a("i",{class:"fas fa-stop"},null,-1)]),8,Bo)):c("",!0),l.status==="active"&&t.hasPosition(l)?(n(),i("button",{key:1,class:"compact-btn close-btn",onClick:T=>t.closePosition(l.id||l._id),title:"一键平仓"},e[58]||(e[58]=[a("i",{class:"fas fa-hand-paper"},null,-1)]),8,Vo)):c("",!0),l.status==="error"?(n(),i("button",{key:2,class:"compact-btn recover-btn",onClick:T=>t.recoverStrategy(l.id||l._id),title:"恢复策略"},e[59]||(e[59]=[a("i",{class:"fas fa-redo"},null,-1)]),8,Lo)):c("",!0),l.status!=="waiting"&&l.status!=="active"?(n(),i("button",{key:3,class:"compact-btn delete-btn",onClick:T=>t.deleteStrategy(l.id||l._id),title:"删除策略"},e[60]||(e[60]=[a("i",{class:"fas fa-trash"},null,-1)]),8,Eo)):c("",!0)])])],2))),128))])):c("",!0)]),t.showOrderHistory?(n(),i("div",Fo,[xe(W,{apiConnected:t.apiConnected,userId:t.userId,visible:t.showOrderHistory},null,8,["apiConnected","userId","visible"])])):c("",!0)])):c("",!0)]),t.apiConnected?(n(),i("div",Ho,[a("div",Xo,[k(a("button",{class:"floating-btn scroll-top-btn",onClick:e[11]||(e[11]=(...l)=>t.scrollToTop&&t.scrollToTop(...l)),title:"回到顶部"},e[62]||(e[62]=[a("i",{class:"fas fa-arrow-up"},null,-1)]),512),[[We,t.showScrollTop]]),a("button",{class:"floating-btn nav-btn",onClick:e[12]||(e[12]=l=>t.showQuickNav=!t.showQuickNav),title:"快速导航"},e[63]||(e[63]=[a("i",{class:"fas fa-compass"},null,-1)]))]),k(a("div",Yo,[a("button",{class:"nav-item",onClick:e[13]||(e[13]=l=>t.scrollToSection("account-info"))},e[64]||(e[64]=[a("i",{class:"fas fa-wallet"},null,-1),a("span",null,"账户信息",-1)])),a("button",{class:"nav-item",onClick:e[14]||(e[14]=l=>t.scrollToSection("strategy-form"))},e[65]||(e[65]=[a("i",{class:"fas fa-plus"},null,-1),a("span",null,"创建策略",-1)])),a("button",{class:"nav-item",onClick:e[15]||(e[15]=l=>t.scrollToSection("strategies-list"))},e[66]||(e[66]=[a("i",{class:"fas fa-list"},null,-1),a("span",null,"策略列表",-1)])),a("button",{class:"nav-item",onClick:e[16]||(e[16]=(...l)=>t.toggleOrderHistory&&t.toggleOrderHistory(...l))},[e[67]||(e[67]=a("i",{class:"fas fa-history"},null,-1)),a("span",null,d(t.showOrderHistory?"隐藏订单":"历史订单"),1)]),t.runningStrategiesCount>0?(n(),i("button",{key:0,class:"nav-item",onClick:e[17]||(e[17]=l=>t.scrollToSection("running-strategies"))},[e[68]||(e[68]=a("i",{class:"fas fa-play"},null,-1)),a("span",null,"运行中策略 ("+d(t.runningStrategiesCount)+")",1)])):c("",!0)],512),[[We,t.showQuickNav]])])):c("",!0),xe(h,{show:t.showApiModal,isReconfiguring:t.isReconfiguring,onClose:e[18]||(e[18]=l=>t.showApiModal=!1),onApiValidated:t.onApiValidated},null,8,["show","isReconfiguring","onApiValidated"]),t.showDeleteApiConfirm?(n(),i("div",{key:1,class:"modal-overlay",onClick:e[22]||(e[22]=me(l=>t.showDeleteApiConfirm=!1,["self"]))},[a("div",qo,[a("div",Go,[e[69]||(e[69]=a("h3",null,"确认删除API",-1)),a("button",{class:"close-button",onClick:e[19]||(e[19]=l=>t.showDeleteApiConfirm=!1)},"×")]),e[71]||(e[71]=F('<div class="modal-body" data-v-184cfeb8><div class="warning-icon" data-v-184cfeb8><i class="fas fa-exclamation-triangle" data-v-184cfeb8></i></div><p class="warning-text" data-v-184cfeb8>您确定要删除OKX API配置吗？</p><p class="warning-details" data-v-184cfeb8>删除后将：</p><ul class="warning-list" data-v-184cfeb8><li data-v-184cfeb8>断开与OKX的连接</li><li data-v-184cfeb8>停止所有运行中的策略</li><li data-v-184cfeb8>清除已保存的API密钥</li><li data-v-184cfeb8>需要重新配置才能使用交易功能</li></ul></div>',1)),a("div",Qo,[a("button",{class:"cancel-button",onClick:e[20]||(e[20]=l=>t.showDeleteApiConfirm=!1)}," 取消 "),a("button",{class:"confirm-delete-button",onClick:e[21]||(e[21]=(...l)=>t.deleteApi&&t.deleteApi(...l)),disabled:t.isDeletingApi},[e[70]||(e[70]=a("i",{class:"fas fa-trash"},null,-1)),I(" "+d(t.isDeletingApi?"删除中...":"确认删除"),1)],8,zo)])])])):c("",!0),t.showBatchStopConfirm?(n(),i("div",{key:2,class:"modal-overlay",onClick:e[26]||(e[26]=me(l=>t.showBatchStopConfirm=!1,["self"]))},[a("div",Jo,[a("div",jo,[e[72]||(e[72]=a("h3",null,"确认批量停止策略",-1)),a("button",{class:"close-button",onClick:e[23]||(e[23]=l=>t.showBatchStopConfirm=!1)},"×")]),a("div",Zo,[e[73]||(e[73]=a("div",{class:"warning-icon"},[a("i",{class:"fas fa-stop-circle"})],-1)),a("p",$o,"您确定要停止选中的 "+d(t.runningSelectedCount)+" 个运行中的策略吗？",1),a("div",el,[(n(!0),i(N,null,V(t.selectedRunningStrategies,l=>(n(),i("div",{key:l.id||l._id,class:"strategy-preview-item"},[a("span",tl,d(l.strategyName||"未命名策略"),1),a("span",al,d(l.symbol),1),a("span",sl,d(t.getStatusText(l.status)),1)]))),128))])]),a("div",ol,[a("button",{class:"cancel-button",onClick:e[24]||(e[24]=l=>t.showBatchStopConfirm=!1)}," 取消 "),a("button",{class:"confirm-stop-button",onClick:e[25]||(e[25]=(...l)=>t.batchStopStrategies&&t.batchStopStrategies(...l)),disabled:t.isBatchStopping},[e[74]||(e[74]=a("i",{class:"fas fa-stop"},null,-1)),I(" "+d(t.isBatchStopping?"停止中...":"确认停止"),1)],8,ll)])])])):c("",!0),t.showBatchDeleteConfirm?(n(),i("div",{key:3,class:"modal-overlay",onClick:e[30]||(e[30]=me(l=>t.showBatchDeleteConfirm=!1,["self"]))},[a("div",nl,[a("div",il,[e[75]||(e[75]=a("h3",null,"确认批量删除策略",-1)),a("button",{class:"close-button",onClick:e[27]||(e[27]=l=>t.showBatchDeleteConfirm=!1)},"×")]),a("div",rl,[e[76]||(e[76]=a("div",{class:"warning-icon"},[a("i",{class:"fas fa-exclamation-triangle"})],-1)),a("p",ul,"您确定要删除选中的 "+d(t.nonRunningSelectedCount)+" 个策略吗？",1),e[77]||(e[77]=a("p",{class:"warning-details"},"此操作不可撤销！",-1)),a("div",dl,[(n(!0),i(N,null,V(t.selectedNonRunningStrategies,l=>(n(),i("div",{key:l.id||l._id,class:"strategy-preview-item"},[a("span",cl,d(l.strategyName||"未命名策略"),1),a("span",vl,d(l.symbol),1),a("span",gl,d(t.getStatusText(l.status)),1)]))),128))])]),a("div",fl,[a("button",{class:"cancel-button",onClick:e[28]||(e[28]=l=>t.showBatchDeleteConfirm=!1)}," 取消 "),a("button",{class:"confirm-delete-button",onClick:e[29]||(e[29]=(...l)=>t.batchDeleteStrategies&&t.batchDeleteStrategies(...l)),disabled:t.isBatchDeleting},[e[78]||(e[78]=a("i",{class:"fas fa-trash"},null,-1)),I(" "+d(t.isBatchDeleting?"删除中...":"确认删除"),1)],8,pl)])])])):c("",!0)])}const yl=re(ks,[["render",ml],["__scopeId","data-v-184cfeb8"]]);export{yl as default};
