import{_ as g,u as m,r,c as l,d as o,e as s,C as u,g as n,n as e}from"./index-a2cd3c28.js";const p="/images/wechat-qr.jpg",_="/images/qq-qr.jpg",k="/images/dingtalk-qr.jpg";const w={name:"ContactServiceView",setup(){const c=m(),a=r(-1),d=r({wechat:!0,qq:!0,dingtalk:!0});return{goBack:()=>{c.go(-1)},activeFaq:a,toggleFaq:i=>{a.value=a.value===i?-1:i},imageLoaded:d,handleImageError:(i,f)=>{i.target.style.display="none",d.value[f]=!1}}}},h={class:"contact-service-view"},I={class:"header"},C={class:"header-content"},E={class:"main-content"},F={class:"contact-methods"},b={class:"contact-card"},y={class:"qr-code-container"},B={class:"qr-code-wrapper"},Q={class:"real-qr-code"},S={class:"qr-code-image wechat-qr-image"},V={key:0,class:"qr-placeholder"},x={class:"contact-card"},L={class:"qr-code-container"},A={class:"qr-code-wrapper"},P={class:"real-qr-code"},j={class:"qr-code-image qq-qr-image"},N={key:0,class:"qr-placeholder"},z={class:"contact-card"},D={class:"qr-code-container"},R={class:"qr-code-wrapper"},U={class:"real-qr-code"},G={class:"qr-code-image dingtalk-qr-image"},H={key:0,class:"qr-placeholder"},J={class:"faq-section"},K={class:"faq-card"},M={class:"faq-list"};function O(c,a,d,t,v,q){return l(),o("div",h,[s("header",I,[s("div",C,[s("button",{class:"back-button",onClick:a[0]||(a[0]=(...i)=>t.goBack&&t.goBack(...i))},a[7]||(a[7]=[s("i",{class:"fas fa-arrow-left"},null,-1)])),a[8]||(a[8]=s("h1",null,"联系客服",-1))])]),s("main",E,[a[19]||(a[19]=u('<div class="service-intro" data-v-6449cd28><div class="intro-card" data-v-6449cd28><div class="intro-icon" data-v-6449cd28><i class="fas fa-headset" data-v-6449cd28></i></div><h2 data-v-6449cd28>专业客服团队</h2><p data-v-6449cd28>我们的客服团队7×24小时为您提供专业服务</p><div class="service-features" data-v-6449cd28><div class="feature-item" data-v-6449cd28><i class="fas fa-clock" data-v-6449cd28></i><span data-v-6449cd28>24小时在线</span></div><div class="feature-item" data-v-6449cd28><i class="fas fa-shield-alt" data-v-6449cd28></i><span data-v-6449cd28>安全可靠</span></div><div class="feature-item" data-v-6449cd28><i class="fas fa-rocket" data-v-6449cd28></i><span data-v-6449cd28>快速响应</span></div></div></div></div>',1)),s("div",F,[s("div",b,[a[10]||(a[10]=s("div",{class:"contact-header"},[s("div",{class:"contact-icon wechat"},[s("i",{class:"fab fa-weixin"})]),s("div",{class:"contact-info"},[s("h3",null,"微信客服"),s("p",null,"扫码添加微信好友")])],-1)),s("div",y,[s("div",B,[s("div",Q,[s("div",S,[s("img",{src:p,alt:"微信客服二维码",class:"qr-image",onError:a[1]||(a[1]=i=>t.handleImageError(i,"wechat"))},null,32),t.imageLoaded.wechat?n("",!0):(l(),o("div",V,a[9]||(a[9]=[s("div",{class:"qr-info"},[s("i",{class:"fab fa-weixin qr-icon"}),s("p",null,"二维码暂未设置")],-1)])))])])])])]),s("div",x,[a[12]||(a[12]=s("div",{class:"contact-header"},[s("div",{class:"contact-icon qq"},[s("i",{class:"fab fa-qq"})]),s("div",{class:"contact-info"},[s("h3",null,"QQ客服"),s("p",null,"云数科技 - 2180220746")])],-1)),s("div",L,[s("div",A,[s("div",P,[s("div",j,[s("img",{src:_,alt:"QQ客服二维码",class:"qr-image",onError:a[2]||(a[2]=i=>t.handleImageError(i,"qq"))},null,32),t.imageLoaded.qq?n("",!0):(l(),o("div",N,a[11]||(a[11]=[s("div",{class:"qr-info"},[s("i",{class:"fab fa-qq qr-icon"}),s("p",null,"二维码暂未设置")],-1)])))])])])])]),s("div",z,[a[14]||(a[14]=s("div",{class:"contact-header"},[s("div",{class:"contact-icon dingtalk"},[s("i",{class:"fas fa-comments"})]),s("div",{class:"contact-info"},[s("h3",null,"钉钉客服"),s("p",null,"华帝 - 天数联盟")])],-1)),s("div",D,[s("div",R,[s("div",U,[s("div",G,[s("img",{src:k,alt:"钉钉客服二维码",class:"qr-image",onError:a[3]||(a[3]=i=>t.handleImageError(i,"dingtalk"))},null,32),t.imageLoaded.dingtalk?n("",!0):(l(),o("div",H,a[13]||(a[13]=[s("div",{class:"qr-info"},[s("i",{class:"fas fa-comments qr-icon"}),s("p",null,"二维码暂未设置")],-1)])))])])])])])]),s("div",J,[s("div",K,[a[18]||(a[18]=s("h3",null,"📋 常见问题",-1)),s("div",M,[s("div",{class:e(["faq-item",{active:t.activeFaq===0}]),onClick:a[4]||(a[4]=i=>t.toggleFaq(0))},a[15]||(a[15]=[s("div",{class:"faq-question"},[s("span",null,"如何开始使用交易策略？"),s("i",{class:"fas fa-chevron-down"})],-1),s("div",{class:"faq-answer"},[s("p",null,"1. 首先需要注册并登录账户"),s("p",null,"2. 在API设置中配置您的交易所API密钥"),s("p",null,"3. 选择合适的交易策略并设置参数"),s("p",null,"4. 启动策略开始自动交易")],-1)]),2),s("div",{class:e(["faq-item",{active:t.activeFaq===1}]),onClick:a[5]||(a[5]=i=>t.toggleFaq(1))},a[16]||(a[16]=[s("div",{class:"faq-question"},[s("span",null,"API密钥安全吗？"),s("i",{class:"fas fa-chevron-down"})],-1),s("div",{class:"faq-answer"},[s("p",null,"我们采用银行级别的加密技术保护您的API密钥，所有数据传输都经过SSL加密，确保您的资产安全。")],-1)]),2),s("div",{class:e(["faq-item",{active:t.activeFaq===2}]),onClick:a[6]||(a[6]=i=>t.toggleFaq(2))},a[17]||(a[17]=[s("div",{class:"faq-question"},[s("span",null,"如何查看交易收益？"),s("i",{class:"fas fa-chevron-down"})],-1),s("div",{class:"faq-answer"},[s("p",null,'您可以在"我的收益"页面查看详细的收益统计，包括总收益、今日收益、收益来源和历史记录。')],-1)]),2)])])]),a[20]||(a[20]=s("div",{class:"contact-tips"},[s("div",{class:"tips-card"},[s("h3",null,"💡 联系提示"),s("ul",null,[s("li",null,"请优先使用微信联系，响应速度最快"),s("li",null,"工作时间：周一至周日 9:00-22:00"),s("li",null,"紧急问题可通过QQ联系24小时客服"),s("li",null,"请准备好您的UID以便快速定位问题")])])],-1))])])}const W=g(w,[["render",O],["__scopeId","data-v-6449cd28"]]);export{W as default};
