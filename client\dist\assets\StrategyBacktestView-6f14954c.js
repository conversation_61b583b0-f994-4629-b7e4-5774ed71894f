import{_ as Ct,u as Rt,r as p,l as X,o as wt,c as i,d,e as t,C as H,n as S,i as v,t as o,g as m,f as w,B as st,F as M,p as V,q as Ut,v as J,k as h,h as It}from"./index-a2cd3c28.js";import{s as W}from"./strategyPermissionService-52b945bf.js";const Et={name:"StrategyBacktestView",setup(){const E=Rt(),s=p(null),k=p(null),a=p(!1),P=p(null),N=p({binance:!1,okx:!1}),B=p(!1),D=p(null),e=p(!1),y=p(1),T=p(20),C=p(!1),U=p(null),R=p({isComplete:!1,status:"pending",text:"未初始化",icon:"fas fa-clock"}),I=p({totalRecords:0,symbolCount:0,lastUpdated:null}),_=p({exchange:"",symbol:"",timeframe:"1h",startDate:"2024-05-01",endDate:"2025-05-01",initialCapital:1e4,feeRate:.1,strategyParams:{}}),et={startDate:"2024-05-01",endDate:"2025-05-01",exchanges:["binance"],symbols:["BTCUSDT","ETHUSDT","BNBUSDT","ADAUSDT","XRPUSDT","SOLUSDT","DOTUSDT","DOGEUSDT","AVAXUSDT","SHIBUSDT","MATICUSDT","LTCUSDT","UNIUSDT","LINKUSDT","ATOMUSDT","ETCUSDT","XLMUSDT","BCHUSDT","FILUSDT","TRXUSDT","NEARUSDT","APTUSDT","OPUSDT","ARBUSDT","SUIUSDT"],timeframes:["1m","5m","15m","1h","4h","1d"],batchSize:1e3,preferPublicAPI:!0},j=p([]),Y=p([]),Z=p(!1),$=X(()=>s.value?s.value==="binance"?j.value:Y.value:[]),lt=X(()=>{const l=$.value,n={futures:[],spot:[]};return l.forEach(r=>{r.type==="futures"?n.futures.push(r):r.type==="spot"&&n.spot.push(r)}),n}),tt=X(()=>$.value.find(l=>l.id===k.value)),ot=X(()=>D.value?Math.ceil(D.value.length/T.value):0),nt=X(()=>{if(!D.value)return[];const l=(y.value-1)*T.value,n=l+T.value;return D.value.slice(l,n)}),q=async l=>{if(l)try{Z.value=!0,console.log(`正在获取${l}策略列表...`);const n=await h.get("/backtest/strategies",{params:{exchange:l}});if(console.log(`${l}策略API响应:`,n.data),n.data.success){let r=n.data.strategies;console.log(`从API获取到${r.length}个${l}策略:`,r.map(c=>({id:c.id,name:c.name,type:c.type})));try{console.log(`开始获取${l}策略的用户权限...`);const u=(await W.getUserStrategies()).map(g=>g.strategyId);if(console.log(`用户允许的策略ID (${u.length}个):`,u),u.length===0)console.warn("用户权限列表为空，将显示所有策略"),r=n.data.strategies;else{const g=r.length;r=r.filter(f=>{const b=at(f.id,l),x=u.includes(b);return console.log(`策略权限检查: ${f.name} (${f.id} -> ${b}) = ${x}`),x||console.log(`用户无权限使用策略: ${f.name} (${b})`),x}),console.log(`权限过滤: ${g} -> ${r.length} 个${l}策略`),r.length===0&&(console.warn("权限过滤后没有可用策略，显示所有策略以避免界面空白"),r=n.data.strategies)}console.log(`最终${l}策略列表 (${r.length}个):`,r.map(g=>({id:g.id,name:g.name,type:g.type})))}catch(c){console.error("获取用户权限失败，显示所有策略:",c),console.log("权限获取失败，显示所有策略以确保用户可以使用系统"),r=n.data.strategies}l==="binance"?(j.value=r,console.log(`最终显示${r.length}个币安策略`)):l==="okx"&&(Y.value=r,console.log(`最终显示${r.length}个OKX策略`))}else console.error(`获取${l}策略列表失败:`,n.data.error)}catch(n){console.error(`获取${l}策略列表失败:`,n)}finally{Z.value=!1}},at=(l,n)=>{console.log("🔄 映射策略ID:",l,"交易所:",n);const c={binance_spot1:"spot1",binance_spot2:"spot2",binance_spot3:"spot3",binance_spot4:"spot4",binance_spot5:"spot5",binance_futures1:"futures1",binance_futures2:"futures2",binance_futures3:"futures3",binance_futures4:"futures4",binance_futures5:"futures5",binance_futures6:"futures6",binance_futures7:"futures7",binance_futures8:"futures8",binance_futures9:"futures9",binance_futures10:"futures10",binance_spot_1:"spot1",binance_spot_2:"spot2",binance_spot_3:"spot3",binance_spot_4:"spot4",binance_spot_5:"spot5",binance_futures_1:"futures1",binance_futures_2:"futures2",binance_futures_3:"futures3",binance_futures_4:"futures4",binance_futures_5:"futures5",binance_futures_6:"futures6",binance_futures_7:"futures7",binance_futures_8:"futures8",binance_futures_9:"futures9",binance_futures_10:"futures10",okx_spot_1:"spot1",okx_spot_2:"spot2",okx_spot_3:"spot3",okx_spot_4:"spot4",okx_spot_5:"spot5",okx_futures_1:"futures1",okx_futures_2:"futures2",okx_futures_3:"futures3",okx_futures_4:"futures4",okx_futures_5:"futures5",okx_futures_6:"futures6",okx_futures_7:"futures7",okx_futures_8:"futures8",okx_futures_9:"futures9",okx_futures_10:"futures10",spot1:"spot1",spot2:"spot2",spot3:"spot3",spot4:"spot4",spot5:"spot5",futures1:"futures1",futures2:"futures2",futures3:"futures3",futures4:"futures4",futures5:"futures5",futures6:"futures6",futures7:"futures7",futures8:"futures8",futures9:"futures9",futures10:"futures10"}[l]||l;return console.log("🎯 映射结果:",l,"→",c),c},it=async()=>{try{const l=await h.get("/binance/api-keys");N.value.binance=l.data.success&&l.data.hasApiKeys;const n=await h.get("/okx/api-keys");N.value.okx=n.data.success&&n.data.hasApiKeys,console.log("API密钥状态:",N.value)}catch(l){console.error("检查API密钥状态失败:",l)}},dt=async l=>{s.value=l,k.value=null,P.value=null,_.value.exchange=l,_.value.symbol="BTCUSDT",await q(l)},rt=l=>{k.value=l,P.value=null,_.value.strategyParams={};const n=tt.value;n&&(_.value.timeframe=n.timeframe)},ct=async()=>{var l,n,r;if(!s.value||!k.value){alert("请先选择交易所和策略");return}if(!R.value.isComplete){alert("请先初始化历史数据后再进行回测");return}try{console.log("🔍 开始策略权限验证..."),console.log("选中的策略:",k.value),console.log("选中的交易所:",s.value);const c=at(k.value,s.value);console.log("映射后的策略ID:",c);const u=await W.getUserStrategies();console.log("用户权限列表:",u),console.log("用户权限策略ID列表:",u.map(f=>f.strategyId));const g=await W.hasPermission(c);if(console.log("权限检查结果:",g),!g){const f=((l=tt.value)==null?void 0:l.name)||k.value;console.error("❌ 权限验证失败:",{strategyTemplate:k.value,mappedStrategyId:c,strategyName:f,userPermissions:u.map(b=>b.strategyId)}),alert(`您当前的会员等级无权使用策略：${f}。请升级会员等级或联系管理员。`);return}console.log("✅ 策略权限验证通过")}catch(c){console.error("策略权限验证失败:",c),alert("策略权限验证失败，请稍后重试");return}a.value=!0,P.value=null;try{console.log("开始真实数据回测:",{exchange:s.value,strategy:k.value,params:_.value});const c=await h.post("/backtest/run",{exchange:s.value,strategyId:k.value,symbol:_.value.symbol,timeframe:_.value.timeframe,startDate:_.value.startDate,endDate:_.value.endDate,initialCapital:_.value.initialCapital,feeRate:_.value.feeRate,strategyParams:_.value.strategyParams},{timeout:12e4});c.data.success?P.value=c.data.results:alert("回测失败: "+(c.data.error||"未知错误"))}catch(c){console.error("回测失败:",c),alert("回测失败: "+(((r=(n=c.response)==null?void 0:n.data)==null?void 0:r.error)||c.message||"网络错误"))}finally{a.value=!1}},ut=async(l=!1)=>{var u,g;if(C.value){alert("数据收集已在进行中");return}const n=l?"强制完整收集":"智能收集";if(confirm(l?`即将开始强制完整收集2024年5月-2025年5月的历史数据。
此模式将重新收集所有数据，确保90%+覆盖率和85%+数据质量。
此过程预计需要4-6小时，建议在网络稳定时进行。

确定要开始强制完整收集吗？`:`即将开始智能收集2024年5月-2025年5月的历史数据。
此模式将跳过已完整的数据，补充缺失部分。
此过程预计需要1-3小时，建议在网络稳定时进行。

确定要开始吗？`)){C.value=!0,U.value=null,R.value={isComplete:!1,status:"collecting",text:`${n}中`,icon:"fas fa-spinner fa-spin"};try{const f={...et,forceComplete:l};console.log(`开始${n}历史数据:`,f);const b=await h.post("/kline-data/start-historical-collection",f,{timeout:6e4});if(b.data.success)alert(l?`强制完整收集已启动！
系统将重新收集所有数据以确保最高质量，您可以随时查看进度。`:`智能收集已启动！
系统将在后台补充缺失的历史数据，您可以随时查看进度。`),vt();else throw new Error(b.data.error||"启动失败")}catch(f){console.error(`启动${n}失败:`,f),alert(`启动${n}失败: `+(((g=(u=f.response)==null?void 0:u.data)==null?void 0:g.error)||f.message)),C.value=!1,R.value={isComplete:!1,status:"error",text:"收集失败",icon:"fas fa-exclamation-triangle"}}}},Q=async()=>{try{const l=await h.get("/kline-data/historical-collection-status");if(console.log("历史数据收集状态API响应:",l.data),l.data.success){const n=l.data.data;console.log("历史数据收集状态详情:",n),U.value=n,C.value=n.isRunning,n.isRunning?(console.log("历史数据收集正在运行，进度信息:",n.progress),R.value={isComplete:!1,status:"collecting",text:"收集中",icon:"fas fa-spinner fa-spin"}):(console.log("历史数据收集未运行，检查数据完整性"),await ft())}else console.warn("历史数据收集状态API返回失败:",l.data);try{const n=await h.get("/kline-data/stats");console.log("数据统计API响应:",n.data),n.data.success&&n.data.stats?(I.value={totalRecords:n.data.stats.totalRecords||0,symbolCount:n.data.stats.symbolCount||0,lastUpdated:n.data.stats.lastUpdated||null},console.log("数据统计更新成功:",I.value)):(console.warn("数据统计API响应格式异常:",n.data),I.value={totalRecords:0,symbolCount:0,lastUpdated:null})}catch(n){console.error("获取数据统计失败:",n),I.value={totalRecords:0,symbolCount:0,lastUpdated:null}}}catch(l){console.error("检查数据状态失败:",l),I.value||(I.value={totalRecords:0,symbolCount:0,lastUpdated:null})}},ft=async()=>{var l;try{const n=await h.get("/kline-data/completeness-check",{params:{startDate:"2024-05-01",endDate:"2025-05-01"}});if(n.data.success){const r=n.data.isComplete,c=n.data.completeness,u=n.data.statusMessage||(r?"数据完整":"数据不完整");R.value={isComplete:r,status:r?"complete":"pending",text:u,icon:r?"fas fa-check-circle":"fas fa-clock",details:{completenessRatio:c.completenessRatio,dataQualityRatio:c.dataQualityRatio,totalDataPoints:(l=c.summary)==null?void 0:l.totalDataPoints,actualCombinations:c.actualCombinations,expectedCombinations:c.expectedCombinations,recommendations:n.data.recommendations||[]}}}}catch(n){console.error("检查数据完整性失败:",n),R.value={isComplete:!1,status:"error",text:"检查失败",icon:"fas fa-exclamation-triangle",details:null}}},vt=()=>{const l=setInterval(async()=>{if(!C.value){clearInterval(l);return}await Q(),C.value||(clearInterval(l),alert("历史数据收集完成！现在可以进行回测了。"))},1e4)},mt=async()=>{var l,n;try{if(!confirm("确定要停止数据收集吗？已收集的数据将保留。"))return;const c=await h.post("/kline-data/stop-historical-collection");c.data.success?(alert("数据收集已停止"),C.value=!1,U.value=null,await Q()):alert("停止数据收集失败: "+(c.data.error||"未知错误"))}catch(r){console.error("停止数据收集失败:",r),alert("停止数据收集失败: "+(((n=(l=r.response)==null?void 0:l.data)==null?void 0:n.error)||r.message))}},gt=l=>l>=1e6?(l/1e6).toFixed(1)+"M":l>=1e3?(l/1e3).toFixed(1)+"K":l.toString(),pt=(l,n)=>{try{if(!U.value||!U.value.progress)return n;const r=U.value.progress[l];return r??n}catch(r){return console.warn(`获取进度值失败 (${l}):`,r),n}},yt=()=>a.value?"回测中...":R.value.isComplete?"开始回测 (使用历史数据)":"请先初始化历史数据",bt=l=>l?l.type==="futures"?"专业的合约交易策略，采用技术指标分析和风险管理，适合有经验的交易者。":l.type==="spot"?"稳健的现货交易策略，基于市场趋势和技术分析，适合长期投资。":"经过验证的量化交易策略，结合多种技术指标进行市场分析。":"",_t=async()=>{var l;if(!P.value||!P.value.trades){alert("没有可用的交易记录");return}e.value=!0;try{const n=P.value.trades;console.log("原始交易记录:",n);const r=[];let c=[];if(n.forEach((u,g)=>{if(u.type==="buy"||u.type==="add_position")c.push(u);else if(u.type==="sell"&&c.length>0){const f=c.reduce((z,O)=>z+O.quantity,0),b=c.reduce((z,O)=>z+O.price*O.quantity,0),x=b/f,F=c[0].timestamp,G=u.price*u.quantity,K=b,L=u.profit||G-K-u.fee,A=(G-K)/K*100,ht=u.timestamp-F;r.push({index:r.length+1,side:"buy",entryTime:F,exitTime:u.timestamp,entryPrice:x,exitPrice:u.price,quantity:f,profit:L,profitPercent:A,duration:ht,buyTrades:c.length,totalFee:c.reduce((z,O)=>z+(O.fee||0),0)+(u.fee||0)}),c=[]}}),c.length>0){const u=c.reduce((L,A)=>L+A.quantity,0),g=c.reduce((L,A)=>L+A.price*A.quantity,0),f=g/u,b=c[0].timestamp,x=((l=n[n.length-1])==null?void 0:l.price)||f,F=x*u,G=F-g,K=(F-g)/g*100;r.push({index:r.length+1,side:"buy",entryTime:b,exitTime:null,entryPrice:f,exitPrice:x,quantity:u,profit:G,profitPercent:K,duration:Date.now()-b,buyTrades:c.length,isOpen:!0})}D.value=r,console.log("处理后的交易记录:",r),B.value=!0,setTimeout(()=>{const u=document.querySelector(".trade-records");u&&u.scrollIntoView({behavior:"smooth"})},100)}catch(n){console.error("处理交易记录失败:",n),alert("处理交易记录失败: "+n.message)}finally{e.value=!1}},kt=()=>{B.value=!1,D.value=null,y.value=1},St=l=>l?new Date(l).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}):"-",Dt=l=>l==null?"-":Number(l).toFixed(4),Pt=l=>l==null?"-":Number(l).toFixed(6),Tt=l=>{if(!l)return"-";const n=Math.floor(l/(1e3*60*60)),r=Math.floor(l%(1e3*60*60)/(1e3*60));if(n>24){const c=Math.floor(n/24),u=n%24;return`${c}天${u}小时`}else return n>0?`${n}小时${r}分钟`:`${r}分钟`},xt=()=>{s.value==="binance"?E.push({name:"binance"}):E.push({name:"okx"})};return wt(async()=>{console.log("🔄 清除策略权限缓存，确保获取最新权限"),W.clearCache(),await it(),await Q(),await q("binance"),await q("okx")}),{selectedExchange:s,selectedStrategy:k,isBacktesting:a,backtestResults:P,backtestParams:_,apiKeyStatus:N,binanceStrategies:j,okxStrategies:Y,filteredStrategies:$,groupedStrategies:lt,selectedStrategyData:tt,isLoadingStrategies:Z,showTradeRecords:B,tradeRecords:D,isLoadingTradeRecords:e,currentPage:y,recordsPerPage:T,totalPages:ot,paginatedRecords:nt,dataStatus:R,dataStats:I,isCollecting:C,collectionProgress:U,initializeHistoricalData:ut,checkDataStatus:Q,stopDataCollection:mt,formatNumber:gt,getProgressValue:pt,getBacktestButtonText:yt,getStrategyDisplayDescription:bt,viewTradeRecords:_t,closeTradeRecords:kt,formatDateTime:St,formatPrice:Dt,formatQuantity:Pt,formatDuration:Tt,goBack:()=>E.go(-1),selectExchange:dt,selectStrategy:rt,startBacktest:ct,goToApiConfig:xt,fetchStrategies:q,exportResults:()=>alert("导出功能开发中..."),applyStrategy:()=>{s.value==="binance"?E.push({name:"binance"}):E.push({name:"okx"})}}}},Bt={class:"strategy-backtest-view"},At={class:"header"},Ot={class:"header-content"},Mt={class:"main-content"},Vt={class:"exchange-selection"},Nt={class:"exchange-tabs"},Ft={key:0,class:"api-warning"},Kt={class:"warning-card"},Lt={class:"warning-content"},zt={key:1,class:"strategy-selection"},Xt={key:0,class:"loading-strategies"},Ht={key:1,class:"strategy-selector"},qt={class:"debug-info",style:{background:"#f0f8ff",padding:"10px","margin-bottom":"15px","border-radius":"5px","font-size":"12px"}},Qt={style:{"margin-top":"5px"}},Gt={style:{"font-size":"10px",margin:"5px 0"}},Jt={class:"selector-group"},Wt=["label"],jt=["value"],Yt={key:0,class:"selected-strategy-info"},Zt={class:"strategy-detail-card"},$t={class:"strategy-detail-header"},ts={class:"strategy-detail-icon"},ss={class:"strategy-detail-info"},as={class:"strategy-detail-stats"},es={class:"detail-stat-item"},ls={class:"detail-stat-value"},os={class:"detail-stat-item"},ns={class:"detail-stat-value"},is={class:"detail-stat-item"},ds={class:"detail-stat-value"},rs={key:2,class:"historical-data-status"},cs={class:"data-status-content"},us={class:"data-overview"},fs={class:"overview-header"},vs={class:"data-specs"},ms={class:"spec-item"},gs={key:0,class:"data-collection-control"},ps={class:"collection-actions"},ys=["disabled"],bs={key:0,class:"fas fa-rocket"},_s={key:1,class:"fas fa-spinner fa-spin"},ks=["disabled"],Ss={key:0,class:"fas fa-sync-alt"},Ds={key:1,class:"fas fa-spinner fa-spin"},Ps=["disabled"],Ts={key:1,class:"collection-progress"},xs={class:"progress-overview"},hs={class:"progress-circle"},Cs={class:"progress-percent"},Rs={class:"progress-details"},ws={class:"detail-row"},Us={class:"detail-value"},Is={class:"detail-row"},Es={class:"detail-value"},Bs={class:"detail-row"},As={class:"detail-value"},Os={class:"detail-row"},Ms={class:"detail-value"},Vs={class:"progress-actions"},Ns={key:2,class:"data-complete"},Fs={class:"complete-message"},Ks={class:"message-content"},Ls={key:0,class:"data-completeness-details"},zs={class:"completeness-stats"},Xs={class:"stat-item"},Hs={class:"stat-value"},qs={class:"stat-item"},Qs={class:"stat-value"},Gs={class:"stat-item"},Js={class:"stat-value"},Ws={class:"stat-item"},js={class:"stat-value"},Ys={key:0,class:"recommendations"},Zs={class:"data-summary"},$s={class:"summary-item"},ta={class:"summary-value"},sa={class:"summary-item"},aa={class:"summary-value"},ea={class:"summary-item"},la={class:"summary-value"},oa={class:"collection-status-info"},na={class:"status-notice"},ia={class:"notice-text"},da={key:3,class:"data-incomplete"},ra={class:"incomplete-message"},ca={class:"message-content"},ua={key:0,class:"data-completeness-details"},fa={class:"completeness-stats"},va={class:"stat-item"},ma={class:"stat-value"},ga={class:"stat-item"},pa={class:"stat-value"},ya={class:"stat-item"},ba={class:"stat-value"},_a={class:"stat-item"},ka={class:"stat-value"},Sa={key:0,class:"recommendations"},Da={key:3,class:"backtest-settings"},Pa={class:"settings-grid"},Ta={class:"setting-group"},xa=["value"],ha={class:"setting-note"},Ca={class:"setting-group"},Ra={class:"setting-group"},wa={class:"setting-group"},Ua=["disabled"],Ia={key:0,class:"date-hint"},Ea={key:1,class:"date-hint"},Ba={class:"setting-group"},Aa=["disabled"],Oa={key:0,class:"date-hint"},Ma={key:1,class:"date-hint"},Va={class:"setting-group"},Na={class:"setting-group"},Fa=["placeholder"],Ka={key:0,class:"strategy-params"},La={class:"params-grid"},za=["onUpdate:modelValue","type","step","min","max","placeholder"],Xa={class:"param-desc"},Ha={class:"backtest-actions"},qa=["disabled"],Qa={key:0,class:"fas fa-play"},Ga={key:1,class:"fas fa-spinner fa-spin"},Ja={key:0,class:"backtest-notice"},Wa={key:4,class:"backtest-results"},ja={class:"results-summary"},Ya={class:"result-card"},Za={class:"result-info"},$a={class:"result-card"},te={class:"result-info"},se={class:"result-value"},ae={class:"result-card"},ee={class:"result-info"},le={class:"result-value"},oe={class:"result-card"},ne={class:"result-info"},ie={class:"result-value negative"},de={class:"detailed-stats"},re={class:"stats-grid"},ce={class:"stat-row"},ue={class:"stat-value"},fe={class:"stat-row"},ve={class:"stat-value positive"},me={class:"stat-row"},ge={class:"stat-value negative"},pe={class:"stat-row"},ye={class:"stat-value positive"},be={class:"stat-row"},_e={class:"stat-value negative"},ke={class:"stat-row"},Se={class:"stat-value"},De={class:"result-actions"},Pe={key:5,class:"trade-records"},Te={class:"records-summary"},xe={class:"summary-card"},he={class:"summary-info"},Ce={class:"summary-value"},Re={class:"summary-card"},we={class:"summary-info"},Ue={class:"summary-value positive"},Ie={class:"summary-card"},Ee={class:"summary-info"},Be={class:"summary-value negative"},Ae={class:"summary-card"},Oe={class:"summary-info"},Me={class:"summary-value"},Ve={class:"records-controls"},Ne={class:"records-info"},Fe={key:0},Ke={key:0,class:"records-pagination"},Le=["disabled"],ze={class:"page-info"},Xe=["disabled"],He={class:"records-table-container"},qe={class:"records-table"},Qe={key:0,class:"add-position-badge"},Ge={key:0,class:"open-status"},Je={key:1},We={class:"price"},je={class:"price"},Ye={key:0,class:"current-price"},Ze={key:1},$e={class:"quantity"},tl={key:0},sl={key:1},al={key:0},el={key:1},ll={class:"duration"},ol={class:"records-actions"};function nl(E,s,k,a,P,N){var B,D;return i(),d("div",Bt,[t("header",At,[t("div",Ot,[t("button",{class:"back-button",onClick:s[0]||(s[0]=(...e)=>a.goBack&&a.goBack(...e))},s[22]||(s[22]=[t("i",{class:"fas fa-arrow-left"},null,-1)])),s[23]||(s[23]=t("h1",null,"策略回测",-1))])]),t("main",Mt,[s[126]||(s[126]=H('<div class="intro-section" data-v-63df4d29><div class="intro-card" data-v-63df4d29><h2 data-v-63df4d29>📊 策略回测系统</h2><p data-v-63df4d29>使用真实历史数据验证交易策略的有效性，分析策略表现和风险指标，为实盘交易提供数据支持。</p><div class="key-features" data-v-63df4d29><div class="feature-item" data-v-63df4d29><i class="fas fa-chart-area" data-v-63df4d29></i><span data-v-63df4d29>真实数据回测</span></div><div class="feature-item" data-v-63df4d29><i class="fas fa-calculator" data-v-63df4d29></i><span data-v-63df4d29>收益分析</span></div><div class="feature-item" data-v-63df4d29><i class="fas fa-shield-alt" data-v-63df4d29></i><span data-v-63df4d29>风险评估</span></div></div></div></div>',1)),t("div",Vt,[s[26]||(s[26]=t("h3",null,"🏢 选择交易所",-1)),t("div",Nt,[t("button",{class:S(["exchange-tab",{active:a.selectedExchange==="binance"}]),onClick:s[1]||(s[1]=e=>a.selectExchange("binance"))},s[24]||(s[24]=[t("i",{class:"fas fa-coins"},null,-1),v(" 币安 (Binance) ")]),2),t("button",{class:S(["exchange-tab",{active:a.selectedExchange==="okx"}]),onClick:s[2]||(s[2]=e=>a.selectExchange("okx"))},s[25]||(s[25]=[t("i",{class:"fas fa-chart-line"},null,-1),v(" OKX ")]),2)])]),a.selectedExchange&&!a.apiKeyStatus[a.selectedExchange]?(i(),d("div",Ft,[t("div",Kt,[s[29]||(s[29]=t("i",{class:"fas fa-exclamation-triangle"},null,-1)),t("div",Lt,[s[28]||(s[28]=t("h4",null,"需要配置API密钥",-1)),t("p",null,"您尚未配置"+o(a.selectedExchange==="binance"?"币安":"OKX")+"的API密钥，无法获取真实历史数据进行回测。",1),t("button",{class:"config-api-btn",onClick:s[3]||(s[3]=(...e)=>a.goToApiConfig&&a.goToApiConfig(...e))},s[27]||(s[27]=[t("i",{class:"fas fa-cog"},null,-1),v(" 配置API密钥 ")]))])])])):m("",!0),a.selectedExchange&&a.apiKeyStatus[a.selectedExchange]?(i(),d("div",zt,[s[43]||(s[43]=t("h3",null,"🎯 选择回测策略",-1)),a.isLoadingStrategies?(i(),d("div",Xt,s[30]||(s[30]=[t("i",{class:"fas fa-spinner fa-spin"},null,-1),t("span",null,"正在加载策略列表...",-1)]))):(i(),d("div",Ht,[t("div",qt,[s[32]||(s[32]=t("strong",null,"🔧 调试信息：",-1)),s[33]||(s[33]=t("br",null,null,-1)),v(" 当前交易所: "+o(a.selectedExchange),1),s[34]||(s[34]=t("br",null,null,-1)),v(" 策略总数: "+o(a.filteredStrategies.length),1),s[35]||(s[35]=t("br",null,null,-1)),v(" 合约策略: "+o(((B=a.groupedStrategies.futures)==null?void 0:B.length)||0)+" 个",1),s[36]||(s[36]=t("br",null,null,-1)),v(" 现货策略: "+o(((D=a.groupedStrategies.spot)==null?void 0:D.length)||0)+" 个",1),s[37]||(s[37]=t("br",null,null,-1)),t("details",Qt,[s[31]||(s[31]=t("summary",null,"详细策略列表",-1)),t("pre",Gt,o(JSON.stringify(a.filteredStrategies.map(e=>({id:e.id,name:e.name,type:e.type})),null,2)),1)])]),t("div",Jt,[s[39]||(s[39]=t("label",{for:"strategy-select"},"选择策略",-1)),w(t("select",{id:"strategy-select","onUpdate:modelValue":s[4]||(s[4]=e=>a.selectedStrategy=e),class:"strategy-select",onChange:s[5]||(s[5]=e=>a.selectStrategy(a.selectedStrategy))},[s[38]||(s[38]=t("option",{value:""},"请选择一个策略...",-1)),(i(!0),d(M,null,V(a.groupedStrategies,(e,y)=>(i(),d("optgroup",{key:y,label:y==="futures"?"合约策略":"现货策略"},[(i(!0),d(M,null,V(e,T=>(i(),d("option",{key:T.id,value:T.id},o(T.icon)+" "+o(T.name),9,jt))),128))],8,Wt))),128))],544),[[st,a.selectedStrategy]])]),a.selectedStrategyData?(i(),d("div",Yt,[t("div",Zt,[t("div",$t,[t("div",ts,o(a.selectedStrategyData.icon),1),t("div",ss,[t("h4",null,o(a.selectedStrategyData.name),1),t("p",null,o(a.getStrategyDisplayDescription(a.selectedStrategyData)),1)]),t("div",{class:S(["strategy-detail-type",a.selectedStrategyData.type])},o(a.selectedStrategyData.typeLabel),3)]),t("div",as,[t("div",es,[s[40]||(s[40]=t("span",{class:"detail-stat-label"},"策略类型",-1)),t("span",ls,o(a.selectedStrategyData.type==="futures"?"合约":"现货"),1)]),t("div",os,[s[41]||(s[41]=t("span",{class:"detail-stat-label"},"时间周期",-1)),t("span",ns,o(a.selectedStrategyData.timeframe),1)]),t("div",is,[s[42]||(s[42]=t("span",{class:"detail-stat-label"},"交易所",-1)),t("span",ds,o(a.selectedStrategyData.exchange==="binance"?"币安":"OKX"),1)])])])])):m("",!0)]))])):m("",!0),a.selectedExchange&&a.apiKeyStatus[a.selectedExchange]?(i(),d("div",rs,[s[82]||(s[82]=t("h3",null,"📊 历史数据状态",-1)),t("div",cs,[t("div",us,[t("div",fs,[s[44]||(s[44]=t("div",{class:"overview-icon"},"🗄️",-1)),s[45]||(s[45]=t("div",{class:"overview-info"},[t("h4",null,"回测数据集"),t("p",null,"2024年5月1日 - 2025年5月1日 (完整12个月)")],-1)),t("div",{class:S(["overview-status",a.dataStatus.status])},[t("i",{class:S(a.dataStatus.icon)},null,2),t("span",null,o(a.dataStatus.text),1)],2)]),t("div",vs,[s[47]||(s[47]=H('<div class="spec-item" data-v-63df4d29><i class="fas fa-exchange-alt" data-v-63df4d29></i><span data-v-63df4d29>币安 + OKX 双交易所</span></div><div class="spec-item" data-v-63df4d29><i class="fas fa-coins" data-v-63df4d29></i><span data-v-63df4d29>25个主流交易对</span></div><div class="spec-item" data-v-63df4d29><i class="fas fa-clock" data-v-63df4d29></i><span data-v-63df4d29>6个时间周期 (1m-1d)</span></div>',3)),t("div",ms,[s[46]||(s[46]=t("i",{class:"fas fa-database"},null,-1)),t("span",null,o(a.dataStats.totalRecords||0)+" 条K线数据",1)])])]),a.dataStatus.isComplete?m("",!0):(i(),d("div",gs,[s[49]||(s[49]=t("div",{class:"collection-notice"},[t("i",{class:"fas fa-info-circle"}),t("div",{class:"notice-content"},[t("h5",null,"需要初始化历史数据"),t("p",null,"系统将一次性收集2024年5月-2025年5月的完整历史数据，用于所有用户的回测。此过程预计需要2-4小时。")])],-1)),t("div",ps,[t("button",{class:"init-data-btn",onClick:s[6]||(s[6]=e=>a.initializeHistoricalData(!1)),disabled:a.isCollecting},[a.isCollecting?(i(),d("i",_s)):(i(),d("i",bs)),v(" "+o(a.isCollecting?"正在收集历史数据...":"智能收集历史数据"),1)],8,ys),a.dataStatus.details&&a.dataStatus.details.completenessRatio>=30?(i(),d("button",{key:0,class:"force-init-data-btn",onClick:s[7]||(s[7]=e=>a.initializeHistoricalData(!0)),disabled:a.isCollecting},[a.isCollecting?(i(),d("i",Ds)):(i(),d("i",Ss)),v(" "+o(a.isCollecting?"正在强制收集...":"强制完整收集"),1)],8,ks)):m("",!0),t("button",{class:"check-progress-btn",onClick:s[8]||(s[8]=(...e)=>a.checkDataStatus&&a.checkDataStatus(...e)),disabled:a.isCollecting},s[48]||(s[48]=[t("i",{class:"fas fa-chart-line"},null,-1),v(" 检查数据状态 ")]),8,Ps)])])),a.isCollecting&&a.collectionProgress?(i(),d("div",Ts,[s[55]||(s[55]=t("h5",null,"📈 数据收集进度",-1)),t("div",xs,[t("div",hs,[t("div",{class:"circle-progress",style:Ut({"--progress":a.getProgressValue("monthProgress",0)})},[t("span",Cs,o(a.getProgressValue("monthProgress",0))+"%",1)],4)]),t("div",Rs,[t("div",ws,[s[50]||(s[50]=t("span",{class:"detail-label"},"当前月份",-1)),t("span",Us,o(a.getProgressValue("currentMonth","未开始")),1)]),t("div",Is,[s[51]||(s[51]=t("span",{class:"detail-label"},"已完成月份",-1)),t("span",Es,o(a.getProgressValue("completedMonths",0))+"/"+o(a.getProgressValue("totalMonths",12)),1)]),t("div",Bs,[s[52]||(s[52]=t("span",{class:"detail-label"},"已收集数据",-1)),t("span",As,o(a.formatNumber(a.getProgressValue("totalCollected",0)))+" 条",1)]),t("div",Os,[s[53]||(s[53]=t("span",{class:"detail-label"},"运行时间",-1)),t("span",Ms,o(a.getProgressValue("elapsedTimeFormatted","0分钟")),1)])])]),t("div",Vs,[t("button",{class:"stop-collection-btn",onClick:s[9]||(s[9]=(...e)=>a.stopDataCollection&&a.stopDataCollection(...e))},s[54]||(s[54]=[t("i",{class:"fas fa-stop"},null,-1),v(" 停止收集 ")]))])])):m("",!0),a.dataStatus.isComplete?(i(),d("div",Ns,[t("div",Fs,[s[57]||(s[57]=t("i",{class:"fas fa-check-circle"},null,-1)),t("div",Ks,[s[56]||(s[56]=t("h5",null,"历史数据已就绪",-1)),t("p",null,o(a.dataStatus.text),1)])]),a.dataStatus.details?(i(),d("div",Ls,[t("div",zs,[t("div",Xs,[s[58]||(s[58]=t("span",{class:"stat-label"},"数据覆盖率",-1)),t("span",Hs,o(a.dataStatus.details.completenessRatio)+"%",1)]),t("div",qs,[s[59]||(s[59]=t("span",{class:"stat-label"},"数据质量",-1)),t("span",Qs,o(a.dataStatus.details.dataQualityRatio)+"%",1)]),t("div",Gs,[s[60]||(s[60]=t("span",{class:"stat-label"},"数据组合",-1)),t("span",Js,o(a.dataStatus.details.actualCombinations)+"/"+o(a.dataStatus.details.expectedCombinations),1)]),t("div",Ws,[s[61]||(s[61]=t("span",{class:"stat-label"},"总数据点",-1)),t("span",js,o(a.formatNumber(a.dataStatus.details.totalDataPoints||0)),1)])]),a.dataStatus.details.recommendations&&a.dataStatus.details.recommendations.length>0?(i(),d("div",Ys,[s[62]||(s[62]=t("h6",null,"💡 建议",-1)),t("ul",null,[(i(!0),d(M,null,V(a.dataStatus.details.recommendations,(e,y)=>(i(),d("li",{key:y},o(e),1))),128))])])):m("",!0)])):m("",!0),t("div",Zs,[s[66]||(s[66]=t("div",{class:"summary-item"},[t("span",{class:"summary-label"},"数据时间范围"),t("span",{class:"summary-value"},"2024年5月1日 - 2025年5月1日")],-1)),t("div",$s,[s[63]||(s[63]=t("span",{class:"summary-label"},"总数据量",-1)),t("span",ta,o(a.formatNumber(a.dataStats.totalRecords||0))+" 条",1)]),t("div",sa,[s[64]||(s[64]=t("span",{class:"summary-label"},"覆盖交易对",-1)),t("span",aa,o(a.dataStats.symbolCount||0)+" 个",1)]),t("div",ea,[s[65]||(s[65]=t("span",{class:"summary-label"},"最后更新",-1)),t("span",la,o(a.dataStats.lastUpdated||"未知"),1)])]),t("div",oa,[t("div",na,[s[75]||(s[75]=t("i",{class:"fas fa-info-circle"},null,-1)),t("div",ia,[s[67]||(s[67]=t("strong",null,"数据收集说明：",-1)),s[68]||(s[68]=t("br",null,null,-1)),s[69]||(s[69]=v("• 数据源：统一使用币安(Binance)历史数据，质量优秀且完整 ")),s[70]||(s[70]=t("br",null,null,-1)),v("• 历史收集：完整的2024-2025年数据 ("+o(a.isCollecting?"进行中":"未启动")+") ",1),s[71]||(s[71]=t("br",null,null,-1)),s[72]||(s[72]=v("• 回测说明：所有交易所策略均使用币安数据进行回测 ")),s[73]||(s[73]=t("br",null,null,-1)),s[74]||(s[74]=v("• 进度显示：仅显示历史数据收集进度 "))])])])])):a.dataStatus.status!=="pending"?(i(),d("div",da,[t("div",ra,[t("i",{class:S(a.dataStatus.icon)},null,2),t("div",ca,[s[76]||(s[76]=t("h5",null,"数据状态",-1)),t("p",null,o(a.dataStatus.text),1)])]),a.dataStatus.details?(i(),d("div",ua,[t("div",fa,[t("div",va,[s[77]||(s[77]=t("span",{class:"stat-label"},"数据覆盖率",-1)),t("span",ma,o(a.dataStatus.details.completenessRatio)+"%",1)]),t("div",ga,[s[78]||(s[78]=t("span",{class:"stat-label"},"数据质量",-1)),t("span",pa,o(a.dataStatus.details.dataQualityRatio)+"%",1)]),t("div",ya,[s[79]||(s[79]=t("span",{class:"stat-label"},"数据组合",-1)),t("span",ba,o(a.dataStatus.details.actualCombinations)+"/"+o(a.dataStatus.details.expectedCombinations),1)]),t("div",_a,[s[80]||(s[80]=t("span",{class:"stat-label"},"总数据点",-1)),t("span",ka,o(a.formatNumber(a.dataStatus.details.totalDataPoints||0)),1)])]),a.dataStatus.details.recommendations&&a.dataStatus.details.recommendations.length>0?(i(),d("div",Sa,[s[81]||(s[81]=t("h6",null,"💡 建议",-1)),t("ul",null,[(i(!0),d(M,null,V(a.dataStatus.details.recommendations,(e,y)=>(i(),d("li",{key:y},o(e),1))),128))])])):m("",!0)])):m("",!0)])):m("",!0)])])):m("",!0),a.selectedStrategy&&a.selectedExchange&&a.apiKeyStatus[a.selectedExchange]?(i(),d("div",Da,[s[96]||(s[96]=t("h3",null,"⚙️ 回测参数设置",-1)),t("div",Pa,[t("div",Ta,[s[84]||(s[84]=t("label",null,"交易所",-1)),t("input",{value:a.selectedExchange==="binance"?"币安 (Binance)":"OKX",class:"setting-input",readonly:""},null,8,xa),t("div",ha,[s[83]||(s[83]=t("i",{class:"fas fa-info-circle"},null,-1)),t("span",null,o(a.selectedExchange==="okx"?"注意：OKX策略将使用币安历史数据进行回测":"使用币安历史数据进行回测"),1)])]),t("div",Ca,[s[86]||(s[86]=t("label",null,"交易对",-1)),w(t("select",{"onUpdate:modelValue":s[10]||(s[10]=e=>a.backtestParams.symbol=e),class:"setting-input"},s[85]||(s[85]=[H('<option value="BTCUSDT" data-v-63df4d29>BTC/USDT</option><option value="ETHUSDT" data-v-63df4d29>ETH/USDT</option><option value="BNBUSDT" data-v-63df4d29>BNB/USDT</option><option value="ADAUSDT" data-v-63df4d29>ADA/USDT</option><option value="XRPUSDT" data-v-63df4d29>XRP/USDT</option><option value="SOLUSDT" data-v-63df4d29>SOL/USDT</option><option value="DOGEUSDT" data-v-63df4d29>DOGE/USDT</option><option value="DOTUSDT" data-v-63df4d29>DOT/USDT</option><option value="MATICUSDT" data-v-63df4d29>MATIC/USDT</option><option value="LTCUSDT" data-v-63df4d29>LTC/USDT</option>',10)]),512),[[st,a.backtestParams.symbol]]),s[87]||(s[87]=t("div",{class:"setting-note"},[t("i",{class:"fas fa-info-circle"}),t("span",null,"所有交易对均使用币安格式，回测数据来源于币安")],-1))]),t("div",Ra,[s[89]||(s[89]=t("label",null,"时间周期",-1)),w(t("select",{"onUpdate:modelValue":s[11]||(s[11]=e=>a.backtestParams.timeframe=e),class:"setting-input"},s[88]||(s[88]=[H('<option value="1m" data-v-63df4d29>1分钟</option><option value="5m" data-v-63df4d29>5分钟</option><option value="15m" data-v-63df4d29>15分钟</option><option value="1h" data-v-63df4d29>1小时</option><option value="4h" data-v-63df4d29>4小时</option><option value="1d" data-v-63df4d29>1天</option>',6)]),512),[[st,a.backtestParams.timeframe]])]),t("div",wa,[s[90]||(s[90]=t("label",null,"回测开始时间",-1)),w(t("input",{"onUpdate:modelValue":s[12]||(s[12]=e=>a.backtestParams.startDate=e),type:"date",class:"setting-input",min:"2024-05-01",max:"2025-05-01",disabled:!a.dataStatus.isComplete},null,8,Ua),[[J,a.backtestParams.startDate]]),a.dataStatus.isComplete?(i(),d("small",Ea," 可选范围: 2024年5月1日 - 2025年5月1日 ")):(i(),d("small",Ia," 请先初始化历史数据 "))]),t("div",Ba,[s[91]||(s[91]=t("label",null,"回测结束时间",-1)),w(t("input",{"onUpdate:modelValue":s[13]||(s[13]=e=>a.backtestParams.endDate=e),type:"date",class:"setting-input",min:"2024-05-01",max:"2025-05-01",disabled:!a.dataStatus.isComplete},null,8,Aa),[[J,a.backtestParams.endDate]]),a.dataStatus.isComplete?(i(),d("small",Ma," 可选范围: 2024年5月1日 - 2025年5月1日 ")):(i(),d("small",Oa," 请先初始化历史数据 "))]),t("div",Va,[s[92]||(s[92]=t("label",null,"初始资金 (USDT)",-1)),w(t("input",{"onUpdate:modelValue":s[14]||(s[14]=e=>a.backtestParams.initialCapital=e),type:"number",class:"setting-input",placeholder:"10000"},null,512),[[J,a.backtestParams.initialCapital]])]),t("div",Na,[s[93]||(s[93]=t("label",null,"手续费率 (%)",-1)),w(t("input",{"onUpdate:modelValue":s[15]||(s[15]=e=>a.backtestParams.feeRate=e),type:"number",step:"0.01",class:"setting-input",placeholder:(a.selectedExchange==="binance","0.1")},null,8,Fa),[[J,a.backtestParams.feeRate]])])]),a.selectedStrategyData?(i(),d("div",Ka,[s[94]||(s[94]=t("h4",null,"📈 策略参数",-1)),t("div",La,[(i(!0),d(M,null,V(a.selectedStrategyData.parameters,e=>(i(),d("div",{key:e.key,class:"param-group"},[t("label",null,o(e.label),1),w(t("input",{"onUpdate:modelValue":y=>a.backtestParams.strategyParams[e.key]=y,type:e.type,step:e.step,min:e.min,max:e.max,class:"setting-input",placeholder:e.default},null,8,za),[[It,a.backtestParams.strategyParams[e.key]]]),t("small",Xa,o(e.description),1)]))),128))])])):m("",!0),t("div",Ha,[t("button",{class:"start-backtest-btn",onClick:s[16]||(s[16]=(...e)=>a.startBacktest&&a.startBacktest(...e)),disabled:a.isBacktesting||!a.selectedExchange||!a.dataStatus.isComplete},[a.isBacktesting?(i(),d("i",Ga)):(i(),d("i",Qa)),v(" "+o(a.getBacktestButtonText()),1)],8,qa),a.dataStatus.isComplete?m("",!0):(i(),d("div",Ja,s[95]||(s[95]=[t("i",{class:"fas fa-info-circle"},null,-1),t("span",null,"请先初始化历史数据后再进行回测",-1)])))])])):m("",!0),a.backtestResults?(i(),d("div",Wa,[s[114]||(s[114]=t("h3",null,"📈 回测结果",-1)),t("div",ja,[t("div",Ya,[s[98]||(s[98]=t("div",{class:"result-icon"},"💰",-1)),t("div",Za,[s[97]||(s[97]=t("div",{class:"result-label"},"总收益",-1)),t("div",{class:S(["result-value",{positive:a.backtestResults.totalReturn>0,negative:a.backtestResults.totalReturn<0}])},o(a.backtestResults.totalReturn>0?"+":"")+o(a.backtestResults.totalReturn.toFixed(2))+"% ",3)])]),t("div",$a,[s[100]||(s[100]=t("div",{class:"result-icon"},"🎯",-1)),t("div",te,[s[99]||(s[99]=t("div",{class:"result-label"},"胜率",-1)),t("div",se,o(a.backtestResults.winRate.toFixed(1))+"%",1)])]),t("div",ae,[s[102]||(s[102]=t("div",{class:"result-icon"},"📊",-1)),t("div",ee,[s[101]||(s[101]=t("div",{class:"result-label"},"夏普比率",-1)),t("div",le,o(a.backtestResults.sharpeRatio.toFixed(2)),1)])]),t("div",oe,[s[104]||(s[104]=t("div",{class:"result-icon"},"📉",-1)),t("div",ne,[s[103]||(s[103]=t("div",{class:"result-label"},"最大回撤",-1)),t("div",ie,o(a.backtestResults.maxDrawdown.toFixed(2))+"%",1)])])]),t("div",de,[s[111]||(s[111]=t("h4",null,"📋 详细统计",-1)),t("div",re,[t("div",ce,[s[105]||(s[105]=t("span",{class:"stat-label"},"交易次数",-1)),t("span",ue,o(a.backtestResults.totalTrades),1)]),t("div",fe,[s[106]||(s[106]=t("span",{class:"stat-label"},"盈利交易",-1)),t("span",ve,o(a.backtestResults.winningTrades),1)]),t("div",me,[s[107]||(s[107]=t("span",{class:"stat-label"},"亏损交易",-1)),t("span",ge,o(a.backtestResults.losingTrades),1)]),t("div",pe,[s[108]||(s[108]=t("span",{class:"stat-label"},"平均盈利",-1)),t("span",ye,o(a.backtestResults.avgWin.toFixed(2))+"%",1)]),t("div",be,[s[109]||(s[109]=t("span",{class:"stat-label"},"平均亏损",-1)),t("span",_e,o(a.backtestResults.avgLoss.toFixed(2))+"%",1)]),t("div",ke,[s[110]||(s[110]=t("span",{class:"stat-label"},"盈亏比",-1)),t("span",Se,o(a.backtestResults.profitLossRatio.toFixed(2)),1)])])]),t("div",De,[t("button",{class:"action-btn secondary",onClick:s[17]||(s[17]=(...e)=>a.viewTradeRecords&&a.viewTradeRecords(...e))},s[112]||(s[112]=[t("i",{class:"fas fa-list-alt"},null,-1),v(" 查询回测交易记录 ")])),t("button",{class:"action-btn primary",onClick:s[18]||(s[18]=(...e)=>a.applyStrategy&&a.applyStrategy(...e))},s[113]||(s[113]=[t("i",{class:"fas fa-rocket"},null,-1),v(" 应用策略 ")]))])])):m("",!0),a.showTradeRecords&&a.tradeRecords?(i(),d("div",Pe,[s[125]||(s[125]=t("h3",null,"📋 回测交易记录",-1)),t("div",Te,[t("div",xe,[s[116]||(s[116]=t("div",{class:"summary-icon"},"📊",-1)),t("div",he,[s[115]||(s[115]=t("div",{class:"summary-label"},"总交易次数",-1)),t("div",Ce,o(a.tradeRecords.length),1)])]),t("div",Re,[s[118]||(s[118]=t("div",{class:"summary-icon"},"💰",-1)),t("div",we,[s[117]||(s[117]=t("div",{class:"summary-label"},"盈利交易",-1)),t("div",Ue,o(a.tradeRecords.filter(e=>e.profit>0).length),1)])]),t("div",Ie,[s[120]||(s[120]=t("div",{class:"summary-icon"},"📉",-1)),t("div",Ee,[s[119]||(s[119]=t("div",{class:"summary-label"},"亏损交易",-1)),t("div",Be,o(a.tradeRecords.filter(e=>e.profit<0).length),1)])]),t("div",Ae,[s[122]||(s[122]=t("div",{class:"summary-icon"},"⚖️",-1)),t("div",Oe,[s[121]||(s[121]=t("div",{class:"summary-label"},"平手交易",-1)),t("div",Me,o(a.tradeRecords.filter(e=>e.profit===0).length),1)])])]),t("div",Ve,[t("div",Ne,[t("span",null,"共 "+o(a.tradeRecords.length)+" 笔交易",1),a.tradeRecords.length>a.recordsPerPage?(i(),d("span",Fe,"，显示第 "+o((a.currentPage-1)*a.recordsPerPage+1)+" - "+o(Math.min(a.currentPage*a.recordsPerPage,a.tradeRecords.length))+" 笔",1)):m("",!0)]),a.totalPages>1?(i(),d("div",Ke,[t("button",{onClick:s[19]||(s[19]=e=>a.currentPage=Math.max(1,a.currentPage-1)),disabled:a.currentPage===1,class:"page-btn"},"上一页",8,Le),t("span",ze,o(a.currentPage)+" / "+o(a.totalPages),1),t("button",{onClick:s[20]||(s[20]=e=>a.currentPage=Math.min(a.totalPages,a.currentPage+1)),disabled:a.currentPage===a.totalPages,class:"page-btn"},"下一页",8,Xe)])):m("",!0)]),t("div",He,[t("table",qe,[s[123]||(s[123]=t("thead",null,[t("tr",null,[t("th",null,"序号"),t("th",null,"交易类型"),t("th",null,"开仓时间"),t("th",null,"平仓时间"),t("th",null,"开仓价格"),t("th",null,"平仓价格"),t("th",null,"数量"),t("th",null,"盈亏金额"),t("th",null,"盈亏比例"),t("th",null,"持仓时长")])],-1)),t("tbody",null,[(i(!0),d(M,null,V(a.paginatedRecords,(e,y)=>(i(),d("tr",{key:y,class:S({"profit-row":e.profit>0,"loss-row":e.profit<0,"open-position":e.isOpen})},[t("td",null,o(e.index),1),t("td",null,[t("span",{class:S(["trade-type",e.side])},[v(o(e.side==="buy"?"做多":"做空")+" ",1),e.buyTrades>1?(i(),d("span",Qe,"+"+o(e.buyTrades-1),1)):m("",!0)],2)]),t("td",null,o(a.formatDateTime(e.entryTime)),1),t("td",null,[e.isOpen?(i(),d("span",Ge,"持仓中")):(i(),d("span",Je,o(a.formatDateTime(e.exitTime)),1))]),t("td",We,o(a.formatPrice(e.entryPrice)),1),t("td",je,[e.isOpen?(i(),d("span",Ye,o(a.formatPrice(e.exitPrice)),1)):(i(),d("span",Ze,o(a.formatPrice(e.exitPrice)),1))]),t("td",$e,o(a.formatQuantity(e.quantity)),1),t("td",{class:S(["profit",{positive:e.profit>0,negative:e.profit<0,unrealized:e.isOpen}])},[e.isOpen?(i(),d("span",tl,"("+o(e.profit>0?"+":"")+o(a.formatPrice(e.profit))+")",1)):(i(),d("span",sl,o(e.profit>0?"+":"")+o(a.formatPrice(e.profit)),1))],2),t("td",{class:S(["profit-percent",{positive:e.profitPercent>0,negative:e.profitPercent<0,unrealized:e.isOpen}])},[e.isOpen?(i(),d("span",al,"("+o(e.profitPercent>0?"+":"")+o(e.profitPercent.toFixed(2))+"%)",1)):(i(),d("span",el,o(e.profitPercent>0?"+":"")+o(e.profitPercent.toFixed(2))+"%",1))],2),t("td",ll,o(a.formatDuration(e.duration)),1)],2))),128))])])]),t("div",ol,[t("button",{class:"action-btn secondary",onClick:s[21]||(s[21]=(...e)=>a.closeTradeRecords&&a.closeTradeRecords(...e))},s[124]||(s[124]=[t("i",{class:"fas fa-times"},null,-1),v(" 关闭记录 ")]))])])):m("",!0),s[127]||(s[127]=H('<div class="risk-warning" data-v-63df4d29><h3 data-v-63df4d29>⚠️ 重要提醒</h3><div class="warning-content" data-v-63df4d29><div class="warning-item" data-v-63df4d29><i class="fas fa-exclamation-triangle" data-v-63df4d29></i><span data-v-63df4d29>回测结果基于历史数据，不代表未来表现</span></div><div class="warning-item" data-v-63df4d29><i class="fas fa-chart-line" data-v-63df4d29></i><span data-v-63df4d29>实盘交易可能面临滑点、手续费等额外成本</span></div><div class="warning-item" data-v-63df4d29><i class="fas fa-shield-alt" data-v-63df4d29></i><span data-v-63df4d29>建议先小资金测试，确认策略有效性后再加大投入</span></div></div></div>',1))])])}const rl=Ct(Et,[["render",nl],["__scopeId","data-v-63df4d29"]]);export{rl as default};
