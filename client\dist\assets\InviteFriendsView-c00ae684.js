import{_ as R,u as z,l as y,r as f,o as M,c as o,d,e as t,g as k,t as l,i as h,C as U,F as b,p as C,n as q,f as J,E as O,k as D}from"./index-a2cd3c28.js";const P={name:"InviteFriendsView",setup(){const I=z(),s=y(()=>{const i=localStorage.getItem("user");return i?JSON.parse(i):null}),w=()=>{I.go(-1)},e=y(()=>{var m;if(!((m=s.value)!=null&&m.uid))return"AC000000";const i=s.value.uid;let n=0;for(let v=0;v<i.length;v++){const u=i.charCodeAt(v);n=(n<<5)-n+u,n=n&n}return`AC${(Math.abs(n)%1e6).toString().padStart(6,"0")}`}),g=y(()=>`https://aacoin.app/register?invite=${e.value}`),p=f({totalInvited:0,activatedInvites:0}),a=f(!1),c=f(""),_=f([]),F=f([{title:"复制邀请码或链接",description:"点击复制按钮获取您的专属邀请码或邀请链接"},{title:"分享给好友",description:"通过微信、QQ、Telegram等方式分享给您的好友"},{title:"好友注册激活",description:"好友使用邀请码注册并完成首次交易即可激活"},{title:"获得奖励",description:"立即获得注册奖励，后续还有交易分成和活跃奖励"}]),x=f([{id:1,question:"邀请奖励什么时候到账？",answer:"注册奖励会在好友完成注册后立即到账，交易分成会在每日结算后到账。",expanded:!1},{id:2,question:"邀请人数有限制吗？",answer:"没有限制，您可以邀请无限多的好友，邀请越多收益越高。",expanded:!1},{id:3,question:"如何查看我的邀请收益？",answer:'您可以在本页面查看邀请统计，也可以在"我的收益"页面查看详细记录。',expanded:!1}]),L=async()=>{try{await navigator.clipboard.writeText(e.value),alert("邀请码已复制到剪贴板")}catch{const n=document.createElement("textarea");n.value=e.value,document.body.appendChild(n),n.select(),document.execCommand("copy"),document.body.removeChild(n),alert("邀请码已复制到剪贴板")}},A=async()=>{try{await navigator.clipboard.writeText(g.value),alert("邀请链接已复制到剪贴板")}catch{const n=document.createElement("textarea");n.value=g.value,document.body.appendChild(n),n.select(),document.execCommand("copy"),document.body.removeChild(n),alert("邀请链接已复制到剪贴板")}},T=()=>{alert("二维码下载功能开发中...")},V=()=>{alert("联系客服功能暂时禁用，请稍后再试")},B=async()=>{var i,n,r,m;try{if(a.value=!0,c.value="",!localStorage.getItem("token")){c.value="请先登录";return}console.log("发送邀请统计请求...");const u=await D.get("/invite/stats");console.log("邀请统计响应:",u.data),u.data.success&&(p.value={totalInvited:u.data.data.totalInvited||0,activatedInvites:u.data.data.activatedInvites||0})}catch(v){console.error("获取邀请统计失败:",v),((i=v.response)==null?void 0:i.status)===401?c.value="认证失败，请重新登录":((n=v.response)==null?void 0:n.status)===404?c.value="邀请功能暂不可用":c.value="获取邀请统计失败: "+(((m=(r=v.response)==null?void 0:r.data)==null?void 0:m.error)||v.message),p.value={totalInvited:0,activatedInvites:0}}finally{a.value=!1}},N=async()=>{try{if(!localStorage.getItem("token")){console.log("未登录，跳过获取好友列表");return}console.log("发送邀请好友列表请求...");const n=await D.get("/invite/friends");console.log("邀请好友列表响应:",n.data),n.data.success&&(_.value=n.data.data.map(r=>({id:r.id,username:r.username,registerDate:r.registerDate,status:r.isActive?"active":"inactive",statusText:r.isActive?"已激活":"未激活",earnings:r.earnings||0})))}catch(i){console.error("获取邀请好友列表失败:",i),_.value=[]}},S=async()=>{await Promise.all([B(),N()])},Q=i=>{const n=x.value.find(r=>r.id===i);n&&(n.expanded=!n.expanded)},E=i=>new Date(i).toLocaleDateString("zh-CN");return M(()=>{S()}),{goBack:w,userInviteCode:e,inviteLink:g,inviteStats:p,invitedFriends:_,tutorialSteps:F,faqList:x,loading:a,error:c,copyInviteCode:L,copyInviteLink:A,downloadQRCode:T,contactCustomerService:V,refreshData:S,toggleFaq:Q,formatDate:E}}},j={class:"invite-friends-view"},G={class:"header"},H={class:"header-content"},K={class:"main-content"},W={class:"stats-section"},X={class:"stats-card"},Y={class:"stats-header"},Z={key:0,class:"loading-state"},$={key:1,class:"error-state"},tt={key:2,class:"stats-grid"},st={class:"stat-item"},et={class:"stat-number"},at={class:"stat-item"},nt={class:"stat-number"},it={class:"invite-section"},ot={class:"invite-card"},dt={class:"invite-code-container"},lt={class:"invite-code"},rt={class:"invite-link-container"},ct={class:"invite-link"},vt={class:"qr-code-section"},ut={class:"qr-code-container"},ft={class:"friends-section"},mt={class:"friends-card"},gt={key:0,class:"friends-list"},pt={class:"friend-info"},_t={class:"friend-name"},yt={class:"friend-date"},kt={class:"friend-earnings"},ht={key:1,class:"empty-friends"},bt={key:2,class:"loading-friends"},Ct={class:"tutorial-section"},It={class:"tutorial-card"},wt={class:"tutorial-steps"},xt={class:"step-number"},St={class:"step-content"},qt={class:"faq-section"},Dt={class:"faq-card"},Ft={class:"faq-list"},Lt=["onClick"],At={class:"faq-question"};function Tt(I,s,w,e,g,p){return o(),d("div",j,[t("header",G,[t("div",H,[t("button",{class:"back-button",onClick:s[0]||(s[0]=(...a)=>e.goBack&&e.goBack(...a))},s[6]||(s[6]=[t("i",{class:"fas fa-arrow-left"},null,-1)])),s[7]||(s[7]=t("h1",null,"邀请好友",-1))])]),t("main",K,[t("div",W,[t("div",X,[t("div",Y,[s[9]||(s[9]=t("h2",null,"👥 邀请统计",-1)),e.loading?k("",!0):(o(),d("button",{key:0,onClick:s[1]||(s[1]=(...a)=>e.refreshData&&e.refreshData(...a)),class:"refresh-button",title:"刷新数据"},s[8]||(s[8]=[t("i",{class:"fas fa-sync-alt"},null,-1)])))]),e.loading?(o(),d("div",Z,s[10]||(s[10]=[t("i",{class:"fas fa-spinner fa-spin"},null,-1),t("span",null,"加载中...",-1)]))):e.error?(o(),d("div",$,[s[11]||(s[11]=t("i",{class:"fas fa-exclamation-triangle"},null,-1)),t("span",null,l(e.error),1),t("button",{onClick:s[2]||(s[2]=(...a)=>e.refreshData&&e.refreshData(...a)),class:"retry-button"},"重试")])):(o(),d("div",tt,[t("div",st,[t("div",et,l(e.inviteStats.totalInvited),1),s[12]||(s[12]=t("div",{class:"stat-label"},"已邀请人数",-1))]),t("div",at,[t("div",nt,l(e.inviteStats.activatedInvites),1),s[13]||(s[13]=t("div",{class:"stat-label"},"邀请码激活数量",-1))])]))])]),t("div",it,[t("div",ot,[s[19]||(s[19]=t("h3",null,"📱 我的邀请码",-1)),t("div",dt,[t("div",lt,l(e.userInviteCode),1),t("button",{class:"copy-button",onClick:s[3]||(s[3]=(...a)=>e.copyInviteCode&&e.copyInviteCode(...a))},s[14]||(s[14]=[t("i",{class:"fas fa-copy"},null,-1),h(" 复制 ")]))]),s[20]||(s[20]=t("h3",null,"🔗 邀请链接",-1)),t("div",rt,[t("div",ct,l(e.inviteLink),1),t("button",{class:"copy-button",onClick:s[4]||(s[4]=(...a)=>e.copyInviteLink&&e.copyInviteLink(...a))},s[15]||(s[15]=[t("i",{class:"fas fa-copy"},null,-1),h(" 复制 ")]))]),t("div",vt,[s[18]||(s[18]=t("h3",null,"📲 扫码邀请",-1)),t("div",ut,[s[17]||(s[17]=t("div",{class:"qr-code-placeholder"},[t("i",{class:"fas fa-qrcode"}),t("p",null,"二维码"),t("small",null,"扫描分享给好友")],-1)),t("button",{class:"download-qr-button",onClick:s[5]||(s[5]=(...a)=>e.downloadQRCode&&e.downloadQRCode(...a))},s[16]||(s[16]=[t("i",{class:"fas fa-download"},null,-1),h(" 下载二维码 ")]))])])])]),s[27]||(s[27]=U('<div class="contact-section" data-v-305fdc52><div class="contact-card" data-v-305fdc52><h3 data-v-305fdc52>💬 邀请奖励详情</h3><div class="contact-content" data-v-305fdc52><div class="contact-icon" data-v-305fdc52><i class="fas fa-headset" data-v-305fdc52></i></div><div class="contact-info" data-v-305fdc52><h4 data-v-305fdc52>联系客服了解详情</h4><p data-v-305fdc52>想了解邀请奖励规则和收益详情？请联系我们的客服团队，我们将为您详细介绍邀请奖励政策。</p><button class="contact-button disabled" disabled data-v-305fdc52><i class="fas fa-comments" data-v-305fdc52></i> 联系客服（暂时禁用） </button></div></div></div></div>',1)),t("div",ft,[t("div",mt,[s[24]||(s[24]=t("h3",null,"👥 我的邀请好友",-1)),!e.loading&&e.invitedFriends.length>0?(o(),d("div",gt,[(o(!0),d(b,null,C(e.invitedFriends,a=>(o(),d("div",{class:"friend-item",key:a.id},[s[21]||(s[21]=t("div",{class:"friend-avatar"},[t("i",{class:"fas fa-user"})],-1)),t("div",pt,[t("div",_t,l(a.username),1),t("div",yt,"注册时间："+l(e.formatDate(a.registerDate)),1)]),t("div",{class:q(["friend-status",a.status])},l(a.statusText),3),t("div",kt," +"+l(a.earnings)+" USDT ",1)]))),128))])):!e.loading&&e.invitedFriends.length===0?(o(),d("div",ht,s[22]||(s[22]=[t("i",{class:"fas fa-user-friends"},null,-1),t("p",null,"还没有邀请好友",-1),t("small",null,"快去邀请好友获得奖励吧！",-1)]))):k("",!0),e.loading?(o(),d("div",bt,s[23]||(s[23]=[t("i",{class:"fas fa-spinner fa-spin"},null,-1),t("span",null,"加载好友列表中...",-1)]))):k("",!0)])]),t("div",Ct,[t("div",It,[s[25]||(s[25]=t("h3",null,"📖 邀请教程",-1)),t("div",wt,[(o(!0),d(b,null,C(e.tutorialSteps,(a,c)=>(o(),d("div",{class:"tutorial-step",key:c},[t("div",xt,l(c+1),1),t("div",St,[t("h4",null,l(a.title),1),t("p",null,l(a.description),1)])]))),128))])])]),t("div",qt,[t("div",Dt,[s[26]||(s[26]=t("h3",null,"❓ 常见问题",-1)),t("div",Ft,[(o(!0),d(b,null,C(e.faqList,a=>(o(),d("div",{class:"faq-item",key:a.id,onClick:c=>e.toggleFaq(a.id)},[t("div",At,[t("span",null,l(a.question),1),t("i",{class:q(["fas fa-chevron-down",{rotated:a.expanded}])},null,2)]),J(t("div",{class:"faq-answer"},l(a.answer),513),[[O,a.expanded]])],8,Lt))),128))])])])])])}const Bt=R(P,[["render",Tt],["__scopeId","data-v-305fdc52"]]);export{Bt as default};
