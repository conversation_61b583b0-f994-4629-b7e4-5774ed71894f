const axios = require('axios');

async function testApiResponse() {
  try {
    console.log('🧪 测试API响应...');
    
    // 测试API端点
    const response = await axios.get('http://localhost:3009/api/test-strategies?userId=default');
    
    console.log('📊 API响应状态:', response.status);
    console.log('📋 响应数据:');
    console.log('  success:', response.data.success);
    console.log('  strategies数量:', response.data.strategies ? response.data.strategies.length : 0);
    console.log('  userId:', response.data.userId);
    
    if (response.data.strategies && response.data.strategies.length > 0) {
      console.log('\n🚀 运行中的策略:');
      const runningStrategies = response.data.strategies.filter(s => 
        s.status === 'waiting' || s.status === 'active'
      );
      
      console.log(`找到 ${runningStrategies.length} 个运行中的策略:`);
      runningStrategies.slice(0, 5).forEach((strategy, index) => {
        console.log(`  ${index + 1}. ${strategy.strategyName || '未命名'} (${strategy.status})`);
        console.log(`     交易对: ${strategy.symbol}, 类型: ${strategy.type}`);
        console.log(`     金额: ${strategy.amount} USDT`);
      });
      
      console.log('\n✅ 前端应该能正确显示这些策略了！');
    } else {
      console.log('\n❌ 没有找到策略数据');
    }
    
  } catch (error) {
    console.error('❌ API测试失败:', error.message);
    if (error.response) {
      console.error('错误状态:', error.response.status);
      console.error('错误数据:', error.response.data);
    }
  }
}

testApiResponse();
