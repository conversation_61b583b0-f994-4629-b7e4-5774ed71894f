import{_ as P,u as E,r as d,l as L,o as N,c as l,d as a,e,F as I,p as h,t as i,w as C,f as r,v as m,B as q,j as M,i as _,g as b,k as p,n as A}from"./index-a2cd3c28.js";const z={name:"ContentManagementView",setup(){const w=E(),t=d(!1),y=d([]),o=d(!1),v=d(!1),u=d(null),n=d({title:"",content:"",type:"info",isActive:!0,isPinned:!1,priority:0,startTime:"",endTime:""}),f=L(()=>!!u.value),c=async()=>{try{t.value=!0;const s=await p.get("/announcements/admin");s.data.success&&(y.value=s.data.data.announcements)}catch(s){console.error("获取公告列表失败:",s),alert("获取公告列表失败")}finally{t.value=!1}},T=async()=>{try{t.value=!0,(await p.post("/announcements",n.value)).data.success&&(alert("公告创建成功"),await c(),g())}catch(s){console.error("创建公告失败:",s),alert("创建公告失败")}finally{t.value=!1}},x=async()=>{try{t.value=!0,(await p.put(`/announcements/${u.value._id}`,n.value)).data.success&&(alert("公告更新成功"),await c(),g())}catch(s){console.error("更新公告失败:",s),alert("更新公告失败")}finally{t.value=!1}},F=async s=>{if(confirm("确定要删除这个公告吗？"))try{t.value=!0,(await p.delete(`/announcements/${s}`)).data.success&&(alert("公告删除成功"),await c())}catch(k){console.error("删除公告失败:",k),alert("删除公告失败")}finally{t.value=!1}},V=s=>{u.value=s,n.value={title:s.title,content:s.content,type:s.type,isActive:s.isActive,isPinned:s.isPinned,priority:s.priority,startTime:s.startTime?new Date(s.startTime).toISOString().slice(0,16):"",endTime:s.endTime?new Date(s.endTime).toISOString().slice(0,16):""},v.value=!0},D=()=>{f.value?x():T()},g=()=>{o.value=!1,v.value=!1,u.value=null,n.value={title:"",content:"",type:"info",isActive:!0,isPinned:!1,priority:0,startTime:"",endTime:""}},S=s=>({info:"信息",warning:"警告",success:"成功",error:"错误"})[s]||"信息",U=s=>new Date(s).toLocaleString("zh-CN"),B=()=>{w.go(-1)};return N(()=>{c()}),{loading:t,announcements:y,showCreateModal:o,showEditModal:v,announcementForm:n,isEditing:f,fetchAnnouncements:c,editAnnouncement:V,deleteAnnouncement:F,submitAnnouncement:D,closeModal:g,getTypeLabel:S,formatDate:U,goBack:B}}},O={class:"content-management-view"},j={class:"header"},R={class:"header-content"},G={class:"main-content"},H={key:0,class:"announcement-list"},J={class:"announcement-header"},K={class:"announcement-title"},Q={class:"title-text"},W={class:"announcement-badges"},X={key:0,class:"badge badge-pinned"},Y={class:"announcement-actions"},Z=["onClick"],$=["onClick"],tt={class:"announcement-content"},et={class:"announcement-meta"},nt={key:0},ot={key:1,class:"empty-state"},st={class:"modal-header"},lt={class:"modal-body"},at={class:"form-group"},it={class:"form-group"},rt={class:"form-row"},dt={class:"form-group"},ut={class:"form-group"},ct={class:"form-row"},mt={class:"form-group checkbox-group"},vt={class:"checkbox-label"},ft={class:"form-group checkbox-group"},bt={class:"checkbox-label"},pt={class:"form-row"},yt={class:"form-group"},gt={class:"form-group"},kt={class:"form-actions"},wt={type:"submit",class:"submit-button"},Ct={key:1,class:"loading-overlay"};function Mt(w,t,y,o,v,u){return l(),a("div",O,[e("header",j,[e("div",R,[e("button",{class:"back-button",onClick:t[0]||(t[0]=(...n)=>o.goBack&&o.goBack(...n))},t[16]||(t[16]=[e("i",{class:"fas fa-arrow-left"},null,-1)])),t[18]||(t[18]=e("h1",null,"内容管理",-1)),e("button",{class:"add-button",onClick:t[1]||(t[1]=n=>o.showCreateModal=!0)},t[17]||(t[17]=[e("i",{class:"fas fa-plus"},null,-1)]))])]),e("main",G,[o.announcements.length>0?(l(),a("div",H,[(l(!0),a(I,null,h(o.announcements,n=>(l(),a("div",{class:"announcement-card",key:n._id},[e("div",J,[e("div",K,[e("span",Q,i(n.title),1),e("div",W,[n.isPinned?(l(),a("span",X,"置顶")):b("",!0),e("span",{class:A(["badge",`badge-${n.type}`])},i(o.getTypeLabel(n.type)),3),e("span",{class:A(["badge",n.isActive?"badge-active":"badge-inactive"])},i(n.isActive?"启用":"禁用"),3)])]),e("div",Y,[e("button",{onClick:f=>o.editAnnouncement(n),class:"action-button edit"},t[19]||(t[19]=[e("i",{class:"fas fa-edit"},null,-1)]),8,Z),e("button",{onClick:f=>o.deleteAnnouncement(n._id),class:"action-button delete"},t[20]||(t[20]=[e("i",{class:"fas fa-trash"},null,-1)]),8,$)])]),e("div",tt,i(n.content),1),e("div",et,[e("span",null,"优先级: "+i(n.priority),1),e("span",null,"创建时间: "+i(o.formatDate(n.createdAt)),1),n.endTime?(l(),a("span",nt,"结束时间: "+i(o.formatDate(n.endTime)),1)):b("",!0)])]))),128))])):(l(),a("div",ot,[t[21]||(t[21]=e("i",{class:"fas fa-bullhorn"},null,-1)),t[22]||(t[22]=e("p",null,"暂无公告",-1)),e("button",{class:"create-first-button",onClick:t[2]||(t[2]=n=>o.showCreateModal=!0)}," 创建第一个公告 ")]))]),o.showCreateModal||o.showEditModal?(l(),a("div",{key:0,class:"modal-overlay",onClick:t[15]||(t[15]=(...n)=>o.closeModal&&o.closeModal(...n))},[e("div",{class:"modal-content",onClick:t[14]||(t[14]=C(()=>{},["stop"]))},[e("div",st,[e("h3",null,i(o.isEditing?"编辑公告":"创建公告"),1),e("button",{class:"close-button",onClick:t[3]||(t[3]=(...n)=>o.closeModal&&o.closeModal(...n))},t[23]||(t[23]=[e("i",{class:"fas fa-times"},null,-1)]))]),e("div",lt,[e("form",{onSubmit:t[13]||(t[13]=C((...n)=>o.submitAnnouncement&&o.submitAnnouncement(...n),["prevent"]))},[e("div",at,[t[24]||(t[24]=e("label",null,"标题",-1)),r(e("input",{"onUpdate:modelValue":t[4]||(t[4]=n=>o.announcementForm.title=n),type:"text",placeholder:"请输入公告标题",maxlength:"100",required:""},null,512),[[m,o.announcementForm.title]])]),e("div",it,[t[25]||(t[25]=e("label",null,"内容",-1)),r(e("textarea",{"onUpdate:modelValue":t[5]||(t[5]=n=>o.announcementForm.content=n),placeholder:"请输入公告内容",maxlength:"1000",rows:"4",required:""},null,512),[[m,o.announcementForm.content]])]),e("div",rt,[e("div",dt,[t[27]||(t[27]=e("label",null,"类型",-1)),r(e("select",{"onUpdate:modelValue":t[6]||(t[6]=n=>o.announcementForm.type=n)},t[26]||(t[26]=[e("option",{value:"info"},"信息",-1),e("option",{value:"warning"},"警告",-1),e("option",{value:"success"},"成功",-1),e("option",{value:"error"},"错误",-1)]),512),[[q,o.announcementForm.type]])]),e("div",ut,[t[28]||(t[28]=e("label",null,"优先级",-1)),r(e("input",{"onUpdate:modelValue":t[7]||(t[7]=n=>o.announcementForm.priority=n),type:"number",min:"0",max:"100",placeholder:"0-100"},null,512),[[m,o.announcementForm.priority,void 0,{number:!0}]])])]),e("div",ct,[e("div",mt,[e("label",vt,[r(e("input",{"onUpdate:modelValue":t[8]||(t[8]=n=>o.announcementForm.isActive=n),type:"checkbox"},null,512),[[M,o.announcementForm.isActive]]),t[29]||(t[29]=e("span",{class:"checkmark"},null,-1)),t[30]||(t[30]=_(" 启用公告 "))])]),e("div",ft,[e("label",bt,[r(e("input",{"onUpdate:modelValue":t[9]||(t[9]=n=>o.announcementForm.isPinned=n),type:"checkbox"},null,512),[[M,o.announcementForm.isPinned]]),t[31]||(t[31]=e("span",{class:"checkmark"},null,-1)),t[32]||(t[32]=_(" 置顶显示 "))])])]),e("div",pt,[e("div",yt,[t[33]||(t[33]=e("label",null,"开始时间",-1)),r(e("input",{"onUpdate:modelValue":t[10]||(t[10]=n=>o.announcementForm.startTime=n),type:"datetime-local"},null,512),[[m,o.announcementForm.startTime]])]),e("div",gt,[t[34]||(t[34]=e("label",null,"结束时间（可选）",-1)),r(e("input",{"onUpdate:modelValue":t[11]||(t[11]=n=>o.announcementForm.endTime=n),type:"datetime-local"},null,512),[[m,o.announcementForm.endTime]])])]),e("div",kt,[e("button",{type:"button",class:"cancel-button",onClick:t[12]||(t[12]=(...n)=>o.closeModal&&o.closeModal(...n))},"取消"),e("button",wt,i(o.isEditing?"更新":"创建"),1)])],32)])])])):b("",!0),o.loading?(l(),a("div",Ct,t[35]||(t[35]=[e("div",{class:"loading-spinner"},null,-1)]))):b("",!0)])}const At=P(z,[["render",Mt],["__scopeId","data-v-d7b68ada"]]);export{At as default};
