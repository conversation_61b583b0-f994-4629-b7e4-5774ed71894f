import{_ as F,u as G,r as T,o as H,m as j,c as r,d as m,e as s,i as N,t as d,n as v,g as b,F as q,p as V,w as K,H as B,q as Q}from"./index-a2cd3c28.js";const W={name:"UserView",setup(){const l=G(),e=T(null);let c=null;const a=T(!1),y=async()=>{const i=localStorage.getItem("user");e.value=i?JSON.parse(i):null,await f()},g=T([{label:"联系客服",icon:"fas fa-headset",action:"contact",disabled:!0},{label:"修改密码",icon:"fas fa-lock",action:"change-password"},{label:"剩余天数",icon:"fas fa-coins",action:"days",value:"0"},{label:"退出登录",icon:"fas fa-sign-out-alt",action:"logout"}]),f=async()=>{if(e.value)try{const i=localStorage.getItem("token");if(i){const o=await B.get("/api/membership/info",{headers:{Authorization:`Bearer ${i}`}});if(o.data.success){const t=o.data.data,u=t.membershipDaysLeft||0,_=g.value.find(P=>P.action==="days");_&&(u===-1?_.value="永久":_.value=u.toString()),e.value&&(e.value.membershipLevel=t.membershipLevel,e.value.membershipExpiry=t.membershipExpiry,e.value.membershipStartDate=t.membershipStartDate,e.value.membershipDaysLeft=t.membershipDaysLeft,e.value.isValidMember=t.isValidMember,localStorage.setItem("user",JSON.stringify(e.value)))}}else{const o=e.value.membershipDaysLeft||0,t=g.value.find(u=>u.action==="days");t&&(o===-1?t.value="永久":t.value=o.toString())}}catch(i){console.error("获取会员信息失败:",i);const o=e.value.membershipDaysLeft||0,t=g.value.find(u=>u.action==="days");t&&(o===-1?t.value="永久":t.value=o.toString())}},M=T([{title:"常见问题",icon:"fas fa-question-circle",color:"#9c6ade",route:"faq"},{title:"策略回测",icon:"fas fa-chart-line",color:"#5cd9a6",route:"strategy-backtest"},{title:"邀请好友",icon:"fas fa-user-friends",color:"#ff9f43",route:"invite-friends"},{title:"新手指南",icon:"fas fa-lightbulb",color:"#feca57",route:"beginner-guide"}]),k=()=>{l.go(-1)},w=()=>{l.push({name:"membership"})},C=async i=>{switch(i){case"contact":alert("联系客服功能暂时不可用，请稍后再试");break;case"change-password":l.push({name:"change-password"});break;case"logout":localStorage.removeItem("token"),localStorage.removeItem("user"),l.push({name:"login"});break;case"days":await A();break;default:alert("该功能正在开发中...")}},U=i=>{i==="invite-friends"?l.push({name:"invite-friends"}):i==="beginner-guide"?l.push({name:"beginner-guide"}):i==="faq"?l.push({name:"faq"}):i==="strategy-backtest"?l.push({name:"strategy-backtest"}):alert("该功能正在开发中...")},L=()=>{l.push({name:"ranking-management"})},S=()=>{l.push({name:"membership-management"})},D=()=>{l.push({name:"user-management"})},I=()=>{l.push("/admin/strategy-permissions")},x=()=>{l.push({name:"content-management"})},p=async()=>{try{const i=localStorage.getItem("token");if(!i){y();return}const o=await B.get("/api/auth/me",{headers:{Authorization:`Bearer ${i}`}});if(o.data.success){let t=o.data.user;t.email==="<EMAIL>"&&!t.isAdmin&&(console.log("检测到管理员邮箱，强制设置管理员权限"),t.isAdmin=!0),localStorage.setItem("user",JSON.stringify(t)),e.value=t,await f(),console.log("用户信息已刷新:",t)}}catch(i){console.error("刷新用户信息失败:",i),y()}},n=()=>{c&&clearInterval(c),c=setInterval(async()=>{console.log("定时刷新会员信息..."),await f()},5*60*1e3)},h=()=>{c&&(clearInterval(c),c=null)},A=async()=>{try{if(await f(),!e.value){alert("请先登录");return}a.value=!0}catch(i){console.error("获取会员信息失败:",i),alert("获取会员信息失败，请稍后重试")}},z=()=>{a.value=!1},E=i=>({none:"无会员",monthly:"月会员",quarterly:"季会员",yearly:"年会员",lifetime:"永久会员"})[i]||"未知",R=i=>`membership-${i}`,J=i=>i?i.membershipLevel==="none"?"无会员":i.membershipLevel==="lifetime"?"永久有效":`${i.membershipDaysLeft||0} 天`:"0",O=i=>i?new Date(i).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"}):"";return H(async()=>{var i,o;await y(),await p(),n(),((i=e.value)==null?void 0:i.email)==="<EMAIL>"&&!((o=e.value)!=null&&o.isAdmin)&&(console.log("检测到管理员邮箱但权限不正确，尝试强制刷新..."),setTimeout(async()=>{await p()},1e3))}),j(()=>{h()}),{currentUser:e,userActions:g,menuItems:M,showMembershipModal:a,goBack:k,goToMembership:w,handleAction:C,navigateTo:U,openRankingManagement:L,openMembershipManagement:S,openUserManagement:D,openStrategyPermissionControl:I,openContentManagement:x,showMembershipInfo:A,closeMembershipModal:z,getMembershipLevelName:E,getMembershipClass:R,getMembershipDaysText:J,formatDate:O}}},X={class:"user-view"},Y={class:"user-header"},Z={class:"user-profile"},$={class:"user-info"},ee={class:"username"},se={class:"user-id"},ae={class:"user-actions"},ne=["onClick"],ie={key:0,class:"disabled-text"},te={key:1},oe={class:"menu-list"},le={key:0,class:"admin-panel"},re={class:"admin-actions"},me=["onClick"],de={class:"menu-item-left"},ce={class:"menu-title"},fe={class:"modal-header"},ue={class:"modal-body"},ve={class:"membership-info"},be={class:"info-card"},ge={class:"info-item"},pe={class:"info-item"},he={class:"info-item"},ye={class:"membership-days"},Me={key:0,class:"info-item"},ke={key:1,class:"info-item"},we={class:"modal-footer"};function Ce(l,e,c,a,y,g){var f,M,k,w,C,U,L,S,D,I,x,p;return r(),m("div",X,[s("div",Y,[s("i",{class:"fas fa-arrow-left back-button",onClick:e[0]||(e[0]=(...n)=>a.goBack&&a.goBack(...n))}),e[14]||(e[14]=s("div",{class:"text-center"},"会员中心",-1)),e[15]||(e[15]=s("i",{class:"fas fa-ellipsis-v menu-button"},null,-1)),s("div",Z,[e[13]||(e[13]=s("div",{class:"avatar"},[s("i",{class:"fas fa-user"})],-1)),s("div",$,[s("div",ee,[N(d(((f=a.currentUser)==null?void 0:f.username)||"Guest")+" ",1),(M=a.currentUser)!=null&&M.membershipLevel&&a.currentUser.membershipLevel!=="none"?(r(),m("span",{key:0,class:v(["membership-badge",a.getMembershipClass(a.currentUser.membershipLevel)])},d(a.getMembershipLevelName(a.currentUser.membershipLevel)),3)):b("",!0)]),s("div",se,"UID: "+d(((k=a.currentUser)==null?void 0:k.uid)||"N/A"),1)]),s("div",{class:"upgrade-button",onClick:e[1]||(e[1]=(...n)=>a.goToMembership&&a.goToMembership(...n))},e[12]||(e[12]=[s("i",{class:"fas fa-crown"},null,-1),N(" 开通会员 ")]))]),s("div",ae,[(r(!0),m(q,null,V(a.userActions,(n,h)=>(r(),m("div",{class:v(["user-action",{disabled:n.disabled}]),key:h,onClick:A=>a.handleAction(n.action)},[s("i",{class:v(n.icon)},null,2),s("span",null,d(n.label),1),n.disabled?(r(),m("span",ie,"（暂不可用）")):n.value!==void 0?(r(),m("span",te,d(n.value),1)):b("",!0)],10,ne))),128))])]),s("div",oe,[(w=a.currentUser)!=null&&w.isAdmin?(r(),m("div",le,[e[21]||(e[21]=s("div",{class:"admin-header"},[s("i",{class:"fas fa-crown"}),s("span",null,"管理员控制面板")],-1)),s("div",re,[s("div",{class:"admin-action-item",onClick:e[2]||(e[2]=(...n)=>a.openRankingManagement&&a.openRankingManagement(...n))},e[16]||(e[16]=[s("div",{class:"admin-action-left"},[s("i",{class:"fas fa-trophy"}),s("span",null,"排行榜管理")],-1),s("i",{class:"fas fa-chevron-right"},null,-1)])),s("div",{class:"admin-action-item",onClick:e[3]||(e[3]=(...n)=>a.openMembershipManagement&&a.openMembershipManagement(...n))},e[17]||(e[17]=[s("div",{class:"admin-action-left"},[s("i",{class:"fas fa-crown"}),s("span",null,"会员管理")],-1),s("i",{class:"fas fa-chevron-right"},null,-1)])),s("div",{class:"admin-action-item",onClick:e[4]||(e[4]=(...n)=>a.openUserManagement&&a.openUserManagement(...n))},e[18]||(e[18]=[s("div",{class:"admin-action-left"},[s("i",{class:"fas fa-users"}),s("span",null,"用户管理")],-1),s("i",{class:"fas fa-chevron-right"},null,-1)])),s("div",{class:"admin-action-item",onClick:e[5]||(e[5]=(...n)=>a.openStrategyPermissionControl&&a.openStrategyPermissionControl(...n))},e[19]||(e[19]=[s("div",{class:"admin-action-left"},[s("i",{class:"fas fa-shield-alt"}),s("span",null,"策略权限控制")],-1),s("i",{class:"fas fa-chevron-right"},null,-1)])),s("div",{class:"admin-action-item",onClick:e[6]||(e[6]=(...n)=>a.openContentManagement&&a.openContentManagement(...n))},e[20]||(e[20]=[s("div",{class:"admin-action-left"},[s("i",{class:"fas fa-edit"}),s("span",null,"内容管理")],-1),s("i",{class:"fas fa-chevron-right"},null,-1)]))])])):b("",!0),(r(!0),m(q,null,V(a.menuItems,(n,h)=>(r(),m("div",{class:"menu-item",key:h,onClick:A=>a.navigateTo(n.route)},[s("div",de,[s("div",{class:"menu-icon",style:Q({backgroundColor:n.color})},[s("i",{class:v(n.icon)},null,2)],4),s("div",ce,d(n.title),1)]),e[22]||(e[22]=s("i",{class:"fas fa-chevron-right menu-arrow"},null,-1))],8,me))),128))]),a.showMembershipModal?(r(),m("div",{key:0,class:"modal-overlay",onClick:e[11]||(e[11]=(...n)=>a.closeMembershipModal&&a.closeMembershipModal(...n))},[s("div",{class:"modal-content membership-modal",onClick:e[10]||(e[10]=K(()=>{},["stop"]))},[s("div",fe,[e[24]||(e[24]=s("h3",null,[s("i",{class:"fas fa-crown"}),N(" 会员信息详情 ")],-1)),s("button",{class:"close-button",onClick:e[7]||(e[7]=(...n)=>a.closeMembershipModal&&a.closeMembershipModal(...n))},e[23]||(e[23]=[s("i",{class:"fas fa-times"},null,-1)]))]),s("div",ue,[s("div",ve,[s("div",be,[s("div",ge,[e[25]||(e[25]=s("label",null,"会员等级",-1)),s("span",{class:v(["membership-level",a.getMembershipClass((C=a.currentUser)==null?void 0:C.membershipLevel)])},d(a.getMembershipLevelName((U=a.currentUser)==null?void 0:U.membershipLevel)),3)]),s("div",pe,[e[26]||(e[26]=s("label",null,"会员状态",-1)),s("span",{class:v(["membership-status",(L=a.currentUser)!=null&&L.isValidMember?"valid":"invalid"])},d((S=a.currentUser)!=null&&S.isValidMember?"有效":"无效/已过期"),3)]),s("div",he,[e[27]||(e[27]=s("label",null,"剩余天数",-1)),s("span",ye,d(a.getMembershipDaysText(a.currentUser)),1)]),(D=a.currentUser)!=null&&D.membershipStartDate?(r(),m("div",Me,[e[28]||(e[28]=s("label",null,"开始时间",-1)),s("span",null,d(a.formatDate(a.currentUser.membershipStartDate)),1)])):b("",!0),(I=a.currentUser)!=null&&I.membershipExpiry||((x=a.currentUser)==null?void 0:x.membershipLevel)==="lifetime"?(r(),m("div",ke,[e[29]||(e[29]=s("label",null,"到期时间",-1)),s("span",null,d(((p=a.currentUser)==null?void 0:p.membershipLevel)==="lifetime"?"永不过期":a.formatDate(a.currentUser.membershipExpiry)),1)])):b("",!0)])])]),s("div",we,[s("button",{class:"close-modal-button",onClick:e[8]||(e[8]=(...n)=>a.closeMembershipModal&&a.closeMembershipModal(...n))},"关闭"),s("button",{class:"upgrade-button",onClick:e[9]||(e[9]=(...n)=>a.goToMembership&&a.goToMembership(...n))},e[30]||(e[30]=[s("i",{class:"fas fa-arrow-up"},null,-1),N(" 升级会员 ")]))])])])):b("",!0)])}const Le=F(W,[["render",Ce],["__scopeId","data-v-e045e850"]]);export{Le as default};
