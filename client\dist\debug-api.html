<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调试工具 - Sakura FRP</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { background-color: #d4edda; border-left: 4px solid #28a745; }
        .error { background-color: #f8d7da; border-left: 4px solid #dc3545; }
        .info { background-color: #d1ecf1; border-left: 4px solid #17a2b8; }
        .warning { background-color: #fff3cd; border-left: 4px solid #ffc107; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; border: 1px solid #e9ecef; }
        .status { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
        .status.online { background: #28a745; color: white; }
        .status.offline { background: #dc3545; color: white; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API调试工具 - Sakura FRP</h1>
        
        <div class="card info">
            <h3>📍 当前访问信息</h3>
            <p><strong>访问域名:</strong> <span id="hostname"></span></p>
            <p><strong>访问协议:</strong> <span id="protocol"></span></p>
            <p><strong>访问端口:</strong> <span id="port"></span></p>
            <p><strong>完整URL:</strong> <span id="fullUrl"></span></p>
            <p><strong>访问类型:</strong> <span id="accessType" class="status"></span></p>
            <p><strong>API基础URL:</strong> <span id="apiBaseUrl"></span></p>
        </div>

        <div class="card">
            <h3>🧪 连接测试</h3>
            <div class="test-grid">
                <button onclick="testHealth()">健康检查</button>
                <button onclick="testAPI()">API连接</button>
                <button onclick="testLogin()">登录测试</button>
                <button onclick="testCORS()">CORS测试</button>
                <button onclick="clearResults()">清除结果</button>
            </div>
        </div>

        <div class="card">
            <h3>📊 测试结果</h3>
            <div id="testResults"></div>
        </div>

        <div class="card">
            <h3>🔧 问题排查建议</h3>
            <div id="suggestions"></div>
        </div>
    </div>

    <script>
        // 显示当前访问信息
        const hostname = window.location.hostname;
        const protocol = window.location.protocol;
        const port = window.location.port;
        const fullUrl = window.location.href;
        
        document.getElementById('hostname').textContent = hostname;
        document.getElementById('protocol').textContent = protocol;
        document.getElementById('port').textContent = port || '默认端口';
        document.getElementById('fullUrl').textContent = fullUrl;
        
        const isLocal = hostname.includes('localhost') || hostname.includes('127.0.0.1');
        const accessTypeEl = document.getElementById('accessType');
        accessTypeEl.textContent = isLocal ? '本地访问' : 'Sakura FRP公网访问';
        accessTypeEl.className = `status ${isLocal ? 'offline' : 'online'}`;
        
        // 确定API URL
        const API_BASE = isLocal ? 'http://localhost:3009' : '';
        document.getElementById('apiBaseUrl').textContent = API_BASE || '相对路径 (通过代理)';
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `card ${type}`;
            div.innerHTML = `<p>${message}</p>`;
            document.getElementById('testResults').appendChild(div);
            
            // 自动滚动到底部
            div.scrollIntoView({ behavior: 'smooth' });
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('suggestions').innerHTML = '';
        }

        async function testHealth() {
            addResult('🔄 正在测试健康检查...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/api/health`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    addResult(`✅ 健康检查成功<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                } else {
                    addResult(`❌ 健康检查失败: ${response.status} ${response.statusText}<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'error');
                }
            } catch (error) {
                addResult(`❌ 健康检查错误: ${error.message}`, 'error');
                showSuggestions('health_error');
            }
        }

        async function testAPI() {
            addResult('🔄 正在测试API连接...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/api/prices`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ API连接成功，获取到 ${data.length} 条价格数据`, 'success');
                } else {
                    addResult(`❌ API连接失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ API连接错误: ${error.message}`, 'error');
                showSuggestions('api_error');
            }
        }

        async function testLogin() {
            addResult('🔄 正在测试登录接口...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'test',
                        password: 'test'
                    })
                });
                
                const data = await response.json();
                
                if (response.status === 401 || response.status === 400) {
                    addResult(`✅ 登录接口正常响应 (${response.status})<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                } else if (response.ok) {
                    addResult(`✅ 登录接口响应正常<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                } else {
                    addResult(`❌ 登录接口异常: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ 登录接口错误: ${error.message}`, 'error');
                showSuggestions('login_error');
            }
        }

        async function testCORS() {
            addResult('🔄 正在测试CORS配置...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/api/health`, {
                    method: 'OPTIONS'
                });
                
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
                };
                
                addResult(`✅ CORS配置检查<br><pre>${JSON.stringify(corsHeaders, null, 2)}</pre>`, 'success');
            } catch (error) {
                addResult(`❌ CORS测试错误: ${error.message}`, 'error');
                showSuggestions('cors_error');
            }
        }

        function showSuggestions(errorType) {
            const suggestions = document.getElementById('suggestions');
            let content = '';
            
            switch(errorType) {
                case 'health_error':
                case 'api_error':
                    content = `
                        <h4>🔧 API连接问题排查</h4>
                        <ol>
                            <li><strong>检查后端服务:</strong> 确认后端服务正在运行 <code>pm2 status</code></li>
                            <li><strong>检查代理配置:</strong> 确认 vite.config.js 中的代理配置正确</li>
                            <li><strong>检查端口:</strong> 确认后端运行在3009端口</li>
                            <li><strong>重启服务:</strong> 尝试重启前端和后端服务</li>
                        </ol>
                    `;
                    break;
                case 'login_error':
                    content = `
                        <h4>🔧 登录问题排查</h4>
                        <ol>
                            <li><strong>检查认证路由:</strong> 确认 /api/auth/login 路由正常</li>
                            <li><strong>检查数据库连接:</strong> 确认MongoDB连接正常</li>
                            <li><strong>检查CORS:</strong> 确认跨域配置允许认证请求</li>
                            <li><strong>检查请求格式:</strong> 确认请求头和数据格式正确</li>
                        </ol>
                    `;
                    break;
                case 'cors_error':
                    content = `
                        <h4>🔧 CORS问题排查</h4>
                        <ol>
                            <li><strong>检查服务器CORS配置:</strong> 确认server.js中的CORS设置</li>
                            <li><strong>检查域名白名单:</strong> 确认Sakura FRP域名在允许列表中</li>
                            <li><strong>重启后端:</strong> CORS配置修改后需要重启后端服务</li>
                        </ol>
                    `;
                    break;
            }
            
            suggestions.innerHTML = content;
        }

        // 页面加载时自动显示信息
        window.onload = function() {
            addResult(`🌐 检测到${isLocal ? '本地' : 'Sakura FRP公网'}访问模式`, 'info');
            addResult(`🔗 API请求将发送到: ${API_BASE || '相对路径 (通过Vite代理)'}`, 'info');

            if (!isLocal) {
                addResult(`📝 Sakura FRP配置提醒: 确保映射了5173端口到公网`, 'warning');
                addResult(`💡 如果仍然无法登录，请运行 fix-sakura-frp.bat 修复脚本`, 'warning');
            }

            // 自动测试健康检查
            setTimeout(() => {
                testHealth();
            }, 1000);
        };
    </script>
</body>
</html>
